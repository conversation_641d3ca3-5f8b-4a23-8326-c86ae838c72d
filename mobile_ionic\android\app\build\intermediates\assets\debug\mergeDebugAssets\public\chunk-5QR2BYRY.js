import{Aa as R,Ba as z,Ca as B,Fa as A,m as I,o as y,pa as u,qa as E,ra as j,sa as x,ta as w,ua as k,va as T,wa as M,xa as S,ya as F,za as D}from"./chunk-YFIZFQXH.js";import{a as c}from"./chunk-RXV5ZBOB.js";import{a as h,d as s,e as g,g as a,h as v}from"./chunk-ODNE7IRY.js";import{e as b}from"./chunk-F4H6ZFEG.js";import{a as f}from"./chunk-WTCPO44B.js";import{g as C,h as m}from"./chunk-LNJ3S2LQ.js";var P=()=>{let n;return{lock:()=>m(void 0,null,function*(){let t=n,i;return n=new Promise(o=>i=o),t!==void 0&&(yield t),i})}};var H=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",_=H,V=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",G=V,W=v(class extends h{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionBackdropTap=a(this,"ionBackdropTap",7),this.visible=!0,this.tappable=!0,this.stopPropagation=!0}onMouseDown(e){this.emitTap(e)}emitTap(e){this.stopPropagation&&(e.preventDefault(),e.stopPropagation()),this.tappable&&this.ionBackdropTap.emit()}render(){let e=u(this);return s(g,{key:"7abaf2c310aa399607451b14063265e8a5846938","aria-hidden":"true",class:{[e]:!0,"backdrop-hide":!this.visible,"backdrop-no-tappable":!this.tappable}})}static get style(){return{ios:_,md:G}}},[33,"ion-backdrop",{visible:[4],tappable:[4],stopPropagation:[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]);function $(){if(typeof customElements>"u")return;["ion-backdrop"].forEach(e=>{switch(e){case"ion-backdrop":customElements.get(e)||customElements.define(e,W);break}})}var X={bubbles:{dur:1e3,circles:9,fn:(n,e,t)=>{let i=`${n*e/t-n}ms`,o=2*Math.PI*e/t;return{r:5,style:{top:`${32*Math.sin(o)}%`,left:`${32*Math.cos(o)}%`,"animation-delay":i}}}},circles:{dur:1e3,circles:8,fn:(n,e,t)=>{let i=e/t,o=`${n*i-n}ms`,r=2*Math.PI*i;return{r:5,style:{top:`${32*Math.sin(r)}%`,left:`${32*Math.cos(r)}%`,"animation-delay":o}}}},circular:{dur:1400,elmDuration:!0,circles:1,fn:()=>({r:20,cx:48,cy:48,fill:"none",viewBox:"24 24 48 48",transform:"translate(0,0)",style:{}})},crescent:{dur:750,circles:1,fn:()=>({r:26,style:{}})},dots:{dur:750,circles:3,fn:(n,e)=>{let t=-(110*e)+"ms";return{r:6,style:{left:`${32-32*e}%`,"animation-delay":t}}}},lines:{dur:1e3,lines:8,fn:(n,e,t)=>{let i=`rotate(${360/t*e+(e<t/2?180:-180)}deg)`,o=`${n*e/t-n}ms`;return{y1:14,y2:26,style:{transform:i,"animation-delay":o}}}},"lines-small":{dur:1e3,lines:8,fn:(n,e,t)=>{let i=`rotate(${360/t*e+(e<t/2?180:-180)}deg)`,o=`${n*e/t-n}ms`;return{y1:12,y2:20,style:{transform:i,"animation-delay":o}}}},"lines-sharp":{dur:1e3,lines:12,fn:(n,e,t)=>{let i=`rotate(${30*e+(e<6?180:-180)}deg)`,o=`${n*e/t-n}ms`;return{y1:17,y2:29,style:{transform:i,"animation-delay":o}}}},"lines-sharp-small":{dur:1e3,lines:12,fn:(n,e,t)=>{let i=`rotate(${30*e+(e<6?180:-180)}deg)`,o=`${n*e/t-n}ms`;return{y1:12,y2:20,style:{transform:i,"animation-delay":o}}}}},L=X,q=":host{display:inline-block;position:relative;width:28px;height:28px;color:var(--color);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.ion-color){color:var(--ion-color-base)}svg{-webkit-transform-origin:center;transform-origin:center;position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0)}:host-context([dir=rtl]) svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){svg:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}:host(.spinner-lines) line,:host(.spinner-lines-small) line{stroke-width:7px}:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-width:4px}:host(.spinner-lines) line,:host(.spinner-lines-small) line,:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-linecap:round;stroke:currentColor}:host(.spinner-lines) svg,:host(.spinner-lines-small) svg,:host(.spinner-lines-sharp) svg,:host(.spinner-lines-sharp-small) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite}:host(.spinner-bubbles) svg{-webkit-animation:spinner-scale-out 1s linear infinite;animation:spinner-scale-out 1s linear infinite;fill:currentColor}:host(.spinner-circles) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite;fill:currentColor}:host(.spinner-crescent) circle{fill:transparent;stroke-width:4px;stroke-dasharray:128px;stroke-dashoffset:82px;stroke:currentColor}:host(.spinner-crescent) svg{-webkit-animation:spinner-rotate 1s linear infinite;animation:spinner-rotate 1s linear infinite}:host(.spinner-dots) circle{stroke-width:0;fill:currentColor}:host(.spinner-dots) svg{-webkit-animation:spinner-dots 1s linear infinite;animation:spinner-dots 1s linear infinite}:host(.spinner-circular) svg{-webkit-animation:spinner-circular linear infinite;animation:spinner-circular linear infinite}:host(.spinner-circular) circle{-webkit-animation:spinner-circular-inner ease-in-out infinite;animation:spinner-circular-inner ease-in-out infinite;stroke:currentColor;stroke-dasharray:80px, 200px;stroke-dashoffset:0px;stroke-width:5.6;fill:none}:host(.spinner-paused),:host(.spinner-paused) svg,:host(.spinner-paused) circle{-webkit-animation-play-state:paused;animation-play-state:paused}@-webkit-keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@-webkit-keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@-webkit-keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@-webkit-keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}@keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}",U=q,Q=v(class extends h{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.color=void 0,this.duration=void 0,this.name=void 0,this.paused=!1}getName(){let e=this.name||f.get("spinner"),t=u(this);return e||(t==="ios"?"lines":"circular")}render(){var e;let t=this,i=u(t),o=t.getName(),r=(e=L[o])!==null&&e!==void 0?e:L.lines,l=typeof t.duration=="number"&&t.duration>10?t.duration:r.dur,d=[];if(r.circles!==void 0)for(let p=0;p<r.circles;p++)d.push(K(r,l,p,r.circles));else if(r.lines!==void 0)for(let p=0;p<r.lines;p++)d.push(Y(r,l,p,r.lines));return s(g,{key:"e0dfa8a3ee2a0469eb31323f506750bd77d65797",class:x(t.color,{[i]:!0,[`spinner-${o}`]:!0,"spinner-paused":t.paused||f.getBoolean("_testing")}),role:"progressbar",style:r.elmDuration?{animationDuration:l+"ms"}:{}},d)}static get style(){return U}},[1,"ion-spinner",{color:[513],duration:[2],name:[1],paused:[4]}]),K=(n,e,t,i)=>{let o=n.fn(e,t,i);return o.style["animation-duration"]=e+"ms",s("svg",{viewBox:o.viewBox||"0 0 64 64",style:o.style},s("circle",{transform:o.transform||"translate(32,32)",cx:o.cx,cy:o.cy,r:o.r,style:n.elmDuration?{animationDuration:e+"ms"}:{}}))},Y=(n,e,t,i)=>{let o=n.fn(e,t,i);return o.style["animation-duration"]=e+"ms",s("svg",{viewBox:o.viewBox||"0 0 64 64",style:o.style},s("line",{transform:"translate(32,32)",y1:o.y1,y2:o.y2}))};function O(){if(typeof customElements>"u")return;["ion-spinner"].forEach(e=>{switch(e){case"ion-spinner":customElements.get(e)||customElements.define(e,Q);break}})}var J=n=>{let e=c(),t=c(),i=c();return t.addElement(n.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),i.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),e.addElement(n).easing("ease-in-out").duration(200).addAnimation([t,i])},ee=n=>{let e=c(),t=c(),i=c();return t.addElement(n.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),i.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),e.addElement(n).easing("ease-in-out").duration(200).addAnimation([t,i])},te=n=>{let e=c(),t=c(),i=c();return t.addElement(n.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),i.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),e.addElement(n).easing("ease-in-out").duration(200).addAnimation([t,i])},ne=n=>{let e=c(),t=c(),i=c();return t.addElement(n.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),i.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),e.addElement(n).easing("ease-in-out").duration(200).addAnimation([t,i])},ie=".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}",oe=ie,re=".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #0054e9);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}",se=re,ce=v(class extends h{constructor(){super(),this.__registerHost(),this.didPresent=a(this,"ionLoadingDidPresent",7),this.willPresent=a(this,"ionLoadingWillPresent",7),this.willDismiss=a(this,"ionLoadingWillDismiss",7),this.didDismiss=a(this,"ionLoadingDidDismiss",7),this.didPresentShorthand=a(this,"didPresent",7),this.willPresentShorthand=a(this,"willPresent",7),this.willDismissShorthand=a(this,"willDismiss",7),this.didDismissShorthand=a(this,"didDismiss",7),this.delegateController=z(this),this.lockController=P(),this.triggerController=B(),this.customHTMLEnabled=f.get("innerHTMLTemplatesEnabled",j),this.presented=!1,this.onBackdropTap=()=>{this.dismiss(void 0,R)},this.overlayIndex=void 0,this.delegate=void 0,this.hasController=!1,this.keyboardClose=!0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.message=void 0,this.cssClass=void 0,this.duration=0,this.backdropDismiss=!1,this.showBackdrop=!0,this.spinner=void 0,this.translucent=!1,this.animated=!0,this.htmlAttributes=void 0,this.isOpen=!1,this.trigger=void 0}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}triggerChanged(){let{trigger:e,el:t,triggerController:i}=this;e&&i.addClickListener(t,e)}connectedCallback(){T(this.el),this.triggerChanged()}componentWillLoad(){var e;if(this.spinner===void 0){let t=u(this);this.spinner=f.get("loadingSpinner",f.get("spinner",t==="ios"?"lines":"crescent"))}!((e=this.htmlAttributes)===null||e===void 0)&&e.id||M(this.el)}componentDidLoad(){this.isOpen===!0&&b(()=>this.present()),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}present(){return m(this,null,function*(){let e=yield this.lockController.lock();yield this.delegateController.attachViewToDom(),yield S(this,"loadingEnter",J,te),this.duration>0&&(this.durationTimeout=setTimeout(()=>this.dismiss(),this.duration+10)),e()})}dismiss(e,t){return m(this,null,function*(){let i=yield this.lockController.lock();this.durationTimeout&&clearTimeout(this.durationTimeout);let o=yield F(this,e,t,"loadingLeave",ee,ne);return o&&this.delegateController.removeViewFromDom(),i(),o})}onDidDismiss(){return D(this.el,"ionLoadingDidDismiss")}onWillDismiss(){return D(this.el,"ionLoadingWillDismiss")}renderLoadingMessage(e){let{customHTMLEnabled:t,message:i}=this;return t?s("div",{class:"loading-content",id:e,innerHTML:E(i)}):s("div",{class:"loading-content",id:e},i)}render(){let{message:e,spinner:t,htmlAttributes:i,overlayIndex:o}=this,r=u(this),l=`loading-${o}-msg`;return s(g,Object.assign({key:"d6066c8b56b1fe4b597a243a7dab191ef0d21286",role:"dialog","aria-modal":"true","aria-labelledby":e!==void 0?l:null,tabindex:"-1"},i,{style:{zIndex:`${4e4+this.overlayIndex}`},onIonBackdropTap:this.onBackdropTap,class:Object.assign(Object.assign({},w(this.cssClass)),{[r]:!0,"overlay-hidden":!0,"loading-translucent":this.translucent})}),s("ion-backdrop",{key:"2431eda00a2a3f510f5dfc39b7c7d47c056dfa3d",visible:this.showBackdrop,tappable:this.backdropDismiss}),s("div",{key:"cf210aaf5e754e4eccdb49cf7ead4647b3f9b2d1",tabindex:"0","aria-hidden":"true"}),s("div",{key:"fa9375143d391656d70e181d25b952c77c2fc6ec",class:"loading-wrapper ion-overlay-wrapper"},t&&s("div",{key:"8e4a4ed994f7f62df86b03696ac95162df41f52d",class:"loading-spinner"},s("ion-spinner",{key:"e5b323c272d365853ba92bd211e390b4fd4751d2",name:t,"aria-hidden":"true"})),e!==void 0&&this.renderLoadingMessage(l)),s("div",{key:"cae35ec8c34800477bff3ebcec8010e632158233",tabindex:"0","aria-hidden":"true"}))}get el(){return this}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}static get style(){return{ios:oe,md:se}}},[34,"ion-loading",{overlayIndex:[2,"overlay-index"],delegate:[16],hasController:[4,"has-controller"],keyboardClose:[4,"keyboard-close"],enterAnimation:[16],leaveAnimation:[16],message:[1],cssClass:[1,"css-class"],duration:[2],backdropDismiss:[4,"backdrop-dismiss"],showBackdrop:[4,"show-backdrop"],spinner:[1025],translucent:[4],animated:[4],htmlAttributes:[16],isOpen:[4,"is-open"],trigger:[1],present:[64],dismiss:[64],onDidDismiss:[64],onWillDismiss:[64]},void 0,{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}]);function ae(){if(typeof customElements>"u")return;["ion-loading","ion-backdrop","ion-spinner"].forEach(e=>{switch(e){case"ion-loading":customElements.get(e)||customElements.define(e,ce);break;case"ion-backdrop":customElements.get(e)||$();break;case"ion-spinner":customElements.get(e)||O();break}})}var N=ae;var Z=(()=>{let e=class e extends A{constructor(){super(k),N()}};C(e,"\u0275fac",function(o){return new(o||e)}),C(e,"\u0275prov",I({token:e,factory:e.\u0275fac,providedIn:"root"}));let n=e;return n})();var tn=(()=>{let e=class e{constructor(i){this.loadingController=i,this.activeLoader=null}showLoading(i="Please wait..."){return m(this,null,function*(){yield this.dismissLoading(),this.activeLoader=yield this.loadingController.create({message:i,spinner:"circular",cssClass:"accessible-loading"}),yield this.activeLoader.present(),this.fixAccessibility(this.activeLoader)})}dismissLoading(){return m(this,null,function*(){if(this.activeLoader){try{yield this.activeLoader.dismiss()}catch(i){console.warn("Error dismissing loader:",i)}this.activeLoader=null}})}fixAccessibility(i){var l;if(!i||!i.querySelector(".loading-wrapper"))return;let r=(l=i.shadowRoot)==null?void 0:l.querySelector(".overlay-hidden");r&&r.hasAttribute("aria-hidden")&&(r.removeAttribute("aria-hidden"),r.setAttribute("inert","")),i.onDidDismiss().then(()=>{let d=document.querySelector(":focus-within");d&&"focus"in d&&d.focus()})}};e.\u0275fac=function(o){return new(o||e)(y(Z))},e.\u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();export{tn as a};
