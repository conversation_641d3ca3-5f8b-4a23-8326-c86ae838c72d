{"version": 3, "sources": ["../../../../../../node_modules/@firebase/util/dist/postinstall.mjs", "../../../../../../node_modules/@firebase/util/dist/index.esm2017.js", "../../../../../../node_modules/@firebase/component/dist/esm/index.esm2017.js", "../../../../../../node_modules/@firebase/logger/dist/esm/index.esm2017.js", "../../../../../../node_modules/idb/build/wrap-idb-value.js", "../../../../../../node_modules/idb/build/index.js", "../../../../../../node_modules/@firebase/app/dist/esm/index.esm2017.js", "../../../../../../node_modules/@firebase/installations/dist/esm/index.esm2017.js", "../../../../../../node_modules/@firebase/messaging/dist/esm/index.esm2017.js", "../../../../../../node_modules/@capacitor-firebase/messaging/dist/esm/web.js"], "sourcesContent": ["const getDefaultsFromPostinstall = () => undefined;\nexport { getDefaultsFromPostinstall };", "import { getDefaultsFromPostinstall } from './postinstall.mjs';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.\n */\nconst CONSTANTS = {\n  /**\n   * @define {boolean} Whether this is the client Node.js SDK.\n   */\n  NODE_CLIENT: false,\n  /**\n   * @define {boolean} Whether this is the Admin Node.js SDK.\n   */\n  NODE_ADMIN: false,\n  /**\n   * Firebase SDK Version\n   */\n  SDK_VERSION: '${JSCORE_VERSION}'\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Throws an error if the provided assertion is falsy\n */\nconst assert = function (assertion, message) {\n  if (!assertion) {\n    throw assertionError(message);\n  }\n};\n/**\n * Returns an Error object suitable for throwing.\n */\nconst assertionError = function (message) {\n  return new Error('Firebase Database (' + CONSTANTS.SDK_VERSION + ') INTERNAL ASSERT FAILED: ' + message);\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst stringToByteArray$1 = function (str) {\n  // TODO(user): Use native implementations if/when available\n  const out = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = c >> 6 | 192;\n      out[p++] = c & 63 | 128;\n    } else if ((c & 0xfc00) === 0xd800 && i + 1 < str.length && (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = c >> 18 | 240;\n      out[p++] = c >> 12 & 63 | 128;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    } else {\n      out[p++] = c >> 12 | 224;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    }\n  }\n  return out;\n};\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes) {\n  // TODO(user): Use native implementations if/when available\n  const out = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode((c1 & 31) << 6 | c2 & 63);\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u = ((c1 & 7) << 18 | (c2 & 63) << 12 | (c3 & 63) << 6 | c4 & 63) - 0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode((c1 & 15) << 12 | (c2 & 63) << 6 | c3 & 63);\n    }\n  }\n  return out.join('');\n};\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nconst base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input, webSafe) {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n    this.init_();\n    const byteToCharMap = webSafe ? this.byteToCharMapWebSafe_ : this.byteToCharMap_;\n    const output = [];\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n      const outByte1 = byte1 >> 2;\n      const outByte2 = (byte1 & 0x03) << 4 | byte2 >> 4;\n      let outByte3 = (byte2 & 0x0f) << 2 | byte3 >> 6;\n      let outByte4 = byte3 & 0x3f;\n      if (!haveByte3) {\n        outByte4 = 64;\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n      output.push(byteToCharMap[outByte1], byteToCharMap[outByte2], byteToCharMap[outByte3], byteToCharMap[outByte4]);\n    }\n    return output.join('');\n  },\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input, webSafe) {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray$1(input), webSafe);\n  },\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input, webSafe) {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input, webSafe) {\n    this.init_();\n    const charToByteMap = webSafe ? this.charToByteMapWebSafe_ : this.charToByteMap_;\n    const output = [];\n    for (let i = 0; i < input.length;) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n      const outByte1 = byte1 << 2 | byte2 >> 4;\n      output.push(outByte1);\n      if (byte3 !== 64) {\n        const outByte2 = byte2 << 4 & 0xf0 | byte3 >> 2;\n        output.push(outByte2);\n        if (byte4 !== 64) {\n          const outByte3 = byte3 << 6 & 0xc0 | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n    return output;\n  },\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n/**\n * An error encountered while decoding base64 string.\n */\nclass DecodeBase64StringError extends Error {\n  constructor() {\n    super(...arguments);\n    this.name = 'DecodeBase64StringError';\n  }\n}\n/**\n * URL-safe base64 encoding\n */\nconst base64Encode = function (str) {\n  const utf8Bytes = stringToByteArray$1(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nconst base64urlEncodeWithoutPadding = function (str) {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nconst base64Decode = function (str) {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nfunction deepCopy(value) {\n  return deepExtend(undefined, value);\n}\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nfunction deepExtend(target, source) {\n  if (!(source instanceof Object)) {\n    return source;\n  }\n  switch (source.constructor) {\n    case Date:\n      // Treat Dates like scalars; if the target date object had any child\n      // properties - they will be lost!\n      const dateValue = source;\n      return new Date(dateValue.getTime());\n    case Object:\n      if (target === undefined) {\n        target = {};\n      }\n      break;\n    case Array:\n      // Always copy the array source and overwrite the target.\n      target = [];\n      break;\n    default:\n      // Not a plain Object - treat it as a scalar.\n      return source;\n  }\n  for (const prop in source) {\n    // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n    if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n      continue;\n    }\n    target[prop] = deepExtend(target[prop], source[prop]);\n  }\n  return target;\n}\nfunction isValidKey(key) {\n  return key !== '__proto__';\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nfunction getGlobal() {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst getDefaultsFromGlobal = () => getGlobal().__FIREBASE_DEFAULTS__;\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = () => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\nconst getDefaultsFromCookie = () => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nconst getDefaults = () => {\n  try {\n    return getDefaultsFromPostinstall() || getDefaultsFromGlobal() || getDefaultsFromEnvVariable() || getDefaultsFromCookie();\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nconst getDefaultEmulatorHost = productName => {\n  var _a, _b;\n  return (_b = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.emulatorHosts) === null || _b === void 0 ? void 0 : _b[productName];\n};\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nconst getDefaultEmulatorHostnameAndPort = productName => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nconst getDefaultAppConfig = () => {\n  var _a;\n  return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.config;\n};\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nconst getExperimentalSetting = name => {\n  var _a;\n  return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a[`_${name}`];\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Deferred {\n  constructor() {\n    this.reject = () => {};\n    this.resolve = () => {};\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve;\n      this.reject = reject;\n    });\n  }\n  /**\n   * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\n   */\n  wrapCallback(callback) {\n    return (error, value) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction createMockUserToken(token, projectId) {\n  if (token.uid) {\n    throw new Error('The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.');\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n  const payload = Object.assign({\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    }\n  }, token);\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [base64urlEncodeWithoutPadding(JSON.stringify(header)), base64urlEncodeWithoutPadding(JSON.stringify(payload)), signature].join('.');\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nfunction getUA() {\n  if (typeof navigator !== 'undefined' && typeof navigator['userAgent'] === 'string') {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nfunction isMobileCordova() {\n  return typeof window !== 'undefined' &&\n  // @ts-ignore Setting up an broadly applicable index signature for Window\n  // just to deal with this case would probably be a bad idea.\n  !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) && /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA());\n}\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nfunction isNode() {\n  var _a;\n  const forceEnvironment = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n  try {\n    return Object.prototype.toString.call(global.process) === '[object process]';\n  } catch (e) {\n    return false;\n  }\n}\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nfunction isBrowser() {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n/**\n * Detect Web Worker context.\n */\nfunction isWebWorker() {\n  return typeof WorkerGlobalScope !== 'undefined' && typeof self !== 'undefined' && self instanceof WorkerGlobalScope;\n}\n/**\n * Detect Cloudflare Worker context.\n */\nfunction isCloudflareWorker() {\n  return typeof navigator !== 'undefined' && navigator.userAgent === 'Cloudflare-Workers';\n}\nfunction isBrowserExtension() {\n  const runtime = typeof chrome === 'object' ? chrome.runtime : typeof browser === 'object' ? browser.runtime : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nfunction isReactNative() {\n  return typeof navigator === 'object' && navigator['product'] === 'ReactNative';\n}\n/** Detects Electron apps. */\nfunction isElectron() {\n  return getUA().indexOf('Electron/') >= 0;\n}\n/** Detects Internet Explorer. */\nfunction isIE() {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n/** Detects Universal Windows Platform apps. */\nfunction isUWP() {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nfunction isNodeSdk() {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n/** Returns true if we are running in Safari. */\nfunction isSafari() {\n  return !isNode() && !!navigator.userAgent && navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome');\n}\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nfunction isIndexedDBAvailable() {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nfunction validateIndexedDBOpenable() {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist = true;\n      const DB_CHECK_NAME = 'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n      request.onerror = () => {\n        var _a;\n        reject(((_a = request.error) === null || _a === void 0 ? void 0 : _a.message) || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nfunction areCookiesEnabled() {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\nconst ERROR_NAME = 'FirebaseError';\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nclass FirebaseError extends Error {\n  constructor(/** The error code for this error. */\n  code, message, /** Custom data for this error. */\n  customData) {\n    super(message);\n    this.code = code;\n    this.customData = customData;\n    /** The custom name for all FirebaseErrors. */\n    this.name = ERROR_NAME;\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\nclass ErrorFactory {\n  constructor(service, serviceName, errors) {\n    this.service = service;\n    this.serviceName = serviceName;\n    this.errors = errors;\n  }\n  create(code, ...data) {\n    const customData = data[0] || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n    return error;\n  }\n}\nfunction replaceTemplate(template, data) {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\nconst PATTERN = /\\{\\$([^}]+)}/g;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Evaluates a JSON string into a javascript object.\n *\n * @param {string} str A string containing JSON.\n * @return {*} The javascript object representing the specified JSON.\n */\nfunction jsonEval(str) {\n  return JSON.parse(str);\n}\n/**\n * Returns JSON representing a javascript object.\n * @param {*} data JavaScript object to be stringified.\n * @return {string} The JSON contents of the object.\n */\nfunction stringify(data) {\n  return JSON.stringify(data);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Decodes a Firebase auth. token into constituent parts.\n *\n * Notes:\n * - May return with invalid / incomplete claims if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst decode = function (token) {\n  let header = {},\n    claims = {},\n    data = {},\n    signature = '';\n  try {\n    const parts = token.split('.');\n    header = jsonEval(base64Decode(parts[0]) || '');\n    claims = jsonEval(base64Decode(parts[1]) || '');\n    signature = parts[2];\n    data = claims['d'] || {};\n    delete claims['d'];\n  } catch (e) {}\n  return {\n    header,\n    claims,\n    data,\n    signature\n  };\n};\n/**\n * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the\n * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isValidTimestamp = function (token) {\n  const claims = decode(token).claims;\n  const now = Math.floor(new Date().getTime() / 1000);\n  let validSince = 0,\n    validUntil = 0;\n  if (typeof claims === 'object') {\n    if (claims.hasOwnProperty('nbf')) {\n      validSince = claims['nbf'];\n    } else if (claims.hasOwnProperty('iat')) {\n      validSince = claims['iat'];\n    }\n    if (claims.hasOwnProperty('exp')) {\n      validUntil = claims['exp'];\n    } else {\n      // token will expire after 24h by default\n      validUntil = validSince + 86400;\n    }\n  }\n  return !!now && !!validSince && !!validUntil && now >= validSince && now <= validUntil;\n};\n/**\n * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.\n *\n * Notes:\n * - May return null if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst issuedAtTime = function (token) {\n  const claims = decode(token).claims;\n  if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {\n    return claims['iat'];\n  }\n  return null;\n};\n/**\n * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isValidFormat = function (token) {\n  const decoded = decode(token),\n    claims = decoded.claims;\n  return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');\n};\n/**\n * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isAdmin = function (token) {\n  const claims = decode(token).claims;\n  return typeof claims === 'object' && claims['admin'] === true;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction contains(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction safeGet(obj, key) {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\nfunction isEmpty(obj) {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction map(obj, fn, contextObj) {\n  const res = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res;\n}\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n    const aProp = a[k];\n    const bProp = b[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isObject(thing) {\n  return thing !== null && typeof thing === 'object';\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Rejects if the given promise doesn't resolve in timeInMS milliseconds.\n * @internal\n */\nfunction promiseWithTimeout(promise, timeInMS = 2000) {\n  const deferredPromise = new Deferred();\n  setTimeout(() => deferredPromise.reject('timeout!'), timeInMS);\n  promise.then(deferredPromise.resolve, deferredPromise.reject);\n  return deferredPromise.promise;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a\n * params object (e.g. {arg: 'val', arg2: 'val2'})\n * Note: You must prepend it with ? when adding it to a URL.\n */\nfunction querystring(querystringParams) {\n  const params = [];\n  for (const [key, value] of Object.entries(querystringParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(arrayVal => {\n        params.push(encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal));\n      });\n    } else {\n      params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n    }\n  }\n  return params.length ? '&' + params.join('&') : '';\n}\n/**\n * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object\n * (e.g. {arg: 'val', arg2: 'val2'})\n */\nfunction querystringDecode(querystring) {\n  const obj = {};\n  const tokens = querystring.replace(/^\\?/, '').split('&');\n  tokens.forEach(token => {\n    if (token) {\n      const [key, value] = token.split('=');\n      obj[decodeURIComponent(key)] = decodeURIComponent(value);\n    }\n  });\n  return obj;\n}\n/**\n * Extract the query string part of a URL, including the leading question mark (if present).\n */\nfunction extractQuerystring(url) {\n  const queryStart = url.indexOf('?');\n  if (!queryStart) {\n    return '';\n  }\n  const fragmentStart = url.indexOf('#', queryStart);\n  return url.substring(queryStart, fragmentStart > 0 ? fragmentStart : undefined);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview SHA-1 cryptographic hash.\n * Variable names follow the notation in FIPS PUB 180-3:\n * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.\n *\n * Usage:\n *   var sha1 = new sha1();\n *   sha1.update(bytes);\n *   var hash = sha1.digest();\n *\n * Performance:\n *   Chrome 23:   ~400 Mbit/s\n *   Firefox 16:  ~250 Mbit/s\n *\n */\n/**\n * SHA-1 cryptographic hash constructor.\n *\n * The properties declared here are discussed in the above algorithm document.\n * @constructor\n * @final\n * @struct\n */\nclass Sha1 {\n  constructor() {\n    /**\n     * Holds the previous values of accumulated variables a-e in the compress_\n     * function.\n     * @private\n     */\n    this.chain_ = [];\n    /**\n     * A buffer holding the partially computed hash result.\n     * @private\n     */\n    this.buf_ = [];\n    /**\n     * An array of 80 bytes, each a part of the message to be hashed.  Referred to\n     * as the message schedule in the docs.\n     * @private\n     */\n    this.W_ = [];\n    /**\n     * Contains data needed to pad messages less than 64 bytes.\n     * @private\n     */\n    this.pad_ = [];\n    /**\n     * @private {number}\n     */\n    this.inbuf_ = 0;\n    /**\n     * @private {number}\n     */\n    this.total_ = 0;\n    this.blockSize = 512 / 8;\n    this.pad_[0] = 128;\n    for (let i = 1; i < this.blockSize; ++i) {\n      this.pad_[i] = 0;\n    }\n    this.reset();\n  }\n  reset() {\n    this.chain_[0] = 0x67452301;\n    this.chain_[1] = 0xefcdab89;\n    this.chain_[2] = 0x98badcfe;\n    this.chain_[3] = 0x10325476;\n    this.chain_[4] = 0xc3d2e1f0;\n    this.inbuf_ = 0;\n    this.total_ = 0;\n  }\n  /**\n   * Internal compress helper function.\n   * @param buf Block to compress.\n   * @param offset Offset of the block in the buffer.\n   * @private\n   */\n  compress_(buf, offset) {\n    if (!offset) {\n      offset = 0;\n    }\n    const W = this.W_;\n    // get 16 big endian words\n    if (typeof buf === 'string') {\n      for (let i = 0; i < 16; i++) {\n        // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS\n        // have a bug that turns the post-increment ++ operator into pre-increment\n        // during JIT compilation.  We have code that depends heavily on SHA-1 for\n        // correctness and which is affected by this bug, so I've removed all uses\n        // of post-increment ++ in which the result value is used.  We can revert\n        // this change once the Safari bug\n        // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and\n        // most clients have been updated.\n        W[i] = buf.charCodeAt(offset) << 24 | buf.charCodeAt(offset + 1) << 16 | buf.charCodeAt(offset + 2) << 8 | buf.charCodeAt(offset + 3);\n        offset += 4;\n      }\n    } else {\n      for (let i = 0; i < 16; i++) {\n        W[i] = buf[offset] << 24 | buf[offset + 1] << 16 | buf[offset + 2] << 8 | buf[offset + 3];\n        offset += 4;\n      }\n    }\n    // expand to 80 words\n    for (let i = 16; i < 80; i++) {\n      const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n      W[i] = (t << 1 | t >>> 31) & 0xffffffff;\n    }\n    let a = this.chain_[0];\n    let b = this.chain_[1];\n    let c = this.chain_[2];\n    let d = this.chain_[3];\n    let e = this.chain_[4];\n    let f, k;\n    // TODO(user): Try to unroll this loop to speed up the computation.\n    for (let i = 0; i < 80; i++) {\n      if (i < 40) {\n        if (i < 20) {\n          f = d ^ b & (c ^ d);\n          k = 0x5a827999;\n        } else {\n          f = b ^ c ^ d;\n          k = 0x6ed9eba1;\n        }\n      } else {\n        if (i < 60) {\n          f = b & c | d & (b | c);\n          k = 0x8f1bbcdc;\n        } else {\n          f = b ^ c ^ d;\n          k = 0xca62c1d6;\n        }\n      }\n      const t = (a << 5 | a >>> 27) + f + e + k + W[i] & 0xffffffff;\n      e = d;\n      d = c;\n      c = (b << 30 | b >>> 2) & 0xffffffff;\n      b = a;\n      a = t;\n    }\n    this.chain_[0] = this.chain_[0] + a & 0xffffffff;\n    this.chain_[1] = this.chain_[1] + b & 0xffffffff;\n    this.chain_[2] = this.chain_[2] + c & 0xffffffff;\n    this.chain_[3] = this.chain_[3] + d & 0xffffffff;\n    this.chain_[4] = this.chain_[4] + e & 0xffffffff;\n  }\n  update(bytes, length) {\n    // TODO(johnlenz): tighten the function signature and remove this check\n    if (bytes == null) {\n      return;\n    }\n    if (length === undefined) {\n      length = bytes.length;\n    }\n    const lengthMinusBlock = length - this.blockSize;\n    let n = 0;\n    // Using local instead of member variables gives ~5% speedup on Firefox 16.\n    const buf = this.buf_;\n    let inbuf = this.inbuf_;\n    // The outer while loop should execute at most twice.\n    while (n < length) {\n      // When we have no data in the block to top up, we can directly process the\n      // input buffer (assuming it contains sufficient data). This gives ~25%\n      // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that\n      // the data is provided in large chunks (or in multiples of 64 bytes).\n      if (inbuf === 0) {\n        while (n <= lengthMinusBlock) {\n          this.compress_(bytes, n);\n          n += this.blockSize;\n        }\n      }\n      if (typeof bytes === 'string') {\n        while (n < length) {\n          buf[inbuf] = bytes.charCodeAt(n);\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      } else {\n        while (n < length) {\n          buf[inbuf] = bytes[n];\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      }\n    }\n    this.inbuf_ = inbuf;\n    this.total_ += length;\n  }\n  /** @override */\n  digest() {\n    const digest = [];\n    let totalBits = this.total_ * 8;\n    // Add pad 0x80 0x00*.\n    if (this.inbuf_ < 56) {\n      this.update(this.pad_, 56 - this.inbuf_);\n    } else {\n      this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));\n    }\n    // Add # bits.\n    for (let i = this.blockSize - 1; i >= 56; i--) {\n      this.buf_[i] = totalBits & 255;\n      totalBits /= 256; // Don't use bit-shifting here!\n    }\n    this.compress_(this.buf_);\n    let n = 0;\n    for (let i = 0; i < 5; i++) {\n      for (let j = 24; j >= 0; j -= 8) {\n        digest[n] = this.chain_[i] >> j & 255;\n        ++n;\n      }\n    }\n    return digest;\n  }\n}\n\n/**\n * Helper to make a Subscribe function (just like Promise helps make a\n * Thenable).\n *\n * @param executor Function which can make calls to a single Observer\n *     as a proxy.\n * @param onNoObservers Callback when count of Observers goes to zero.\n */\nfunction createSubscribe(executor, onNoObservers) {\n  const proxy = new ObserverProxy(executor, onNoObservers);\n  return proxy.subscribe.bind(proxy);\n}\n/**\n * Implement fan-out for any number of Observers attached via a subscribe\n * function.\n */\nclass ObserverProxy {\n  /**\n   * @param executor Function which can make calls to a single Observer\n   *     as a proxy.\n   * @param onNoObservers Callback when count of Observers goes to zero.\n   */\n  constructor(executor, onNoObservers) {\n    this.observers = [];\n    this.unsubscribes = [];\n    this.observerCount = 0;\n    // Micro-task scheduling by calling task.then().\n    this.task = Promise.resolve();\n    this.finalized = false;\n    this.onNoObservers = onNoObservers;\n    // Call the executor asynchronously so subscribers that are called\n    // synchronously after the creation of the subscribe function\n    // can still receive the very first value generated in the executor.\n    this.task.then(() => {\n      executor(this);\n    }).catch(e => {\n      this.error(e);\n    });\n  }\n  next(value) {\n    this.forEachObserver(observer => {\n      observer.next(value);\n    });\n  }\n  error(error) {\n    this.forEachObserver(observer => {\n      observer.error(error);\n    });\n    this.close(error);\n  }\n  complete() {\n    this.forEachObserver(observer => {\n      observer.complete();\n    });\n    this.close();\n  }\n  /**\n   * Subscribe function that can be used to add an Observer to the fan-out list.\n   *\n   * - We require that no event is sent to a subscriber synchronously to their\n   *   call to subscribe().\n   */\n  subscribe(nextOrObserver, error, complete) {\n    let observer;\n    if (nextOrObserver === undefined && error === undefined && complete === undefined) {\n      throw new Error('Missing Observer.');\n    }\n    // Assemble an Observer object when passed as callback functions.\n    if (implementsAnyMethods(nextOrObserver, ['next', 'error', 'complete'])) {\n      observer = nextOrObserver;\n    } else {\n      observer = {\n        next: nextOrObserver,\n        error,\n        complete\n      };\n    }\n    if (observer.next === undefined) {\n      observer.next = noop;\n    }\n    if (observer.error === undefined) {\n      observer.error = noop;\n    }\n    if (observer.complete === undefined) {\n      observer.complete = noop;\n    }\n    const unsub = this.unsubscribeOne.bind(this, this.observers.length);\n    // Attempt to subscribe to a terminated Observable - we\n    // just respond to the Observer with the final error or complete\n    // event.\n    if (this.finalized) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this.task.then(() => {\n        try {\n          if (this.finalError) {\n            observer.error(this.finalError);\n          } else {\n            observer.complete();\n          }\n        } catch (e) {\n          // nothing\n        }\n        return;\n      });\n    }\n    this.observers.push(observer);\n    return unsub;\n  }\n  // Unsubscribe is synchronous - we guarantee that no events are sent to\n  // any unsubscribed Observer.\n  unsubscribeOne(i) {\n    if (this.observers === undefined || this.observers[i] === undefined) {\n      return;\n    }\n    delete this.observers[i];\n    this.observerCount -= 1;\n    if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n      this.onNoObservers(this);\n    }\n  }\n  forEachObserver(fn) {\n    if (this.finalized) {\n      // Already closed by previous event....just eat the additional values.\n      return;\n    }\n    // Since sendOne calls asynchronously - there is no chance that\n    // this.observers will become undefined.\n    for (let i = 0; i < this.observers.length; i++) {\n      this.sendOne(i, fn);\n    }\n  }\n  // Call the Observer via one of it's callback function. We are careful to\n  // confirm that the observe has not been unsubscribed since this asynchronous\n  // function had been queued.\n  sendOne(i, fn) {\n    // Execute the callback asynchronously\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      if (this.observers !== undefined && this.observers[i] !== undefined) {\n        try {\n          fn(this.observers[i]);\n        } catch (e) {\n          // Ignore exceptions raised in Observers or missing methods of an\n          // Observer.\n          // Log error to console. b/31404806\n          if (typeof console !== 'undefined' && console.error) {\n            console.error(e);\n          }\n        }\n      }\n    });\n  }\n  close(err) {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    if (err !== undefined) {\n      this.finalError = err;\n    }\n    // Proxy is no longer needed - garbage collect references\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      this.observers = undefined;\n      this.onNoObservers = undefined;\n    });\n  }\n}\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction async(fn, onError) {\n  return (...args) => {\n    Promise.resolve(true).then(() => {\n      fn(...args);\n    }).catch(error => {\n      if (onError) {\n        onError(error);\n      }\n    });\n  };\n}\n/**\n * Return true if the object passed in implements any of the named methods.\n */\nfunction implementsAnyMethods(obj, methods) {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n  for (const method of methods) {\n    if (method in obj && typeof obj[method] === 'function') {\n      return true;\n    }\n  }\n  return false;\n}\nfunction noop() {\n  // do nothing\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Check to make sure the appropriate number of arguments are provided for a public function.\n * Throws an error if it fails.\n *\n * @param fnName The function name\n * @param minCount The minimum number of arguments to allow for the function call\n * @param maxCount The maximum number of argument to allow for the function call\n * @param argCount The actual number of arguments provided.\n */\nconst validateArgCount = function (fnName, minCount, maxCount, argCount) {\n  let argError;\n  if (argCount < minCount) {\n    argError = 'at least ' + minCount;\n  } else if (argCount > maxCount) {\n    argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;\n  }\n  if (argError) {\n    const error = fnName + ' failed: Was called with ' + argCount + (argCount === 1 ? ' argument.' : ' arguments.') + ' Expects ' + argError + '.';\n    throw new Error(error);\n  }\n};\n/**\n * Generates a string to prefix an error message about failed argument validation\n *\n * @param fnName The function name\n * @param argName The name of the argument\n * @return The prefix to add to the error thrown for validation.\n */\nfunction errorPrefix(fnName, argName) {\n  return `${fnName} failed: ${argName} argument `;\n}\n/**\n * @param fnName\n * @param argumentNumber\n * @param namespace\n * @param optional\n */\nfunction validateNamespace(fnName, namespace, optional) {\n  if (optional && !namespace) {\n    return;\n  }\n  if (typeof namespace !== 'string') {\n    //TODO: I should do more validation here. We only allow certain chars in namespaces.\n    throw new Error(errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.');\n  }\n}\nfunction validateCallback(fnName, argumentName,\n// eslint-disable-next-line @typescript-eslint/ban-types\ncallback, optional) {\n  if (optional && !callback) {\n    return;\n  }\n  if (typeof callback !== 'function') {\n    throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid function.');\n  }\n}\nfunction validateContextObject(fnName, argumentName, context, optional) {\n  if (optional && !context) {\n    return;\n  }\n  if (typeof context !== 'object' || context === null) {\n    throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid context object.');\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they\n// automatically replaced '\\r\\n' with '\\n', and they didn't handle surrogate pairs,\n// so it's been modified.\n// Note that not all Unicode characters appear as single characters in JavaScript strings.\n// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters\n// use 2 characters in JavaScript.  All 4-byte UTF-8 characters begin with a first\n// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate\n// pair).\n// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3\n/**\n * @param {string} str\n * @return {Array}\n */\nconst stringToByteArray = function (str) {\n  const out = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    // Is this the lead surrogate in a surrogate pair?\n    if (c >= 0xd800 && c <= 0xdbff) {\n      const high = c - 0xd800; // the high 10 bits.\n      i++;\n      assert(i < str.length, 'Surrogate pair missing trail surrogate.');\n      const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.\n      c = 0x10000 + (high << 10) + low;\n    }\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = c >> 6 | 192;\n      out[p++] = c & 63 | 128;\n    } else if (c < 65536) {\n      out[p++] = c >> 12 | 224;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    } else {\n      out[p++] = c >> 18 | 240;\n      out[p++] = c >> 12 & 63 | 128;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    }\n  }\n  return out;\n};\n/**\n * Calculate length without actually converting; useful for doing cheaper validation.\n * @param {string} str\n * @return {number}\n */\nconst stringLength = function (str) {\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i);\n    if (c < 128) {\n      p++;\n    } else if (c < 2048) {\n      p += 2;\n    } else if (c >= 0xd800 && c <= 0xdbff) {\n      // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.\n      p += 4;\n      i++; // skip trail surrogate.\n    } else {\n      p += 3;\n    }\n  }\n  return p;\n};\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nconst MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nconst RANDOM_FACTOR = 0.5;\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nfunction calculateBackoffMillis(backoffCount, intervalMillis = DEFAULT_INTERVAL_MILLIS, backoffFactor = DEFAULT_BACKOFF_FACTOR) {\n  // Calculates an exponentially increasing value.\n  // Deviation: calculates value from count and a constant interval, so we only need to save value\n  // and count to restore state.\n  const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n  // A random \"fuzz\" to avoid waves of retries.\n  // Deviation: randomFactor is required.\n  const randomWait = Math.round(\n  // A fraction of the backoff value to add/subtract.\n  // Deviation: changes multiplication order to improve readability.\n  RANDOM_FACTOR * currBaseValue * (\n  // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n  // if we add or subtract.\n  Math.random() - 0.5) * 2);\n  // Limits backoff to max to avoid effectively permanent backoff.\n  return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provide English ordinal letters after a number\n */\nfunction ordinal(i) {\n  if (!Number.isFinite(i)) {\n    return `${i}`;\n  }\n  return i + indicator(i);\n}\nfunction indicator(i) {\n  i = Math.abs(i);\n  const cent = i % 100;\n  if (cent >= 10 && cent <= 20) {\n    return 'th';\n  }\n  const dec = i % 10;\n  if (dec === 1) {\n    return 'st';\n  }\n  if (dec === 2) {\n    return 'nd';\n  }\n  if (dec === 3) {\n    return 'rd';\n  }\n  return 'th';\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction getModularInstance(service) {\n  if (service && service._delegate) {\n    return service._delegate;\n  } else {\n    return service;\n  }\n}\n\n/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nfunction isCloudWorkstation(host) {\n  return host.endsWith('.cloudworkstations.dev');\n}\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nasync function pingServer(endpoint) {\n  const result = await fetch(endpoint, {\n    credentials: 'include'\n  });\n  return result.ok;\n}\nexport { CONSTANTS, DecodeBase64StringError, Deferred, ErrorFactory, FirebaseError, MAX_VALUE_MILLIS, RANDOM_FACTOR, Sha1, areCookiesEnabled, assert, assertionError, async, base64, base64Decode, base64Encode, base64urlEncodeWithoutPadding, calculateBackoffMillis, contains, createMockUserToken, createSubscribe, decode, deepCopy, deepEqual, deepExtend, errorPrefix, extractQuerystring, getDefaultAppConfig, getDefaultEmulatorHost, getDefaultEmulatorHostnameAndPort, getDefaults, getExperimentalSetting, getGlobal, getModularInstance, getUA, isAdmin, isBrowser, isBrowserExtension, isCloudWorkstation, isCloudflareWorker, isElectron, isEmpty, isIE, isIndexedDBAvailable, isMobileCordova, isNode, isNodeSdk, isReactNative, isSafari, isUWP, isValidFormat, isValidTimestamp, isWebWorker, issuedAtTime, jsonEval, map, ordinal, pingServer, promiseWithTimeout, querystring, querystringDecode, safeGet, stringLength, stringToByteArray, stringify, validateArgCount, validateCallback, validateContextObject, validateIndexedDBOpenable, validateNamespace };\n", "import { Deferred } from '@firebase/util';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nclass Component {\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(name, instanceFactory, type) {\n    this.name = name;\n    this.instanceFactory = instanceFactory;\n    this.type = type;\n    this.multipleInstances = false;\n    /**\n     * Properties to be added to the service namespace\n     */\n    this.serviceProps = {};\n    this.instantiationMode = \"LAZY\" /* InstantiationMode.LAZY */;\n    this.onInstanceCreated = null;\n  }\n  setInstantiationMode(mode) {\n    this.instantiationMode = mode;\n    return this;\n  }\n  setMultipleInstances(multipleInstances) {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n  setServiceProps(props) {\n    this.serviceProps = props;\n    return this;\n  }\n  setInstanceCreatedCallback(callback) {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nclass Provider {\n  constructor(name, container) {\n    this.name = name;\n    this.container = container;\n    this.component = null;\n    this.instances = new Map();\n    this.instancesDeferred = new Map();\n    this.instancesOptions = new Map();\n    this.onInitCallbacks = new Map();\n  }\n  /**\n   * @param identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier) {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n      if (this.isInitialized(normalizedIdentifier) || this.shouldAutoInitialize()) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n    return this.instancesDeferred.get(normalizedIdentifier).promise;\n  }\n  getImmediate(options) {\n    var _a;\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(options === null || options === void 0 ? void 0 : options.identifier);\n    const optional = (_a = options === null || options === void 0 ? void 0 : options.optional) !== null && _a !== void 0 ? _a : false;\n    if (this.isInitialized(normalizedIdentifier) || this.shouldAutoInitialize()) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n  getComponent() {\n    return this.component;\n  }\n  setComponent(component) {\n    if (component.name !== this.name) {\n      throw Error(`Mismatching Component ${component.name} for Provider ${this.name}.`);\n    }\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n    this.component = component;\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({\n          instanceIdentifier: DEFAULT_ENTRY_NAME\n        });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n  clearInstance(identifier = DEFAULT_ENTRY_NAME) {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete() {\n    const services = Array.from(this.instances.values());\n    await Promise.all([...services.filter(service => 'INTERNAL' in service) // legacy services\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    .map(service => service.INTERNAL.delete()), ...services.filter(service => '_delete' in service) // modularized services\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    .map(service => service._delete())]);\n  }\n  isComponentSet() {\n    return this.component != null;\n  }\n  isInitialized(identifier = DEFAULT_ENTRY_NAME) {\n    return this.instances.has(identifier);\n  }\n  getOptions(identifier = DEFAULT_ENTRY_NAME) {\n    return this.instancesOptions.get(identifier) || {};\n  }\n  initialize(opts = {}) {\n    const {\n      options = {}\n    } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(opts.instanceIdentifier);\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(`${this.name}(${normalizedIdentifier}) has already been initialized`);\n    }\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    });\n    // resolve any pending promise waiting for the service instance\n    for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n    return instance;\n  }\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback, identifier) {\n    var _a;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks = (_a = this.onInitCallbacks.get(normalizedIdentifier)) !== null && _a !== void 0 ? _a : new Set();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  invokeOnInitCallbacks(instance, identifier) {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch (_a) {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n  getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }) {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance);\n      this.instancesOptions.set(instanceIdentifier, options);\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance, instanceIdentifier);\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(this.container, instanceIdentifier, instance);\n        } catch (_a) {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n    return instance || null;\n  }\n  normalizeInstanceIdentifier(identifier = DEFAULT_ENTRY_NAME) {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n  shouldAutoInitialize() {\n    return !!this.component && this.component.instantiationMode !== \"EXPLICIT\" /* InstantiationMode.EXPLICIT */;\n  }\n}\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier) {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\nfunction isComponentEager(component) {\n  return component.instantiationMode === \"EAGER\" /* InstantiationMode.EAGER */;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nclass ComponentContainer {\n  constructor(name) {\n    this.name = name;\n    this.providers = new Map();\n  }\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent(component) {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(`Component ${component.name} has already been registered with ${this.name}`);\n    }\n    provider.setComponent(component);\n  }\n  addOrOverwriteComponent(component) {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n    this.addComponent(component);\n  }\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider(name) {\n    if (this.providers.has(name)) {\n      return this.providers.get(name);\n    }\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider(name, this);\n    this.providers.set(name, provider);\n    return provider;\n  }\n  getProviders() {\n    return Array.from(this.providers.values());\n  }\n}\nexport { Component, ComponentContainer, Provider };\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A container for all of the Logger instances\n */\nconst instances = [];\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nvar LogLevel;\n(function (LogLevel) {\n  LogLevel[LogLevel[\"DEBUG\"] = 0] = \"DEBUG\";\n  LogLevel[LogLevel[\"VERBOSE\"] = 1] = \"VERBOSE\";\n  LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n  LogLevel[LogLevel[\"WARN\"] = 3] = \"WARN\";\n  LogLevel[LogLevel[\"ERROR\"] = 4] = \"ERROR\";\n  LogLevel[LogLevel[\"SILENT\"] = 5] = \"SILENT\";\n})(LogLevel || (LogLevel = {}));\nconst levelStringToEnum = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n/**\n * The default log level\n */\nconst defaultLogLevel = LogLevel.INFO;\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler = (instance, logType, ...args) => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType];\n  if (method) {\n    console[method](`[${now}]  ${instance.name}:`, ...args);\n  } else {\n    throw new Error(`Attempted to log a message with an invalid logType (value: ${logType})`);\n  }\n};\nclass Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(name) {\n    this.name = name;\n    /**\n     * The log level of the given Logger instance.\n     */\n    this._logLevel = defaultLogLevel;\n    /**\n     * The main (internal) log handler for the Logger instance.\n     * Can be set to a new function in internal package code but not by user.\n     */\n    this._logHandler = defaultLogHandler;\n    /**\n     * The optional, additional, user-defined log handler for the Logger instance.\n     */\n    this._userLogHandler = null;\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n  get logLevel() {\n    return this._logLevel;\n  }\n  set logLevel(val) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val) {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n  get logHandler() {\n    return this._logHandler;\n  }\n  set logHandler(val) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n  get userLogHandler() {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val) {\n    this._userLogHandler = val;\n  }\n  /**\n   * The functions below are all based on the `console` interface\n   */\n  debug(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\nfunction setLogLevel(level) {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\nfunction setUserLogHandler(logCallback, options) {\n  for (const instance of instances) {\n    let customLogLevel = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (instance, level, ...args) => {\n        const message = args.map(arg => {\n          if (arg == null) {\n            return null;\n          } else if (typeof arg === 'string') {\n            return arg;\n          } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n            return arg.toString();\n          } else if (arg instanceof Error) {\n            return arg.message;\n          } else {\n            try {\n              return JSON.stringify(arg);\n            } catch (ignored) {\n              return null;\n            }\n          }\n        }).filter(arg => arg).join(' ');\n        if (level >= (customLogLevel !== null && customLogLevel !== void 0 ? customLogLevel : instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase(),\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\nexport { LogLevel, Logger, setLogLevel, setUserLogHandler };\n", "const instanceOfAny = (object, constructors) => constructors.some(c => object instanceof c);\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n  return idbProxyableTypes || (idbProxyableTypes = [IDBDatabase, IDBObjectStore, IDBIndex, IDBCursor, IDBTransaction]);\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n  return cursorAdvanceMethods || (cursorAdvanceMethods = [IDBCursor.prototype.advance, IDBCursor.prototype.continue, IDBCursor.prototype.continuePrimaryKey]);\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n  const promise = new Promise((resolve, reject) => {\n    const unlisten = () => {\n      request.removeEventListener('success', success);\n      request.removeEventListener('error', error);\n    };\n    const success = () => {\n      resolve(wrap(request.result));\n      unlisten();\n    };\n    const error = () => {\n      reject(request.error);\n      unlisten();\n    };\n    request.addEventListener('success', success);\n    request.addEventListener('error', error);\n  });\n  promise.then(value => {\n    // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n    // (see wrapFunction).\n    if (value instanceof IDBCursor) {\n      cursorRequestMap.set(value, request);\n    }\n    // Catching to avoid \"Uncaught Promise exceptions\"\n  }).catch(() => {});\n  // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n  // is because we create many promises from a single IDBRequest.\n  reverseTransformCache.set(promise, request);\n  return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n  // Early bail if we've already created a done promise for this transaction.\n  if (transactionDoneMap.has(tx)) return;\n  const done = new Promise((resolve, reject) => {\n    const unlisten = () => {\n      tx.removeEventListener('complete', complete);\n      tx.removeEventListener('error', error);\n      tx.removeEventListener('abort', error);\n    };\n    const complete = () => {\n      resolve();\n      unlisten();\n    };\n    const error = () => {\n      reject(tx.error || new DOMException('AbortError', 'AbortError'));\n      unlisten();\n    };\n    tx.addEventListener('complete', complete);\n    tx.addEventListener('error', error);\n    tx.addEventListener('abort', error);\n  });\n  // Cache it for later retrieval.\n  transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n  get(target, prop, receiver) {\n    if (target instanceof IDBTransaction) {\n      // Special handling for transaction.done.\n      if (prop === 'done') return transactionDoneMap.get(target);\n      // Polyfill for objectStoreNames because of Edge.\n      if (prop === 'objectStoreNames') {\n        return target.objectStoreNames || transactionStoreNamesMap.get(target);\n      }\n      // Make tx.store return the only store in the transaction, or undefined if there are many.\n      if (prop === 'store') {\n        return receiver.objectStoreNames[1] ? undefined : receiver.objectStore(receiver.objectStoreNames[0]);\n      }\n    }\n    // Else transform whatever we get back.\n    return wrap(target[prop]);\n  },\n  set(target, prop, value) {\n    target[prop] = value;\n    return true;\n  },\n  has(target, prop) {\n    if (target instanceof IDBTransaction && (prop === 'done' || prop === 'store')) {\n      return true;\n    }\n    return prop in target;\n  }\n};\nfunction replaceTraps(callback) {\n  idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n  // Due to expected object equality (which is enforced by the caching in `wrap`), we\n  // only create one new func per func.\n  // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n  if (func === IDBDatabase.prototype.transaction && !('objectStoreNames' in IDBTransaction.prototype)) {\n    return function (storeNames, ...args) {\n      const tx = func.call(unwrap(this), storeNames, ...args);\n      transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n      return wrap(tx);\n    };\n  }\n  // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n  // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n  // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n  // with real promises, so each advance methods returns a new promise for the cursor object, or\n  // undefined if the end of the cursor has been reached.\n  if (getCursorAdvanceMethods().includes(func)) {\n    return function (...args) {\n      // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n      // the original object.\n      func.apply(unwrap(this), args);\n      return wrap(cursorRequestMap.get(this));\n    };\n  }\n  return function (...args) {\n    // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n    // the original object.\n    return wrap(func.apply(unwrap(this), args));\n  };\n}\nfunction transformCachableValue(value) {\n  if (typeof value === 'function') return wrapFunction(value);\n  // This doesn't return, it just creates a 'done' promise for the transaction,\n  // which is later returned for transaction.done (see idbObjectHandler).\n  if (value instanceof IDBTransaction) cacheDonePromiseForTransaction(value);\n  if (instanceOfAny(value, getIdbProxyableTypes())) return new Proxy(value, idbProxyTraps);\n  // Return the same value back if we're not going to transform it.\n  return value;\n}\nfunction wrap(value) {\n  // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n  // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n  if (value instanceof IDBRequest) return promisifyRequest(value);\n  // If we've already transformed this value before, reuse the transformed value.\n  // This is faster, but it also provides object equality.\n  if (transformCache.has(value)) return transformCache.get(value);\n  const newValue = transformCachableValue(value);\n  // Not all types are transformed.\n  // These may be primitive types, so they can't be WeakMap keys.\n  if (newValue !== value) {\n    transformCache.set(value, newValue);\n    reverseTransformCache.set(newValue, value);\n  }\n  return newValue;\n}\nconst unwrap = value => reverseTransformCache.get(value);\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, {\n  blocked,\n  upgrade,\n  blocking,\n  terminated\n} = {}) {\n  const request = indexedDB.open(name, version);\n  const openPromise = wrap(request);\n  if (upgrade) {\n    request.addEventListener('upgradeneeded', event => {\n      upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n    });\n  }\n  if (blocked) {\n    request.addEventListener('blocked', event => blocked(\n    // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n    event.oldVersion, event.newVersion, event));\n  }\n  openPromise.then(db => {\n    if (terminated) db.addEventListener('close', () => terminated());\n    if (blocking) {\n      db.addEventListener('versionchange', event => blocking(event.oldVersion, event.newVersion, event));\n    }\n  }).catch(() => {});\n  return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, {\n  blocked\n} = {}) {\n  const request = indexedDB.deleteDatabase(name);\n  if (blocked) {\n    request.addEventListener('blocked', event => blocked(\n    // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n    event.oldVersion, event));\n  }\n  return wrap(request).then(() => undefined);\n}\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n  if (!(target instanceof IDBDatabase && !(prop in target) && typeof prop === 'string')) {\n    return;\n  }\n  if (cachedMethods.get(prop)) return cachedMethods.get(prop);\n  const targetFuncName = prop.replace(/FromIndex$/, '');\n  const useIndex = prop !== targetFuncName;\n  const isWrite = writeMethods.includes(targetFuncName);\n  if (\n  // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n  !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) || !(isWrite || readMethods.includes(targetFuncName))) {\n    return;\n  }\n  const method = async function (storeName, ...args) {\n    // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n    const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n    let target = tx.store;\n    if (useIndex) target = target.index(args.shift());\n    // Must reject if op rejects.\n    // If it's a write operation, must reject if tx.done rejects.\n    // Must reject with op rejection first.\n    // Must resolve with op value.\n    // Must handle both promises (no unhandled rejections)\n    return (await Promise.all([target[targetFuncName](...args), isWrite && tx.done]))[0];\n  };\n  cachedMethods.set(prop, method);\n  return method;\n}\nreplaceTraps(oldTraps => ({\n  ...oldTraps,\n  get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n  has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop)\n}));\nexport { deleteDB, openDB };", "import { Component, ComponentContainer } from '@firebase/component';\nimport { <PERSON><PERSON>, setUser<PERSON>og<PERSON><PERSON><PERSON>, setLogLevel as setLogLevel$1 } from '@firebase/logger';\nimport { ErrorFactory, base64Decode, getDefaultAppConfig, deepEqual, isBrowser, isWebWorker, FirebaseError, base64urlEncodeWithoutPadding, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nexport { FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass PlatformLoggerServiceImpl {\n  constructor(container) {\n    this.container = container;\n  }\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString() {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers.map(provider => {\n      if (isVersionServiceProvider(provider)) {\n        const service = provider.getImmediate();\n        return `${service.library}/${service.version}`;\n      } else {\n        return null;\n      }\n    }).filter(logString => logString).join(' ');\n  }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider) {\n  const component = provider.getComponent();\n  return (component === null || component === void 0 ? void 0 : component.type) === \"VERSION\" /* ComponentType.VERSION */;\n}\nconst name$q = \"@firebase/app\";\nconst version$1 = \"0.12.1\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst logger = new Logger('@firebase/app');\nconst name$p = \"@firebase/app-compat\";\nconst name$o = \"@firebase/analytics-compat\";\nconst name$n = \"@firebase/analytics\";\nconst name$m = \"@firebase/app-check-compat\";\nconst name$l = \"@firebase/app-check\";\nconst name$k = \"@firebase/auth\";\nconst name$j = \"@firebase/auth-compat\";\nconst name$i = \"@firebase/database\";\nconst name$h = \"@firebase/data-connect\";\nconst name$g = \"@firebase/database-compat\";\nconst name$f = \"@firebase/functions\";\nconst name$e = \"@firebase/functions-compat\";\nconst name$d = \"@firebase/installations\";\nconst name$c = \"@firebase/installations-compat\";\nconst name$b = \"@firebase/messaging\";\nconst name$a = \"@firebase/messaging-compat\";\nconst name$9 = \"@firebase/performance\";\nconst name$8 = \"@firebase/performance-compat\";\nconst name$7 = \"@firebase/remote-config\";\nconst name$6 = \"@firebase/remote-config-compat\";\nconst name$5 = \"@firebase/storage\";\nconst name$4 = \"@firebase/storage-compat\";\nconst name$3 = \"@firebase/firestore\";\nconst name$2 = \"@firebase/vertexai\";\nconst name$1 = \"@firebase/firestore-compat\";\nconst name = \"firebase\";\nconst version = \"11.7.1\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The default app name\n *\n * @internal\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\nconst PLATFORM_LOG_STRING = {\n  [name$q]: 'fire-core',\n  [name$p]: 'fire-core-compat',\n  [name$n]: 'fire-analytics',\n  [name$o]: 'fire-analytics-compat',\n  [name$l]: 'fire-app-check',\n  [name$m]: 'fire-app-check-compat',\n  [name$k]: 'fire-auth',\n  [name$j]: 'fire-auth-compat',\n  [name$i]: 'fire-rtdb',\n  [name$h]: 'fire-data-connect',\n  [name$g]: 'fire-rtdb-compat',\n  [name$f]: 'fire-fn',\n  [name$e]: 'fire-fn-compat',\n  [name$d]: 'fire-iid',\n  [name$c]: 'fire-iid-compat',\n  [name$b]: 'fire-fcm',\n  [name$a]: 'fire-fcm-compat',\n  [name$9]: 'fire-perf',\n  [name$8]: 'fire-perf-compat',\n  [name$7]: 'fire-rc',\n  [name$6]: 'fire-rc-compat',\n  [name$5]: 'fire-gcs',\n  [name$4]: 'fire-gcs-compat',\n  [name$3]: 'fire-fst',\n  [name$1]: 'fire-fst-compat',\n  [name$2]: 'fire-vertex',\n  'fire-js': 'fire-js',\n  // Platform identifier for JS SDK.\n  [name]: 'fire-js-all'\n};\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @internal\n */\nconst _apps = new Map();\n/**\n * @internal\n */\nconst _serverApps = new Map();\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst _components = new Map();\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nfunction _addComponent(app, component) {\n  try {\n    app.container.addComponent(component);\n  } catch (e) {\n    logger.debug(`Component ${component.name} failed to register with FirebaseApp ${app.name}`, e);\n  }\n}\n/**\n *\n * @internal\n */\nfunction _addOrOverwriteComponent(app, component) {\n  app.container.addOrOverwriteComponent(component);\n}\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nfunction _registerComponent(component) {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(`There were multiple attempts to register component ${componentName}.`);\n    return false;\n  }\n  _components.set(componentName, component);\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app, component);\n  }\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp, component);\n  }\n  return true;\n}\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nfunction _getProvider(app, name) {\n  const heartbeatController = app.container.getProvider('heartbeat').getImmediate({\n    optional: true\n  });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return app.container.getProvider(name);\n}\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nfunction _removeServiceInstance(app, name, instanceIdentifier = DEFAULT_ENTRY_NAME) {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n/**\n *\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nfunction _isFirebaseApp(obj) {\n  return obj.options !== undefined;\n}\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nfunction _isFirebaseServerApp(obj) {\n  if (obj === null || obj === undefined) {\n    return false;\n  }\n  return obj.settings !== undefined;\n}\n/**\n * Test only\n *\n * @internal\n */\nfunction _clearComponents() {\n  _components.clear();\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst ERRORS = {\n  [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" + 'call initializeApp() first',\n  [\"bad-app-name\" /* AppError.BAD_APP_NAME */]: \"Illegal App name: '{$appName}'\",\n  [\"duplicate-app\" /* AppError.DUPLICATE_APP */]: \"Firebase App named '{$appName}' already exists with different options or config\",\n  [\"app-deleted\" /* AppError.APP_DELETED */]: \"Firebase App named '{$appName}' already deleted\",\n  [\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */]: 'Firebase Server App has been deleted',\n  [\"no-options\" /* AppError.NO_OPTIONS */]: 'Need to provide options, when not being deployed to hosting via source.',\n  [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' + 'Firebase App instance.',\n  [\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */]: 'First argument to `onLog` must be null or a function.',\n  [\"idb-open\" /* AppError.IDB_OPEN */]: 'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-get\" /* AppError.IDB_GET */]: 'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-set\" /* AppError.IDB_WRITE */]: 'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-delete\" /* AppError.IDB_DELETE */]: 'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */]: 'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */]: 'FirebaseServerApp is not for use in browser environments.'\n};\nconst ERROR_FACTORY = new ErrorFactory('app', 'Firebase', ERRORS);\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass FirebaseAppImpl {\n  constructor(options, config, container) {\n    this._isDeleted = false;\n    this._options = Object.assign({}, options);\n    this._config = Object.assign({}, config);\n    this._name = config.name;\n    this._automaticDataCollectionEnabled = config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(new Component('app', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  }\n  get automaticDataCollectionEnabled() {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n  set automaticDataCollectionEnabled(val) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n  get name() {\n    this.checkDestroyed();\n    return this._name;\n  }\n  get options() {\n    this.checkDestroyed();\n    return this._options;\n  }\n  get config() {\n    this.checkDestroyed();\n    return this._config;\n  }\n  get container() {\n    return this._container;\n  }\n  get isDeleted() {\n    return this._isDeleted;\n  }\n  set isDeleted(val) {\n    this._isDeleted = val;\n  }\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  checkDestroyed() {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(\"app-deleted\" /* AppError.APP_DELETED */, {\n        appName: this._name\n      });\n    }\n  }\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Parse the token and check to see if the `exp` claim is in the future.\n// Reports an error to the console if the token or claim could not be parsed, or if `exp` is in\n// the past.\nfunction validateTokenTTL(base64Token, tokenName) {\n  const secondPart = base64Decode(base64Token.split('.')[1]);\n  if (secondPart === null) {\n    console.error(`FirebaseServerApp ${tokenName} is invalid: second part could not be parsed.`);\n    return;\n  }\n  const expClaim = JSON.parse(secondPart).exp;\n  if (expClaim === undefined) {\n    console.error(`FirebaseServerApp ${tokenName} is invalid: expiration claim could not be parsed`);\n    return;\n  }\n  const exp = JSON.parse(secondPart).exp * 1000;\n  const now = new Date().getTime();\n  const diff = exp - now;\n  if (diff <= 0) {\n    console.error(`FirebaseServerApp ${tokenName} is invalid: the token has expired.`);\n  }\n}\nclass FirebaseServerAppImpl extends FirebaseAppImpl {\n  constructor(options, serverConfig, name, container) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled = serverConfig.automaticDataCollectionEnabled !== undefined ? serverConfig.automaticDataCollectionEnabled : false;\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config = {\n      name,\n      automaticDataCollectionEnabled\n    };\n    if (options.apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options, config, container);\n    } else {\n      const appImpl = options;\n      super(appImpl.options, config, container);\n    }\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = Object.assign({\n      automaticDataCollectionEnabled\n    }, serverConfig);\n    // Ensure that the current time is within the `authIdtoken` window of validity.\n    if (this._serverConfig.authIdToken) {\n      validateTokenTTL(this._serverConfig.authIdToken, 'authIdToken');\n    }\n    // Ensure that the current time is within the `appCheckToken` window of validity.\n    if (this._serverConfig.appCheckToken) {\n      validateTokenTTL(this._serverConfig.appCheckToken, 'appCheckToken');\n    }\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n    registerVersion(name$q, version$1, 'serverapp');\n  }\n  toJSON() {\n    return undefined;\n  }\n  get refCount() {\n    return this._refCount;\n  }\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj) {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n  // Decrement the reference count.\n  decRefCount() {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  automaticCleanup() {\n    void deleteApp(this);\n  }\n  get settings() {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  checkDestroyed() {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */);\n    }\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The current SDK version.\n *\n * @public\n */\nconst SDK_VERSION = version;\nfunction initializeApp(_options, rawConfig = {}) {\n  let options = _options;\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = {\n      name\n    };\n  }\n  const config = Object.assign({\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: false\n  }, rawConfig);\n  const name = config.name;\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(\"bad-app-name\" /* AppError.BAD_APP_NAME */, {\n      appName: String(name)\n    });\n  }\n  options || (options = getDefaultAppConfig());\n  if (!options) {\n    throw ERROR_FACTORY.create(\"no-options\" /* AppError.NO_OPTIONS */);\n  }\n  const existingApp = _apps.get(name);\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (deepEqual(options, existingApp.options) && deepEqual(config, existingApp.config)) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(\"duplicate-app\" /* AppError.DUPLICATE_APP */, {\n        appName: name\n      });\n    }\n  }\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n  const newApp = new FirebaseAppImpl(options, config, container);\n  _apps.set(name, newApp);\n  return newApp;\n}\nfunction initializeServerApp(_options, _serverAppConfig) {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */);\n  }\n  if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n    _serverAppConfig.automaticDataCollectionEnabled = false;\n  }\n  let appOptions;\n  if (_isFirebaseApp(_options)) {\n    appOptions = _options.options;\n  } else {\n    appOptions = _options;\n  }\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = Object.assign(Object.assign({}, _serverAppConfig), appOptions);\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n  const hashCode = s => {\n    return [...s].reduce((hash, c) => Math.imul(31, hash) + c.charCodeAt(0) | 0, 0);\n  };\n  if (_serverAppConfig.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */, {});\n    }\n  }\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString);\n  if (existingApp) {\n    existingApp.incRefCount(_serverAppConfig.releaseOnDeref);\n    return existingApp;\n  }\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n  const newApp = new FirebaseServerAppImpl(appOptions, _serverAppConfig, nameString, container);\n  _serverApps.set(nameString, newApp);\n  return newApp;\n}\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nfunction getApp(name = DEFAULT_ENTRY_NAME) {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, {\n      appName: name\n    });\n  }\n  return app;\n}\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nfunction getApps() {\n  return Array.from(_apps.values());\n}\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nasync function deleteApp(app) {\n  let cleanupProviders = false;\n  const name = app.name;\n  if (_apps.has(name)) {\n    cleanupProviders = true;\n    _apps.delete(name);\n  } else if (_serverApps.has(name)) {\n    const firebaseServerApp = app;\n    if (firebaseServerApp.decRefCount() <= 0) {\n      _serverApps.delete(name);\n      cleanupProviders = true;\n    }\n  }\n  if (cleanupProviders) {\n    await Promise.all(app.container.getProviders().map(provider => provider.delete()));\n    app.isDeleted = true;\n  }\n}\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nfunction registerVersion(libraryKeyOrName, version, variant) {\n  var _a;\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = (_a = PLATFORM_LOG_STRING[libraryKeyOrName]) !== null && _a !== void 0 ? _a : libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [`Unable to register library \"${library}\" with version \"${version}\":`];\n    if (libraryMismatch) {\n      warning.push(`library name \"${library}\" contains illegal characters (whitespace or \"/\")`);\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(`version name \"${version}\" contains illegal characters (whitespace or \"/\")`);\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(new Component(`${library}-version`, () => ({\n    library,\n    version\n  }), \"VERSION\" /* ComponentType.VERSION */));\n}\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nfunction onLog(logCallback, options) {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */);\n  }\n  setUserLogHandler(logCallback, options);\n}\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nfunction setLogLevel(logLevel) {\n  setLogLevel$1(logLevel);\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n  if (!dbPromise) {\n    dbPromise = openDB(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(\"idb-open\" /* AppError.IDB_OPEN */, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\nasync function readHeartbeatsFromIndexedDB(app) {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME);\n    const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n    // We already have the value but tx.done can throw,\n    // so we need to await it here to catch errors\n    await tx.done;\n    return result;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(\"idb-get\" /* AppError.IDB_GET */, {\n        originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\nasync function writeHeartbeatsToIndexedDB(app, heartbeatObject) {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(STORE_NAME);\n    await objectStore.put(heartbeatObject, computeKey(app));\n    await tx.done;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(\"idb-set\" /* AppError.IDB_WRITE */, {\n        originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\nfunction computeKey(app) {\n  return `${app.name}!${app.options.appId}`;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst MAX_HEADER_BYTES = 1024;\nconst MAX_NUM_STORED_HEARTBEATS = 30;\nclass HeartbeatServiceImpl {\n  constructor(container) {\n    this.container = container;\n    /**\n     * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n     * the header string.\n     * Stores one record per date. This will be consolidated into the standard\n     * format of one record per user agent string before being sent as a header.\n     * Populated from indexedDB when the controller is instantiated and should\n     * be kept in sync with indexedDB.\n     * Leave public for easier testing.\n     */\n    this._heartbeatsCache = null;\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n  /**\n   * Called to report a heartbeat. The function will generate\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n   * to IndexedDB.\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\n   * already logged, subsequent calls to this function in the same day will be ignored.\n   */\n  async triggerHeartbeat() {\n    var _a, _b;\n    try {\n      const platformLogger = this.container.getProvider('platform-logger').getImmediate();\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null) {\n        this._heartbeatsCache = await this._heartbeatsCachePromise;\n        // If we failed to construct a heartbeats cache, then return immediately.\n        if (((_b = this._heartbeatsCache) === null || _b === void 0 ? void 0 : _b.heartbeats) == null) {\n          return;\n        }\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (this._heartbeatsCache.lastSentHeartbeatDate === date || this._heartbeatsCache.heartbeats.some(singleDateHeartbeat => singleDateHeartbeat.date === date)) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        this._heartbeatsCache.heartbeats.push({\n          date,\n          agent\n        });\n        // If the number of stored heartbeats exceeds the maximum number of stored heartbeats, remove the heartbeat with the earliest date.\n        // Since this is executed each time a heartbeat is pushed, the limit can only be exceeded by one, so only one needs to be removed.\n        if (this._heartbeatsCache.heartbeats.length > MAX_NUM_STORED_HEARTBEATS) {\n          const earliestHeartbeatIdx = getEarliestHeartbeatIdx(this._heartbeatsCache.heartbeats);\n          this._heartbeatsCache.heartbeats.splice(earliestHeartbeatIdx, 1);\n        }\n      }\n      return this._storage.overwrite(this._heartbeatsCache);\n    } catch (e) {\n      logger.warn(e);\n    }\n  }\n  /**\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n   * It also clears all heartbeats from memory as well as in IndexedDB.\n   *\n   * NOTE: Consuming product SDKs should not send the header if this method\n   * returns an empty string.\n   */\n  async getHeartbeatsHeader() {\n    var _a;\n    try {\n      if (this._heartbeatsCache === null) {\n        await this._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null || this._heartbeatsCache.heartbeats.length === 0) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const {\n        heartbeatsToSend,\n        unsentEntries\n      } = extractHeartbeatsForHeader(this._heartbeatsCache.heartbeats);\n      const headerString = base64urlEncodeWithoutPadding(JSON.stringify({\n        version: 2,\n        heartbeats: heartbeatsToSend\n      }));\n      // Store last sent date to prevent another being logged/sent for the same day.\n      this._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        this._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        await this._storage.overwrite(this._heartbeatsCache);\n      } else {\n        this._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void this._storage.overwrite(this._heartbeatsCache);\n      }\n      return headerString;\n    } catch (e) {\n      logger.warn(e);\n      return '';\n    }\n  }\n}\nfunction getUTCDateString() {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\nfunction extractHeartbeatsForHeader(heartbeatsCache, maxSize = MAX_HEADER_BYTES) {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(hb => hb.agent === singleDateHeartbeat.agent);\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\nclass HeartbeatStorageImpl {\n  constructor(app) {\n    this.app = app;\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  async runIndexedDBEnvironmentCheck() {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    } else {\n      return validateIndexedDBOpenable().then(() => true).catch(() => false);\n    }\n  }\n  /**\n   * Read all heartbeats.\n   */\n  async read() {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return {\n        heartbeats: []\n      };\n    } else {\n      const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n      if (idbHeartbeatObject === null || idbHeartbeatObject === void 0 ? void 0 : idbHeartbeatObject.heartbeats) {\n        return idbHeartbeatObject;\n      } else {\n        return {\n          heartbeats: []\n        };\n      }\n    }\n  }\n  // overwrite the storage with the provided heartbeats\n  async overwrite(heartbeatsObject) {\n    var _a;\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: heartbeatsObject.heartbeats\n      });\n    }\n  }\n  // add heartbeats\n  async add(heartbeatsObject) {\n    var _a;\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: [...existingHeartbeatsObject.heartbeats, ...heartbeatsObject.heartbeats]\n      });\n    }\n  }\n}\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nfunction countBytes(heartbeatsCache) {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n  // heartbeatsCache wrapper properties\n  JSON.stringify({\n    version: 2,\n    heartbeats: heartbeatsCache\n  })).length;\n}\n/**\n * Returns the index of the heartbeat with the earliest date.\n * If the heartbeats array is empty, -1 is returned.\n */\nfunction getEarliestHeartbeatIdx(heartbeats) {\n  if (heartbeats.length === 0) {\n    return -1;\n  }\n  let earliestHeartbeatIdx = 0;\n  let earliestHeartbeatDate = heartbeats[0].date;\n  for (let i = 1; i < heartbeats.length; i++) {\n    if (heartbeats[i].date < earliestHeartbeatDate) {\n      earliestHeartbeatDate = heartbeats[i].date;\n      earliestHeartbeatIdx = i;\n    }\n  }\n  return earliestHeartbeatIdx;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction registerCoreComponents(variant) {\n  _registerComponent(new Component('platform-logger', container => new PlatformLoggerServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n  _registerComponent(new Component('heartbeat', container => new HeartbeatServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n  // Register `app` package.\n  registerVersion(name$q, version$1, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name$q, version$1, 'esm2017');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n\n/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\nregisterCoreComponents('');\nexport { SDK_VERSION, DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME, _addComponent, _addOrOverwriteComponent, _apps, _clearComponents, _components, _getProvider, _isFirebaseApp, _isFirebaseServerApp, _registerComponent, _removeServiceInstance, _serverApps, deleteApp, getApp, getApps, initializeApp, initializeServerApp, onLog, registerVersion, setLogLevel };\n", "import { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Component } from '@firebase/component';\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\nconst name = \"@firebase/installations\";\nconst version = \"0.6.14\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst PENDING_TIMEOUT_MS = 10000;\nconst PACKAGE_VERSION = `w:${version}`;\nconst INTERNAL_AUTH_VERSION = 'FIS_v2';\nconst INSTALLATIONS_API_URL = 'https://firebaseinstallations.googleapis.com/v1';\nconst TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\nconst SERVICE = 'installations';\nconst SERVICE_NAME = 'Installations';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst ERROR_DESCRIPTION_MAP = {\n  [\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */]: 'Missing App configuration value: \"{$valueName}\"',\n  [\"not-registered\" /* ErrorCode.NOT_REGISTERED */]: 'Firebase Installation is not registered.',\n  [\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */]: 'Firebase Installation not found.',\n  [\"request-failed\" /* ErrorCode.REQUEST_FAILED */]: '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [\"app-offline\" /* ErrorCode.APP_OFFLINE */]: 'Could not process request. Application offline.',\n  [\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */]: \"Can't delete installation while there is a pending registration request.\"\n};\nconst ERROR_FACTORY = new ErrorFactory(SERVICE, SERVICE_NAME, ERROR_DESCRIPTION_MAP);\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nfunction isServerError(error) {\n  return error instanceof FirebaseError && error.code.includes(\"request-failed\" /* ErrorCode.REQUEST_FAILED */);\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction getInstallationsEndpoint({\n  projectId\n}) {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\nfunction extractAuthTokenInfoFromResponse(response) {\n  return {\n    token: response.token,\n    requestStatus: 2 /* RequestStatus.COMPLETED */,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\nasync function getErrorFromResponse(requestName, response) {\n  const responseJson = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(\"request-failed\" /* ErrorCode.REQUEST_FAILED */, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\nfunction getHeaders({\n  apiKey\n}) {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\nfunction getHeadersWithAuth(appConfig, {\n  refreshToken\n}) {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nasync function retryIfServerError(fn) {\n  const result = await fn();\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n  return result;\n}\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn) {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\nfunction getAuthorizationHeader(refreshToken) {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function createInstallationRequest({\n  appConfig,\n  heartbeatServiceProvider\n}, {\n  fid\n}) {\n  const endpoint = getInstallationsEndpoint(appConfig);\n  const headers = getHeaders(appConfig);\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n  const request = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue = await response.json();\n    const registeredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: 2 /* RequestStatus.COMPLETED */,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Returns a promise that resolves after given time passes. */\nfunction sleep(ms) {\n  return new Promise(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction bufferToBase64UrlSafe(array) {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nconst INVALID_FID = '';\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nfunction generateFid() {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto = self.crypto || self.msCrypto;\n    crypto.getRandomValues(fidByteArray);\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + fidByteArray[0] % 0b00010000;\n    const fid = encode(fidByteArray);\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch (_a) {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray) {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Returns a string key that can be used to identify the app. */\nfunction getKey(appConfig) {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst fidChangeCallbacks = new Map();\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nfunction fidChanged(appConfig, fid) {\n  const key = getKey(appConfig);\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\nfunction addCallback(appConfig, callback) {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n  const key = getKey(appConfig);\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\nfunction removeCallback(appConfig, callback) {\n  const key = getKey(appConfig);\n  const callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    return;\n  }\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\nfunction callFidChangeCallbacks(key, fid) {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\nfunction broadcastFidChange(key, fid) {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({\n      key,\n      fid\n    });\n  }\n  closeBroadcastChannel();\n}\nlet broadcastChannel = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel() {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\nfunction closeBroadcastChannel() {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n/** Assigns or overwrites the record for the given key with the given value. */\nasync function set(appConfig, value) {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = await objectStore.get(key);\n  await objectStore.put(value, key);\n  await tx.done;\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n  return value;\n}\n/** Removes record(s) from the objectStore that match the given key. */\nasync function remove(appConfig) {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nasync function update(appConfig, updateFn) {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = await store.get(key);\n  const newValue = updateFn(oldValue);\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n  return newValue;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nasync function getInstallationEntry(installations) {\n  let registrationPromise;\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(installations, installationEntry);\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return {\n      installationEntry: await registrationPromise\n    };\n  }\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(oldEntry) {\n  const entry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: 0 /* RequestStatus.NOT_STARTED */\n  };\n  return clearTimedOutRequest(entry);\n}\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(installations, installationEntry) {\n  if (installationEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */));\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: 1 /* RequestStatus.IN_PROGRESS */,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(installations, inProgressEntry);\n    return {\n      installationEntry: inProgressEntry,\n      registrationPromise\n    };\n  } else if (installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return {\n      installationEntry\n    };\n  }\n}\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(installations, installationEntry) {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(installations, installationEntry);\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: 0 /* RequestStatus.NOT_STARTED */\n      });\n    }\n    throw e;\n  }\n}\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(installations) {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n  let entry = await updateInstallationRequest(installations.appConfig);\n  while (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n    // createInstallation request still in progress.\n    await sleep(100);\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n  if (entry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\n    // The request timed out or failed in a different call. Try again.\n    const {\n      installationEntry,\n      registrationPromise\n    } = await getInstallationEntry(installations);\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry;\n    }\n  }\n  return entry;\n}\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(appConfig) {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\nfunction clearTimedOutRequest(entry) {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: 0 /* RequestStatus.NOT_STARTED */\n    };\n  }\n  return entry;\n}\nfunction hasInstallationRequestTimedOut(installationEntry) {\n  return installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */ && installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now();\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function generateAuthTokenRequest({\n  appConfig,\n  heartbeatServiceProvider\n}, installationEntry) {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n  const request = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue = await response.json();\n    const completedAuthToken = extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\nfunction getGenerateAuthTokenEndpoint(appConfig, {\n  fid\n}) {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nasync function refreshAuthToken(installations, forceRefresh = false) {\n  let tokenPromise;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\n    }\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\n      }\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n  const authToken = tokenPromise ? await tokenPromise : entry.authToken;\n  return authToken;\n}\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(installations, forceRefresh) {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === 0 /* RequestStatus.NOT_STARTED */) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(appConfig) {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\n    }\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return Object.assign(Object.assign({}, oldEntry), {\n        authToken: {\n          requestStatus: 0 /* RequestStatus.NOT_STARTED */\n        }\n      });\n    }\n    return oldEntry;\n  });\n}\nasync function fetchAuthTokenFromServer(installations, installationEntry) {\n  try {\n    const authToken = await generateAuthTokenRequest(installations, installationEntry);\n    const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), {\n      authToken\n    });\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (isServerError(e) && (e.customData.serverCode === 401 || e.customData.serverCode === 404)) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), {\n        authToken: {\n          requestStatus: 0 /* RequestStatus.NOT_STARTED */\n        }\n      });\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\nfunction isEntryRegistered(installationEntry) {\n  return installationEntry !== undefined && installationEntry.registrationStatus === 2 /* RequestStatus.COMPLETED */;\n}\nfunction isAuthTokenValid(authToken) {\n  return authToken.requestStatus === 2 /* RequestStatus.COMPLETED */ && !isAuthTokenExpired(authToken);\n}\nfunction isAuthTokenExpired(authToken) {\n  const now = Date.now();\n  return now < authToken.creationTime || authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER;\n}\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(oldEntry) {\n  const inProgressAuthToken = {\n    requestStatus: 1 /* RequestStatus.IN_PROGRESS */,\n    requestTime: Date.now()\n  };\n  return Object.assign(Object.assign({}, oldEntry), {\n    authToken: inProgressAuthToken\n  });\n}\nfunction hasAuthTokenRequestTimedOut(authToken) {\n  return authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */ && authToken.requestTime + PENDING_TIMEOUT_MS < Date.now();\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nasync function getId(installations) {\n  const installationsImpl = installations;\n  const {\n    installationEntry,\n    registrationPromise\n  } = await getInstallationEntry(installationsImpl);\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n  return installationEntry.fid;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nasync function getToken(installations, forceRefresh = false) {\n  const installationsImpl = installations;\n  await completeInstallationRegistration(installationsImpl);\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\nasync function completeInstallationRegistration(installations) {\n  const {\n    registrationPromise\n  } = await getInstallationEntry(installations);\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function deleteInstallationRequest(appConfig, installationEntry) {\n  const endpoint = getDeleteEndpoint(appConfig, installationEntry);\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n  const request = {\n    method: 'DELETE',\n    headers\n  };\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (!response.ok) {\n    throw await getErrorFromResponse('Delete Installation', response);\n  }\n}\nfunction getDeleteEndpoint(appConfig, {\n  fid\n}) {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}`;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Deletes the Firebase Installation and all associated data.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nasync function deleteInstallations(installations) {\n  const {\n    appConfig\n  } = installations;\n  const entry = await update(appConfig, oldEntry => {\n    if (oldEntry && oldEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\n      // Delete the unregistered entry without sending a deleteInstallation request.\n      return undefined;\n    }\n    return oldEntry;\n  });\n  if (entry) {\n    if (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n      // Can't delete while trying to register.\n      throw ERROR_FACTORY.create(\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */);\n    } else if (entry.registrationStatus === 2 /* RequestStatus.COMPLETED */) {\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\n      } else {\n        await deleteInstallationRequest(appConfig, entry);\n        await remove(appConfig);\n      }\n    }\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Sets a new callback that will get called when Installation ID changes.\n * Returns an unsubscribe function that will remove the callback when called.\n * @param installations - The `Installations` instance.\n * @param callback - The callback function that is invoked when FID changes.\n * @returns A function that can be called to unsubscribe.\n *\n * @public\n */\nfunction onIdChange(installations, callback) {\n  const {\n    appConfig\n  } = installations;\n  addCallback(appConfig, callback);\n  return () => {\n    removeCallback(appConfig, callback);\n  };\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns an instance of {@link Installations} associated with the given\n * {@link @firebase/app#FirebaseApp} instance.\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\n *\n * @public\n */\nfunction getInstallations(app = getApp()) {\n  const installationsImpl = _getProvider(app, 'installations').getImmediate();\n  return installationsImpl;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction extractAppConfig(app) {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n  // Required app config keys\n  const configKeys = ['projectId', 'apiKey', 'appId'];\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n  return {\n    appName: app.name,\n    projectId: app.options.projectId,\n    apiKey: app.options.apiKey,\n    appId: app.options.appId\n  };\n}\nfunction getMissingValueError(valueName) {\n  return ERROR_FACTORY.create(\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */, {\n    valueName\n  });\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\nconst publicFactory = container => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n  const installationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\nconst internalFactory = container => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n  const installationsInternal = {\n    getId: () => getId(installations),\n    getToken: forceRefresh => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\nfunction registerInstallations() {\n  _registerComponent(new Component(INSTALLATIONS_NAME, publicFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  _registerComponent(new Component(INSTALLATIONS_NAME_INTERNAL, internalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\n}\n\n/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\nregisterVersion(name, version, 'esm2017');\nexport { deleteInstallations, getId, getInstallations, getToken, onIdChange };\n", "import '@firebase/installations';\nimport { Component } from '@firebase/component';\nimport { openDB, deleteDB } from 'idb';\nimport { ErrorFactory, validateIndexedDBOpenable, isIndexedDBAvailable, areCookiesEnabled, getModularInstance } from '@firebase/util';\nimport { _registerComponent, registerVersion, _getProvider, getApp } from '@firebase/app';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_SW_PATH = '/firebase-messaging-sw.js';\nconst DEFAULT_SW_SCOPE = '/firebase-cloud-messaging-push-scope';\nconst DEFAULT_VAPID_KEY = 'BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4';\nconst ENDPOINT = 'https://fcmregistrations.googleapis.com/v1';\nconst CONSOLE_CAMPAIGN_ID = 'google.c.a.c_id';\nconst CONSOLE_CAMPAIGN_NAME = 'google.c.a.c_l';\nconst CONSOLE_CAMPAIGN_TIME = 'google.c.a.ts';\n/** Set to '1' if Analytics is enabled for the campaign */\nconst CONSOLE_CAMPAIGN_ANALYTICS_ENABLED = 'google.c.a.e';\nconst DEFAULT_REGISTRATION_TIMEOUT = 10000;\nvar MessageType$1;\n(function (MessageType) {\n  MessageType[MessageType[\"DATA_MESSAGE\"] = 1] = \"DATA_MESSAGE\";\n  MessageType[MessageType[\"DISPLAY_NOTIFICATION\"] = 3] = \"DISPLAY_NOTIFICATION\";\n})(MessageType$1 || (MessageType$1 = {}));\n\n/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n * in compliance with the License. You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under the License\n * is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n * or implied. See the License for the specific language governing permissions and limitations under\n * the License.\n */\nvar MessageType;\n(function (MessageType) {\n  MessageType[\"PUSH_RECEIVED\"] = \"push-received\";\n  MessageType[\"NOTIFICATION_CLICKED\"] = \"notification-clicked\";\n})(MessageType || (MessageType = {}));\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction arrayToBase64(array) {\n  const uint8Array = new Uint8Array(array);\n  const base64String = btoa(String.fromCharCode(...uint8Array));\n  return base64String.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\nfunction base64ToArray(base64String) {\n  const padding = '='.repeat((4 - base64String.length % 4) % 4);\n  const base64 = (base64String + padding).replace(/\\-/g, '+').replace(/_/g, '/');\n  const rawData = atob(base64);\n  const outputArray = new Uint8Array(rawData.length);\n  for (let i = 0; i < rawData.length; ++i) {\n    outputArray[i] = rawData.charCodeAt(i);\n  }\n  return outputArray;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst OLD_DB_NAME = 'fcm_token_details_db';\n/**\n * The last DB version of 'fcm_token_details_db' was 4. This is one higher, so that the upgrade\n * callback is called for all versions of the old DB.\n */\nconst OLD_DB_VERSION = 5;\nconst OLD_OBJECT_STORE_NAME = 'fcm_token_object_Store';\nasync function migrateOldDatabase(senderId) {\n  if ('databases' in indexedDB) {\n    // indexedDb.databases() is an IndexedDB v3 API and does not exist in all browsers. TODO: Remove\n    // typecast when it lands in TS types.\n    const databases = await indexedDB.databases();\n    const dbNames = databases.map(db => db.name);\n    if (!dbNames.includes(OLD_DB_NAME)) {\n      // old DB didn't exist, no need to open.\n      return null;\n    }\n  }\n  let tokenDetails = null;\n  const db = await openDB(OLD_DB_NAME, OLD_DB_VERSION, {\n    upgrade: async (db, oldVersion, newVersion, upgradeTransaction) => {\n      var _a;\n      if (oldVersion < 2) {\n        // Database too old, skip migration.\n        return;\n      }\n      if (!db.objectStoreNames.contains(OLD_OBJECT_STORE_NAME)) {\n        // Database did not exist. Nothing to do.\n        return;\n      }\n      const objectStore = upgradeTransaction.objectStore(OLD_OBJECT_STORE_NAME);\n      const value = await objectStore.index('fcmSenderId').get(senderId);\n      await objectStore.clear();\n      if (!value) {\n        // No entry in the database, nothing to migrate.\n        return;\n      }\n      if (oldVersion === 2) {\n        const oldDetails = value;\n        if (!oldDetails.auth || !oldDetails.p256dh || !oldDetails.endpoint) {\n          return;\n        }\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: (_a = oldDetails.createTime) !== null && _a !== void 0 ? _a : Date.now(),\n          subscriptionOptions: {\n            auth: oldDetails.auth,\n            p256dh: oldDetails.p256dh,\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: typeof oldDetails.vapidKey === 'string' ? oldDetails.vapidKey : arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 3) {\n        const oldDetails = value;\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 4) {\n        const oldDetails = value;\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      }\n    }\n  });\n  db.close();\n  // Delete all old databases.\n  await deleteDB(OLD_DB_NAME);\n  await deleteDB('fcm_vapid_details_db');\n  await deleteDB('undefined');\n  return checkTokenDetails(tokenDetails) ? tokenDetails : null;\n}\nfunction checkTokenDetails(tokenDetails) {\n  if (!tokenDetails || !tokenDetails.subscriptionOptions) {\n    return false;\n  }\n  const {\n    subscriptionOptions\n  } = tokenDetails;\n  return typeof tokenDetails.createTime === 'number' && tokenDetails.createTime > 0 && typeof tokenDetails.token === 'string' && tokenDetails.token.length > 0 && typeof subscriptionOptions.auth === 'string' && subscriptionOptions.auth.length > 0 && typeof subscriptionOptions.p256dh === 'string' && subscriptionOptions.p256dh.length > 0 && typeof subscriptionOptions.endpoint === 'string' && subscriptionOptions.endpoint.length > 0 && typeof subscriptionOptions.swScope === 'string' && subscriptionOptions.swScope.length > 0 && typeof subscriptionOptions.vapidKey === 'string' && subscriptionOptions.vapidKey.length > 0;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Exported for tests.\nconst DATABASE_NAME = 'firebase-messaging-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-messaging-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (upgradeDb, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through behavior is what we want,\n        // because if there are multiple versions between the old version and the current version, we\n        // want ALL the migrations that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            upgradeDb.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n/** Gets record(s) from the objectStore that match the given key. */\nasync function dbGet(firebaseDependencies) {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tokenDetails = await db.transaction(OBJECT_STORE_NAME).objectStore(OBJECT_STORE_NAME).get(key);\n  if (tokenDetails) {\n    return tokenDetails;\n  } else {\n    // Check if there is a tokenDetails object in the old DB.\n    const oldTokenDetails = await migrateOldDatabase(firebaseDependencies.appConfig.senderId);\n    if (oldTokenDetails) {\n      await dbSet(firebaseDependencies, oldTokenDetails);\n      return oldTokenDetails;\n    }\n  }\n}\n/** Assigns or overwrites the record for the given key with the given value. */\nasync function dbSet(firebaseDependencies, tokenDetails) {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).put(tokenDetails, key);\n  await tx.done;\n  return tokenDetails;\n}\n/** Removes record(s) from the objectStore that match the given key. */\nasync function dbRemove(firebaseDependencies) {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\nfunction getKey({\n  appConfig\n}) {\n  return appConfig.appId;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst ERROR_MAP = {\n  [\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */]: 'Missing App configuration value: \"{$valueName}\"',\n  [\"only-available-in-window\" /* ErrorCode.AVAILABLE_IN_WINDOW */]: 'This method is available in a Window context.',\n  [\"only-available-in-sw\" /* ErrorCode.AVAILABLE_IN_SW */]: 'This method is available in a service worker context.',\n  [\"permission-default\" /* ErrorCode.PERMISSION_DEFAULT */]: 'The notification permission was not granted and dismissed instead.',\n  [\"permission-blocked\" /* ErrorCode.PERMISSION_BLOCKED */]: 'The notification permission was not granted and blocked instead.',\n  [\"unsupported-browser\" /* ErrorCode.UNSUPPORTED_BROWSER */]: \"This browser doesn't support the API's required to use the Firebase SDK.\",\n  [\"indexed-db-unsupported\" /* ErrorCode.INDEXED_DB_UNSUPPORTED */]: \"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)\",\n  [\"failed-service-worker-registration\" /* ErrorCode.FAILED_DEFAULT_REGISTRATION */]: 'We are unable to register the default service worker. {$browserErrorMessage}',\n  [\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */]: 'A problem occurred while subscribing the user to FCM: {$errorInfo}',\n  [\"token-subscribe-no-token\" /* ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN */]: 'FCM returned no token when subscribing the user to push.',\n  [\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */]: 'A problem occurred while unsubscribing the ' + 'user from FCM: {$errorInfo}',\n  [\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */]: 'A problem occurred while updating the user from FCM: {$errorInfo}',\n  [\"token-update-no-token\" /* ErrorCode.TOKEN_UPDATE_NO_TOKEN */]: 'FCM returned no token when updating the user to push.',\n  [\"use-sw-after-get-token\" /* ErrorCode.USE_SW_AFTER_GET_TOKEN */]: 'The useServiceWorker() method may only be called once and must be ' + 'called before calling getToken() to ensure your service worker is used.',\n  [\"invalid-sw-registration\" /* ErrorCode.INVALID_SW_REGISTRATION */]: 'The input to useServiceWorker() must be a ServiceWorkerRegistration.',\n  [\"invalid-bg-handler\" /* ErrorCode.INVALID_BG_HANDLER */]: 'The input to setBackgroundMessageHandler() must be a function.',\n  [\"invalid-vapid-key\" /* ErrorCode.INVALID_VAPID_KEY */]: 'The public VAPID key must be a string.',\n  [\"use-vapid-key-after-get-token\" /* ErrorCode.USE_VAPID_KEY_AFTER_GET_TOKEN */]: 'The usePublicVapidKey() method may only be called once and must be ' + 'called before calling getToken() to ensure your VAPID key is used.'\n};\nconst ERROR_FACTORY = new ErrorFactory('messaging', 'Messaging', ERROR_MAP);\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function requestGetToken(firebaseDependencies, subscriptionOptions) {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(subscriptionOptions);\n  const subscribeOptions = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n  let responseData;\n  try {\n    const response = await fetch(getEndpoint(firebaseDependencies.appConfig), subscribeOptions);\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */, {\n      errorInfo: err === null || err === void 0 ? void 0 : err.toString()\n    });\n  }\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */, {\n      errorInfo: message\n    });\n  }\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(\"token-subscribe-no-token\" /* ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN */);\n  }\n  return responseData.token;\n}\nasync function requestUpdateToken(firebaseDependencies, tokenDetails) {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(tokenDetails.subscriptionOptions);\n  const updateOptions = {\n    method: 'PATCH',\n    headers,\n    body: JSON.stringify(body)\n  };\n  let responseData;\n  try {\n    const response = await fetch(`${getEndpoint(firebaseDependencies.appConfig)}/${tokenDetails.token}`, updateOptions);\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */, {\n      errorInfo: err === null || err === void 0 ? void 0 : err.toString()\n    });\n  }\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */, {\n      errorInfo: message\n    });\n  }\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(\"token-update-no-token\" /* ErrorCode.TOKEN_UPDATE_NO_TOKEN */);\n  }\n  return responseData.token;\n}\nasync function requestDeleteToken(firebaseDependencies, token) {\n  const headers = await getHeaders(firebaseDependencies);\n  const unsubscribeOptions = {\n    method: 'DELETE',\n    headers\n  };\n  try {\n    const response = await fetch(`${getEndpoint(firebaseDependencies.appConfig)}/${token}`, unsubscribeOptions);\n    const responseData = await response.json();\n    if (responseData.error) {\n      const message = responseData.error.message;\n      throw ERROR_FACTORY.create(\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */, {\n        errorInfo: message\n      });\n    }\n  } catch (err) {\n    throw ERROR_FACTORY.create(\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */, {\n      errorInfo: err === null || err === void 0 ? void 0 : err.toString()\n    });\n  }\n}\nfunction getEndpoint({\n  projectId\n}) {\n  return `${ENDPOINT}/projects/${projectId}/registrations`;\n}\nasync function getHeaders({\n  appConfig,\n  installations\n}) {\n  const authToken = await installations.getToken();\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': appConfig.apiKey,\n    'x-goog-firebase-installations-auth': `FIS ${authToken}`\n  });\n}\nfunction getBody({\n  p256dh,\n  auth,\n  endpoint,\n  vapidKey\n}) {\n  const body = {\n    web: {\n      endpoint,\n      auth,\n      p256dh\n    }\n  };\n  if (vapidKey !== DEFAULT_VAPID_KEY) {\n    body.web.applicationPubKey = vapidKey;\n  }\n  return body;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// UpdateRegistration will be called once every week.\nconst TOKEN_EXPIRATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days\nasync function getTokenInternal(messaging) {\n  const pushSubscription = await getPushSubscription(messaging.swRegistration, messaging.vapidKey);\n  const subscriptionOptions = {\n    vapidKey: messaging.vapidKey,\n    swScope: messaging.swRegistration.scope,\n    endpoint: pushSubscription.endpoint,\n    auth: arrayToBase64(pushSubscription.getKey('auth')),\n    p256dh: arrayToBase64(pushSubscription.getKey('p256dh'))\n  };\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (!tokenDetails) {\n    // No token, get a new one.\n    return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n  } else if (!isTokenValid(tokenDetails.subscriptionOptions, subscriptionOptions)) {\n    // Invalid token, get a new one.\n    try {\n      await requestDeleteToken(messaging.firebaseDependencies, tokenDetails.token);\n    } catch (e) {\n      // Suppress errors because of #2364\n      console.warn(e);\n    }\n    return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n  } else if (Date.now() >= tokenDetails.createTime + TOKEN_EXPIRATION_MS) {\n    // Weekly token refresh\n    return updateToken(messaging, {\n      token: tokenDetails.token,\n      createTime: Date.now(),\n      subscriptionOptions\n    });\n  } else {\n    // Valid token, nothing to do.\n    return tokenDetails.token;\n  }\n}\n/**\n * This method deletes the token from the database, unsubscribes the token from FCM, and unregisters\n * the push subscription if it exists.\n */\nasync function deleteTokenInternal(messaging) {\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (tokenDetails) {\n    await requestDeleteToken(messaging.firebaseDependencies, tokenDetails.token);\n    await dbRemove(messaging.firebaseDependencies);\n  }\n  // Unsubscribe from the push subscription.\n  const pushSubscription = await messaging.swRegistration.pushManager.getSubscription();\n  if (pushSubscription) {\n    return pushSubscription.unsubscribe();\n  }\n  // If there's no SW, consider it a success.\n  return true;\n}\nasync function updateToken(messaging, tokenDetails) {\n  try {\n    const updatedToken = await requestUpdateToken(messaging.firebaseDependencies, tokenDetails);\n    const updatedTokenDetails = Object.assign(Object.assign({}, tokenDetails), {\n      token: updatedToken,\n      createTime: Date.now()\n    });\n    await dbSet(messaging.firebaseDependencies, updatedTokenDetails);\n    return updatedToken;\n  } catch (e) {\n    throw e;\n  }\n}\nasync function getNewToken(firebaseDependencies, subscriptionOptions) {\n  const token = await requestGetToken(firebaseDependencies, subscriptionOptions);\n  const tokenDetails = {\n    token,\n    createTime: Date.now(),\n    subscriptionOptions\n  };\n  await dbSet(firebaseDependencies, tokenDetails);\n  return tokenDetails.token;\n}\n/**\n * Gets a PushSubscription for the current user.\n */\nasync function getPushSubscription(swRegistration, vapidKey) {\n  const subscription = await swRegistration.pushManager.getSubscription();\n  if (subscription) {\n    return subscription;\n  }\n  return swRegistration.pushManager.subscribe({\n    userVisibleOnly: true,\n    // Chrome <= 75 doesn't support base64-encoded VAPID key. For backward compatibility, VAPID key\n    // submitted to pushManager#subscribe must be of type Uint8Array.\n    applicationServerKey: base64ToArray(vapidKey)\n  });\n}\n/**\n * Checks if the saved tokenDetails object matches the configuration provided.\n */\nfunction isTokenValid(dbOptions, currentOptions) {\n  const isVapidKeyEqual = currentOptions.vapidKey === dbOptions.vapidKey;\n  const isEndpointEqual = currentOptions.endpoint === dbOptions.endpoint;\n  const isAuthEqual = currentOptions.auth === dbOptions.auth;\n  const isP256dhEqual = currentOptions.p256dh === dbOptions.p256dh;\n  return isVapidKeyEqual && isEndpointEqual && isAuthEqual && isP256dhEqual;\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction externalizePayload(internalPayload) {\n  const payload = {\n    from: internalPayload.from,\n    // eslint-disable-next-line camelcase\n    collapseKey: internalPayload.collapse_key,\n    // eslint-disable-next-line camelcase\n    messageId: internalPayload.fcmMessageId\n  };\n  propagateNotificationPayload(payload, internalPayload);\n  propagateDataPayload(payload, internalPayload);\n  propagateFcmOptions(payload, internalPayload);\n  return payload;\n}\nfunction propagateNotificationPayload(payload, messagePayloadInternal) {\n  if (!messagePayloadInternal.notification) {\n    return;\n  }\n  payload.notification = {};\n  const title = messagePayloadInternal.notification.title;\n  if (!!title) {\n    payload.notification.title = title;\n  }\n  const body = messagePayloadInternal.notification.body;\n  if (!!body) {\n    payload.notification.body = body;\n  }\n  const image = messagePayloadInternal.notification.image;\n  if (!!image) {\n    payload.notification.image = image;\n  }\n  const icon = messagePayloadInternal.notification.icon;\n  if (!!icon) {\n    payload.notification.icon = icon;\n  }\n}\nfunction propagateDataPayload(payload, messagePayloadInternal) {\n  if (!messagePayloadInternal.data) {\n    return;\n  }\n  payload.data = messagePayloadInternal.data;\n}\nfunction propagateFcmOptions(payload, messagePayloadInternal) {\n  var _a, _b, _c, _d, _e;\n  // fcmOptions.link value is written into notification.click_action. see more in b/232072111\n  if (!messagePayloadInternal.fcmOptions && !((_a = messagePayloadInternal.notification) === null || _a === void 0 ? void 0 : _a.click_action)) {\n    return;\n  }\n  payload.fcmOptions = {};\n  const link = (_c = (_b = messagePayloadInternal.fcmOptions) === null || _b === void 0 ? void 0 : _b.link) !== null && _c !== void 0 ? _c : (_d = messagePayloadInternal.notification) === null || _d === void 0 ? void 0 : _d.click_action;\n  if (!!link) {\n    payload.fcmOptions.link = link;\n  }\n  // eslint-disable-next-line camelcase\n  const analyticsLabel = (_e = messagePayloadInternal.fcmOptions) === null || _e === void 0 ? void 0 : _e.analytics_label;\n  if (!!analyticsLabel) {\n    payload.fcmOptions.analyticsLabel = analyticsLabel;\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction isConsoleMessage(data) {\n  // This message has a campaign ID, meaning it was sent using the Firebase Console.\n  return typeof data === 'object' && !!data && CONSOLE_CAMPAIGN_ID in data;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n_mergeStrings('AzSCbw63g1R0nCw85jG8', 'Iaya3yLKwmgvh7cF0q4');\nfunction _mergeStrings(s1, s2) {\n  const resultArray = [];\n  for (let i = 0; i < s1.length; i++) {\n    resultArray.push(s1.charAt(i));\n    if (i < s2.length) {\n      resultArray.push(s2.charAt(i));\n    }\n  }\n  return resultArray.join('');\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction extractAppConfig(app) {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration Object');\n  }\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n  // Required app config keys\n  const configKeys = ['projectId', 'apiKey', 'appId', 'messagingSenderId'];\n  const {\n    options\n  } = app;\n  for (const keyName of configKeys) {\n    if (!options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n  return {\n    appName: app.name,\n    projectId: options.projectId,\n    apiKey: options.apiKey,\n    appId: options.appId,\n    senderId: options.messagingSenderId\n  };\n}\nfunction getMissingValueError(valueName) {\n  return ERROR_FACTORY.create(\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */, {\n    valueName\n  });\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass MessagingService {\n  constructor(app, installations, analyticsProvider) {\n    // logging is only done with end user consent. Default to false.\n    this.deliveryMetricsExportedToBigQueryEnabled = false;\n    this.onBackgroundMessageHandler = null;\n    this.onMessageHandler = null;\n    this.logEvents = [];\n    this.isLogServiceStarted = false;\n    const appConfig = extractAppConfig(app);\n    this.firebaseDependencies = {\n      app,\n      appConfig,\n      installations,\n      analyticsProvider\n    };\n  }\n  _delete() {\n    return Promise.resolve();\n  }\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function registerDefaultSw(messaging) {\n  try {\n    messaging.swRegistration = await navigator.serviceWorker.register(DEFAULT_SW_PATH, {\n      scope: DEFAULT_SW_SCOPE\n    });\n    // The timing when browser updates sw when sw has an update is unreliable from experiment. It\n    // leads to version conflict when the SDK upgrades to a newer version in the main page, but sw\n    // is stuck with the old version. For example,\n    // https://github.com/firebase/firebase-js-sdk/issues/2590 The following line reliably updates\n    // sw if there was an update.\n    messaging.swRegistration.update().catch(() => {\n      /* it is non blocking and we don't care if it failed */\n    });\n    await waitForRegistrationActive(messaging.swRegistration);\n  } catch (e) {\n    throw ERROR_FACTORY.create(\"failed-service-worker-registration\" /* ErrorCode.FAILED_DEFAULT_REGISTRATION */, {\n      browserErrorMessage: e === null || e === void 0 ? void 0 : e.message\n    });\n  }\n}\n/**\n * Waits for registration to become active. MDN documentation claims that\n * a service worker registration should be ready to use after awaiting\n * navigator.serviceWorker.register() but that doesn't seem to be the case in\n * practice, causing the SDK to throw errors when calling\n * swRegistration.pushManager.subscribe() too soon after register(). The only\n * solution seems to be waiting for the service worker registration `state`\n * to become \"active\".\n */\nasync function waitForRegistrationActive(registration) {\n  return new Promise((resolve, reject) => {\n    const rejectTimeout = setTimeout(() => reject(new Error(`Service worker not registered after ${DEFAULT_REGISTRATION_TIMEOUT} ms`)), DEFAULT_REGISTRATION_TIMEOUT);\n    const incomingSw = registration.installing || registration.waiting;\n    if (registration.active) {\n      clearTimeout(rejectTimeout);\n      resolve();\n    } else if (incomingSw) {\n      incomingSw.onstatechange = ev => {\n        var _a;\n        if (((_a = ev.target) === null || _a === void 0 ? void 0 : _a.state) === 'activated') {\n          incomingSw.onstatechange = null;\n          clearTimeout(rejectTimeout);\n          resolve();\n        }\n      };\n    } else {\n      clearTimeout(rejectTimeout);\n      reject(new Error('No incoming service worker found.'));\n    }\n  });\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function updateSwReg(messaging, swRegistration) {\n  if (!swRegistration && !messaging.swRegistration) {\n    await registerDefaultSw(messaging);\n  }\n  if (!swRegistration && !!messaging.swRegistration) {\n    return;\n  }\n  if (!(swRegistration instanceof ServiceWorkerRegistration)) {\n    throw ERROR_FACTORY.create(\"invalid-sw-registration\" /* ErrorCode.INVALID_SW_REGISTRATION */);\n  }\n  messaging.swRegistration = swRegistration;\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function updateVapidKey(messaging, vapidKey) {\n  if (!!vapidKey) {\n    messaging.vapidKey = vapidKey;\n  } else if (!messaging.vapidKey) {\n    messaging.vapidKey = DEFAULT_VAPID_KEY;\n  }\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function getToken$1(messaging, options) {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(\"only-available-in-window\" /* ErrorCode.AVAILABLE_IN_WINDOW */);\n  }\n  if (Notification.permission === 'default') {\n    await Notification.requestPermission();\n  }\n  if (Notification.permission !== 'granted') {\n    throw ERROR_FACTORY.create(\"permission-blocked\" /* ErrorCode.PERMISSION_BLOCKED */);\n  }\n  await updateVapidKey(messaging, options === null || options === void 0 ? void 0 : options.vapidKey);\n  await updateSwReg(messaging, options === null || options === void 0 ? void 0 : options.serviceWorkerRegistration);\n  return getTokenInternal(messaging);\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function logToScion(messaging, messageType, data) {\n  const eventType = getEventType(messageType);\n  const analytics = await messaging.firebaseDependencies.analyticsProvider.get();\n  analytics.logEvent(eventType, {\n    /* eslint-disable camelcase */\n    message_id: data[CONSOLE_CAMPAIGN_ID],\n    message_name: data[CONSOLE_CAMPAIGN_NAME],\n    message_time: data[CONSOLE_CAMPAIGN_TIME],\n    message_device_time: Math.floor(Date.now() / 1000)\n    /* eslint-enable camelcase */\n  });\n}\nfunction getEventType(messageType) {\n  switch (messageType) {\n    case MessageType.NOTIFICATION_CLICKED:\n      return 'notification_open';\n    case MessageType.PUSH_RECEIVED:\n      return 'notification_foreground';\n    default:\n      throw new Error();\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function messageEventListener(messaging, event) {\n  const internalPayload = event.data;\n  if (!internalPayload.isFirebaseMessaging) {\n    return;\n  }\n  if (messaging.onMessageHandler && internalPayload.messageType === MessageType.PUSH_RECEIVED) {\n    if (typeof messaging.onMessageHandler === 'function') {\n      messaging.onMessageHandler(externalizePayload(internalPayload));\n    } else {\n      messaging.onMessageHandler.next(externalizePayload(internalPayload));\n    }\n  }\n  // Log to Scion if applicable\n  const dataPayload = internalPayload.data;\n  if (isConsoleMessage(dataPayload) && dataPayload[CONSOLE_CAMPAIGN_ANALYTICS_ENABLED] === '1') {\n    await logToScion(messaging, internalPayload.messageType, dataPayload);\n  }\n}\nconst name = \"@firebase/messaging\";\nconst version = \"0.12.18\";\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst WindowMessagingFactory = container => {\n  const messaging = new MessagingService(container.getProvider('app').getImmediate(), container.getProvider('installations-internal').getImmediate(), container.getProvider('analytics-internal'));\n  navigator.serviceWorker.addEventListener('message', e => messageEventListener(messaging, e));\n  return messaging;\n};\nconst WindowMessagingInternalFactory = container => {\n  const messaging = container.getProvider('messaging').getImmediate();\n  const messagingInternal = {\n    getToken: options => getToken$1(messaging, options)\n  };\n  return messagingInternal;\n};\nfunction registerMessagingInWindow() {\n  _registerComponent(new Component('messaging', WindowMessagingFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  _registerComponent(new Component('messaging-internal', WindowMessagingInternalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, 'esm2017');\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks if all required APIs exist in the browser.\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nasync function isWindowSupported() {\n  try {\n    // This throws if open() is unsupported, so adding it to the conditional\n    // statement below can cause an uncaught error.\n    await validateIndexedDBOpenable();\n  } catch (e) {\n    return false;\n  }\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return typeof window !== 'undefined' && isIndexedDBAvailable() && areCookiesEnabled() && 'serviceWorker' in navigator && 'PushManager' in window && 'Notification' in window && 'fetch' in window && ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') && PushSubscription.prototype.hasOwnProperty('getKey');\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function deleteToken$1(messaging) {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(\"only-available-in-window\" /* ErrorCode.AVAILABLE_IN_WINDOW */);\n  }\n  if (!messaging.swRegistration) {\n    await registerDefaultSw(messaging);\n  }\n  return deleteTokenInternal(messaging);\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction onMessage$1(messaging, nextOrObserver) {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(\"only-available-in-window\" /* ErrorCode.AVAILABLE_IN_WINDOW */);\n  }\n  messaging.onMessageHandler = nextOrObserver;\n  return () => {\n    messaging.onMessageHandler = null;\n  };\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nfunction getMessagingInWindow(app = getApp()) {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isWindowSupported().then(isSupported => {\n    // If `isWindowSupported()` resolved, but returned false.\n    if (!isSupported) {\n      throw ERROR_FACTORY.create(\"unsupported-browser\" /* ErrorCode.UNSUPPORTED_BROWSER */);\n    }\n  }, _ => {\n    // If `isWindowSupported()` rejected.\n    throw ERROR_FACTORY.create(\"indexed-db-unsupported\" /* ErrorCode.INDEXED_DB_UNSUPPORTED */);\n  });\n  return _getProvider(getModularInstance(app), 'messaging').getImmediate();\n}\n/**\n * Subscribes the {@link Messaging} instance to push notifications. Returns a Firebase Cloud\n * Messaging registration token that can be used to send push messages to that {@link Messaging}\n * instance.\n *\n * If notification permission isn't already granted, this method asks the user for permission. The\n * returned promise rejects if the user does not allow the app to show notifications.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param options - Provides an optional vapid key and an optional service worker registration.\n *\n * @returns The promise resolves with an FCM registration token.\n *\n * @public\n */\nasync function getToken(messaging, options) {\n  messaging = getModularInstance(messaging);\n  return getToken$1(messaging, options);\n}\n/**\n * Deletes the registration token associated with this {@link Messaging} instance and unsubscribes\n * the {@link Messaging} instance from the push subscription.\n *\n * @param messaging - The {@link Messaging} instance.\n *\n * @returns The promise resolves when the token has been successfully deleted.\n *\n * @public\n */\nfunction deleteToken(messaging) {\n  messaging = getModularInstance(messaging);\n  return deleteToken$1(messaging);\n}\n/**\n * When a push message is received and the user is currently on a page for your origin, the\n * message is passed to the page and an `onMessage()` event is dispatched with the payload of\n * the push message.\n *\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined,\n *     is called when a message is received and the user is currently viewing your page.\n * @returns To stop listening for messages execute this returned function.\n *\n * @public\n */\nfunction onMessage(messaging, nextOrObserver) {\n  messaging = getModularInstance(messaging);\n  return onMessage$1(messaging, nextOrObserver);\n}\n\n/**\n * The Firebase Cloud Messaging Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\nregisterMessagingInWindow();\nexport { deleteToken, getMessagingInWindow as getMessaging, getToken, isWindowSupported as isSupported, onMessage };\n", "import { WebPlugin } from '@capacitor/core';\nimport { deleteToken, getMessaging, getToken, isSupported as isSupportedInWeb, onMessage } from 'firebase/messaging';\nexport class FirebaseMessagingWeb extends WebPlugin {\n  constructor() {\n    super();\n    isSupportedInWeb().then(supported => {\n      if (!supported) {\n        return;\n      }\n      const messaging = getMessaging();\n      onMessage(messaging, payload => this.handleNotificationReceived(payload));\n    });\n  }\n  async checkPermissions() {\n    const isSupported = await isSupportedInWeb();\n    if (!isSupported) {\n      return {\n        receive: 'denied'\n      };\n    }\n    const receive = this.convertNotificationPermissionToPermissionState(Notification.permission);\n    return {\n      receive\n    };\n  }\n  async requestPermissions() {\n    const isSupported = await isSupportedInWeb();\n    if (!isSupported) {\n      return {\n        receive: 'denied'\n      };\n    }\n    const notificationPermission = await Notification.requestPermission();\n    const receive = this.convertNotificationPermissionToPermissionState(notificationPermission);\n    return {\n      receive\n    };\n  }\n  async isSupported() {\n    const isSupported = await isSupportedInWeb();\n    return {\n      isSupported\n    };\n  }\n  async getToken(options) {\n    const messaging = getMessaging();\n    const token = await getToken(messaging, {\n      vapidKey: options.vapidKey,\n      serviceWorkerRegistration: options.serviceWorkerRegistration\n    });\n    return {\n      token\n    };\n  }\n  async deleteToken() {\n    const messaging = getMessaging();\n    await deleteToken(messaging);\n  }\n  async getDeliveredNotifications() {\n    this.throwUnavailableError();\n  }\n  async removeDeliveredNotifications(_options) {\n    this.throwUnavailableError();\n  }\n  async removeAllDeliveredNotifications() {\n    this.throwUnavailableError();\n  }\n  async subscribeToTopic(_options) {\n    this.throwUnavailableError();\n  }\n  async unsubscribeFromTopic(_options) {\n    this.throwUnavailableError();\n  }\n  async createChannel(_options) {\n    this.throwUnavailableError();\n  }\n  async deleteChannel(_options) {\n    this.throwUnavailableError();\n  }\n  async listChannels() {\n    this.throwUnavailableError();\n  }\n  handleNotificationReceived(messagePayload) {\n    const notification = this.createNotificationResult(messagePayload);\n    const event = {\n      notification\n    };\n    this.notifyListeners(FirebaseMessagingWeb.notificationReceivedEvent, event);\n  }\n  createNotificationResult(messagePayload) {\n    var _a, _b, _c;\n    const notification = {\n      body: (_a = messagePayload.notification) === null || _a === void 0 ? void 0 : _a.body,\n      data: messagePayload.data,\n      id: messagePayload.messageId,\n      image: (_b = messagePayload.notification) === null || _b === void 0 ? void 0 : _b.image,\n      title: (_c = messagePayload.notification) === null || _c === void 0 ? void 0 : _c.title\n    };\n    return notification;\n  }\n  convertNotificationPermissionToPermissionState(permission) {\n    let state = 'prompt';\n    switch (permission) {\n      case 'granted':\n        state = 'granted';\n        break;\n      case 'denied':\n        state = 'denied';\n        break;\n    }\n    return state;\n  }\n  throwUnavailableError() {\n    throw this.unavailable('Not available on web.');\n  }\n}\nFirebaseMessagingWeb.notificationReceivedEvent = 'notificationReceived';\n"], "mappings": ";;;;;;;;;;AAAA,IAAM,6BAA6B,MAAM;;;ACmFzC,IAAM,sBAAsB,SAAU,KAAK;AAEzC,QAAM,MAAM,CAAC;AACb,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,IAAI,IAAI,WAAW,CAAC;AACxB,QAAI,IAAI,KAAK;AACX,UAAI,GAAG,IAAI;AAAA,IACb,WAAW,IAAI,MAAM;AACnB,UAAI,GAAG,IAAI,KAAK,IAAI;AACpB,UAAI,GAAG,IAAI,IAAI,KAAK;AAAA,IACtB,YAAY,IAAI,WAAY,SAAU,IAAI,IAAI,IAAI,WAAW,IAAI,WAAW,IAAI,CAAC,IAAI,WAAY,OAAQ;AAEvG,UAAI,UAAY,IAAI,SAAW,OAAO,IAAI,WAAW,EAAE,CAAC,IAAI;AAC5D,UAAI,GAAG,IAAI,KAAK,KAAK;AACrB,UAAI,GAAG,IAAI,KAAK,KAAK,KAAK;AAC1B,UAAI,GAAG,IAAI,KAAK,IAAI,KAAK;AACzB,UAAI,GAAG,IAAI,IAAI,KAAK;AAAA,IACtB,OAAO;AACL,UAAI,GAAG,IAAI,KAAK,KAAK;AACrB,UAAI,GAAG,IAAI,KAAK,IAAI,KAAK;AACzB,UAAI,GAAG,IAAI,IAAI,KAAK;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AAOA,IAAM,oBAAoB,SAAU,OAAO;AAEzC,QAAM,MAAM,CAAC;AACb,MAAI,MAAM,GACR,IAAI;AACN,SAAO,MAAM,MAAM,QAAQ;AACzB,UAAM,KAAK,MAAM,KAAK;AACtB,QAAI,KAAK,KAAK;AACZ,UAAI,GAAG,IAAI,OAAO,aAAa,EAAE;AAAA,IACnC,WAAW,KAAK,OAAO,KAAK,KAAK;AAC/B,YAAM,KAAK,MAAM,KAAK;AACtB,UAAI,GAAG,IAAI,OAAO,cAAc,KAAK,OAAO,IAAI,KAAK,EAAE;AAAA,IACzD,WAAW,KAAK,OAAO,KAAK,KAAK;AAE/B,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,MAAM,KAAK,MAAM,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO,IAAI,KAAK,MAAM;AAC1E,UAAI,GAAG,IAAI,OAAO,aAAa,SAAU,KAAK,GAAG;AACjD,UAAI,GAAG,IAAI,OAAO,aAAa,SAAU,IAAI,KAAK;AAAA,IACpD,OAAO;AACL,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,KAAK,MAAM,KAAK;AACtB,UAAI,GAAG,IAAI,OAAO,cAAc,KAAK,OAAO,MAAM,KAAK,OAAO,IAAI,KAAK,EAAE;AAAA,IAC3E;AAAA,EACF;AACA,SAAO,IAAI,KAAK,EAAE;AACpB;AAKA,IAAM,SAAS;AAAA;AAAA;AAAA;AAAA,EAIb,gBAAgB;AAAA;AAAA;AAAA;AAAA,EAIhB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,mBAAmB;AAAA;AAAA;AAAA;AAAA,EAInB,IAAI,eAAe;AACjB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,uBAAuB;AACzB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUpC,gBAAgB,OAAO,SAAS;AAC9B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,MAAM,+CAA+C;AAAA,IAC7D;AACA,SAAK,MAAM;AACX,UAAM,gBAAgB,UAAU,KAAK,wBAAwB,KAAK;AAClE,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,YAAM,QAAQ,MAAM,CAAC;AACrB,YAAM,YAAY,IAAI,IAAI,MAAM;AAChC,YAAM,QAAQ,YAAY,MAAM,IAAI,CAAC,IAAI;AACzC,YAAM,YAAY,IAAI,IAAI,MAAM;AAChC,YAAM,QAAQ,YAAY,MAAM,IAAI,CAAC,IAAI;AACzC,YAAM,WAAW,SAAS;AAC1B,YAAM,YAAY,QAAQ,MAAS,IAAI,SAAS;AAChD,UAAI,YAAY,QAAQ,OAAS,IAAI,SAAS;AAC9C,UAAI,WAAW,QAAQ;AACvB,UAAI,CAAC,WAAW;AACd,mBAAW;AACX,YAAI,CAAC,WAAW;AACd,qBAAW;AAAA,QACb;AAAA,MACF;AACA,aAAO,KAAK,cAAc,QAAQ,GAAG,cAAc,QAAQ,GAAG,cAAc,QAAQ,GAAG,cAAc,QAAQ,CAAC;AAAA,IAChH;AACA,WAAO,OAAO,KAAK,EAAE;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,OAAO,SAAS;AAG3B,QAAI,KAAK,sBAAsB,CAAC,SAAS;AACvC,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,WAAO,KAAK,gBAAgB,oBAAoB,KAAK,GAAG,OAAO;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,OAAO,SAAS;AAG3B,QAAI,KAAK,sBAAsB,CAAC,SAAS;AACvC,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,WAAO,kBAAkB,KAAK,wBAAwB,OAAO,OAAO,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,wBAAwB,OAAO,SAAS;AACtC,SAAK,MAAM;AACX,UAAM,gBAAgB,UAAU,KAAK,wBAAwB,KAAK;AAClE,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,MAAM,UAAS;AACjC,YAAM,QAAQ,cAAc,MAAM,OAAO,GAAG,CAAC;AAC7C,YAAM,YAAY,IAAI,MAAM;AAC5B,YAAM,QAAQ,YAAY,cAAc,MAAM,OAAO,CAAC,CAAC,IAAI;AAC3D,QAAE;AACF,YAAM,YAAY,IAAI,MAAM;AAC5B,YAAM,QAAQ,YAAY,cAAc,MAAM,OAAO,CAAC,CAAC,IAAI;AAC3D,QAAE;AACF,YAAM,YAAY,IAAI,MAAM;AAC5B,YAAM,QAAQ,YAAY,cAAc,MAAM,OAAO,CAAC,CAAC,IAAI;AAC3D,QAAE;AACF,UAAI,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,MAAM;AACpE,cAAM,IAAI,wBAAwB;AAAA,MACpC;AACA,YAAM,WAAW,SAAS,IAAI,SAAS;AACvC,aAAO,KAAK,QAAQ;AACpB,UAAI,UAAU,IAAI;AAChB,cAAM,WAAW,SAAS,IAAI,MAAO,SAAS;AAC9C,eAAO,KAAK,QAAQ;AACpB,YAAI,UAAU,IAAI;AAChB,gBAAM,WAAW,SAAS,IAAI,MAAO;AACrC,iBAAO,KAAK,QAAQ;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB,CAAC;AACvB,WAAK,iBAAiB,CAAC;AACvB,WAAK,wBAAwB,CAAC;AAC9B,WAAK,wBAAwB,CAAC;AAE9B,eAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACjD,aAAK,eAAe,CAAC,IAAI,KAAK,aAAa,OAAO,CAAC;AACnD,aAAK,eAAe,KAAK,eAAe,CAAC,CAAC,IAAI;AAC9C,aAAK,sBAAsB,CAAC,IAAI,KAAK,qBAAqB,OAAO,CAAC;AAClE,aAAK,sBAAsB,KAAK,sBAAsB,CAAC,CAAC,IAAI;AAE5D,YAAI,KAAK,KAAK,kBAAkB,QAAQ;AACtC,eAAK,eAAe,KAAK,qBAAqB,OAAO,CAAC,CAAC,IAAI;AAC3D,eAAK,sBAAsB,KAAK,aAAa,OAAO,CAAC,CAAC,IAAI;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAM,0BAAN,cAAsC,MAAM;AAAA,EAC1C,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AAAA,EACd;AACF;AAIA,IAAM,eAAe,SAAU,KAAK;AAClC,QAAM,YAAY,oBAAoB,GAAG;AACzC,SAAO,OAAO,gBAAgB,WAAW,IAAI;AAC/C;AAKA,IAAM,gCAAgC,SAAU,KAAK;AAEnD,SAAO,aAAa,GAAG,EAAE,QAAQ,OAAO,EAAE;AAC5C;AAUA,IAAM,eAAe,SAAU,KAAK;AAClC,MAAI;AACF,WAAO,OAAO,aAAa,KAAK,IAAI;AAAA,EACtC,SAAS,GAAG;AACV,YAAQ,MAAM,yBAAyB,CAAC;AAAA,EAC1C;AACA,SAAO;AACT;AA+FA,SAAS,YAAY;AACnB,MAAI,OAAO,SAAS,aAAa;AAC/B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,QAAM,IAAI,MAAM,iCAAiC;AACnD;AAkBA,IAAM,wBAAwB,MAAM,UAAU,EAAE;AAShD,IAAM,6BAA6B,MAAM;AACvC,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,aAAa;AACxE;AAAA,EACF;AACA,QAAM,qBAAqB,QAAQ,IAAI;AACvC,MAAI,oBAAoB;AACtB,WAAO,KAAK,MAAM,kBAAkB;AAAA,EACtC;AACF;AACA,IAAM,wBAAwB,MAAM;AAClC,MAAI,OAAO,aAAa,aAAa;AACnC;AAAA,EACF;AACA,MAAI;AACJ,MAAI;AACF,YAAQ,SAAS,OAAO,MAAM,+BAA+B;AAAA,EAC/D,SAAS,GAAG;AAGV;AAAA,EACF;AACA,QAAM,UAAU,SAAS,aAAa,MAAM,CAAC,CAAC;AAC9C,SAAO,WAAW,KAAK,MAAM,OAAO;AACtC;AAQA,IAAM,cAAc,MAAM;AACxB,MAAI;AACF,WAAO,2BAA2B,KAAK,sBAAsB,KAAK,2BAA2B,KAAK,sBAAsB;AAAA,EAC1H,SAAS,GAAG;AAOV,YAAQ,KAAK,+CAA+C,CAAC,EAAE;AAC/D;AAAA,EACF;AACF;AAuCA,IAAM,sBAAsB,MAAM;AAChC,MAAI;AACJ,UAAQ,KAAK,YAAY,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACtE;AA2BA,IAAM,WAAN,MAAe;AAAA,EACb,cAAc;AACZ,SAAK,SAAS,MAAM;AAAA,IAAC;AACrB,SAAK,UAAU,MAAM;AAAA,IAAC;AACtB,SAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC9C,WAAK,UAAU;AACf,WAAK,SAAS;AAAA,IAChB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,UAAU;AACrB,WAAO,CAAC,OAAO,UAAU;AACvB,UAAI,OAAO;AACT,aAAK,OAAO,KAAK;AAAA,MACnB,OAAO;AACL,aAAK,QAAQ,KAAK;AAAA,MACpB;AACA,UAAI,OAAO,aAAa,YAAY;AAGlC,aAAK,QAAQ,MAAM,MAAM;AAAA,QAAC,CAAC;AAG3B,YAAI,SAAS,WAAW,GAAG;AACzB,mBAAS,KAAK;AAAA,QAChB,OAAO;AACL,mBAAS,OAAO,KAAK;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AA8KA,SAAS,uBAAuB;AAC9B,MAAI;AACF,WAAO,OAAO,cAAc;AAAA,EAC9B,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAQA,SAAS,4BAA4B;AACnC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI;AACF,UAAI,WAAW;AACf,YAAM,gBAAgB;AACtB,YAAM,UAAU,KAAK,UAAU,KAAK,aAAa;AACjD,cAAQ,YAAY,MAAM;AACxB,gBAAQ,OAAO,MAAM;AAErB,YAAI,CAAC,UAAU;AACb,eAAK,UAAU,eAAe,aAAa;AAAA,QAC7C;AACA,gBAAQ,IAAI;AAAA,MACd;AACA,cAAQ,kBAAkB,MAAM;AAC9B,mBAAW;AAAA,MACb;AACA,cAAQ,UAAU,MAAM;AACtB,YAAI;AACJ,iBAAS,KAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,EAAE;AAAA,MACrF;AAAA,IACF,SAAS,OAAO;AACd,aAAO,KAAK;AAAA,IACd;AAAA,EACF,CAAC;AACH;AAMA,SAAS,oBAAoB;AAC3B,MAAI,OAAO,cAAc,eAAe,CAAC,UAAU,eAAe;AAChE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AA0DA,IAAM,aAAa;AAGnB,IAAM,gBAAN,MAAM,uBAAsB,MAAM;AAAA,EAChC,YACA,MAAM,SACN,YAAY;AACV,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,aAAa;AAElB,SAAK,OAAO;AAKZ,WAAO,eAAe,MAAM,eAAc,SAAS;AAGnD,QAAI,MAAM,mBAAmB;AAC3B,YAAM,kBAAkB,MAAM,aAAa,UAAU,MAAM;AAAA,IAC7D;AAAA,EACF;AACF;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,SAAS,aAAa,QAAQ;AACxC,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,SAAS,MAAM;AACpB,UAAM,aAAa,KAAK,CAAC,KAAK,CAAC;AAC/B,UAAM,WAAW,GAAG,KAAK,OAAO,IAAI,IAAI;AACxC,UAAM,WAAW,KAAK,OAAO,IAAI;AACjC,UAAM,UAAU,WAAW,gBAAgB,UAAU,UAAU,IAAI;AAEnE,UAAM,cAAc,GAAG,KAAK,WAAW,KAAK,OAAO,KAAK,QAAQ;AAChE,UAAM,QAAQ,IAAI,cAAc,UAAU,aAAa,UAAU;AACjE,WAAO;AAAA,EACT;AACF;AACA,SAAS,gBAAgB,UAAU,MAAM;AACvC,SAAO,SAAS,QAAQ,SAAS,CAAC,GAAG,QAAQ;AAC3C,UAAM,QAAQ,KAAK,GAAG;AACtB,WAAO,SAAS,OAAO,OAAO,KAAK,IAAI,IAAI,GAAG;AAAA,EAChD,CAAC;AACH;AACA,IAAM,UAAU;AA+LhB,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,QAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,aAAW,KAAK,OAAO;AACrB,QAAI,CAAC,MAAM,SAAS,CAAC,GAAG;AACtB,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,EAAE,CAAC;AACjB,UAAM,QAAQ,EAAE,CAAC;AACjB,QAAI,SAAS,KAAK,KAAK,SAAS,KAAK,GAAG;AACtC,UAAI,CAAC,UAAU,OAAO,KAAK,GAAG;AAC5B,eAAO;AAAA,MACT;AAAA,IACF,WAAW,UAAU,OAAO;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AACA,aAAW,KAAK,OAAO;AACrB,QAAI,CAAC,MAAM,SAAS,CAAC,GAAG;AACtB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,UAAU,QAAQ,OAAO,UAAU;AAC5C;AAstBA,IAAM,mBAAmB,IAAI,KAAK,KAAK;AA6FvC,SAAS,mBAAmB,SAAS;AACnC,MAAI,WAAW,QAAQ,WAAW;AAChC,WAAO,QAAQ;AAAA,EACjB,OAAO;AACL,WAAO;AAAA,EACT;AACF;;;ACr+DA,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOd,YAAYA,OAAM,iBAAiB,MAAM;AACvC,SAAK,OAAOA;AACZ,SAAK,kBAAkB;AACvB,SAAK,OAAO;AACZ,SAAK,oBAAoB;AAIzB,SAAK,eAAe,CAAC;AACrB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,qBAAqB,MAAM;AACzB,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,mBAAmB;AACtC,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA,EACA,2BAA2B,UAAU;AACnC,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACT;AACF;AAkBA,IAAM,qBAAqB;AAsB3B,IAAM,WAAN,MAAe;AAAA,EACb,YAAYA,OAAM,WAAW;AAC3B,SAAK,OAAOA;AACZ,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,YAAY,oBAAI,IAAI;AACzB,SAAK,oBAAoB,oBAAI,IAAI;AACjC,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,kBAAkB,oBAAI,IAAI;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AAEd,UAAM,uBAAuB,KAAK,4BAA4B,UAAU;AACxE,QAAI,CAAC,KAAK,kBAAkB,IAAI,oBAAoB,GAAG;AACrD,YAAM,WAAW,IAAI,SAAS;AAC9B,WAAK,kBAAkB,IAAI,sBAAsB,QAAQ;AACzD,UAAI,KAAK,cAAc,oBAAoB,KAAK,KAAK,qBAAqB,GAAG;AAE3E,YAAI;AACF,gBAAM,WAAW,KAAK,uBAAuB;AAAA,YAC3C,oBAAoB;AAAA,UACtB,CAAC;AACD,cAAI,UAAU;AACZ,qBAAS,QAAQ,QAAQ;AAAA,UAC3B;AAAA,QACF,SAAS,GAAG;AAAA,QAGZ;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK,kBAAkB,IAAI,oBAAoB,EAAE;AAAA,EAC1D;AAAA,EACA,aAAa,SAAS;AACpB,QAAI;AAEJ,UAAM,uBAAuB,KAAK,4BAA4B,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU;AAClI,UAAM,YAAY,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC5H,QAAI,KAAK,cAAc,oBAAoB,KAAK,KAAK,qBAAqB,GAAG;AAC3E,UAAI;AACF,eAAO,KAAK,uBAAuB;AAAA,UACjC,oBAAoB;AAAA,QACtB,CAAC;AAAA,MACH,SAAS,GAAG;AACV,YAAI,UAAU;AACZ,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,OAAO;AAEL,UAAI,UAAU;AACZ,eAAO;AAAA,MACT,OAAO;AACL,cAAM,MAAM,WAAW,KAAK,IAAI,mBAAmB;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,WAAW;AACtB,QAAI,UAAU,SAAS,KAAK,MAAM;AAChC,YAAM,MAAM,yBAAyB,UAAU,IAAI,iBAAiB,KAAK,IAAI,GAAG;AAAA,IAClF;AACA,QAAI,KAAK,WAAW;AAClB,YAAM,MAAM,iBAAiB,KAAK,IAAI,4BAA4B;AAAA,IACpE;AACA,SAAK,YAAY;AAEjB,QAAI,CAAC,KAAK,qBAAqB,GAAG;AAChC;AAAA,IACF;AAEA,QAAI,iBAAiB,SAAS,GAAG;AAC/B,UAAI;AACF,aAAK,uBAAuB;AAAA,UAC1B,oBAAoB;AAAA,QACtB,CAAC;AAAA,MACH,SAAS,GAAG;AAAA,MAKZ;AAAA,IACF;AAIA,eAAW,CAAC,oBAAoB,gBAAgB,KAAK,KAAK,kBAAkB,QAAQ,GAAG;AACrF,YAAM,uBAAuB,KAAK,4BAA4B,kBAAkB;AAChF,UAAI;AAEF,cAAM,WAAW,KAAK,uBAAuB;AAAA,UAC3C,oBAAoB;AAAA,QACtB,CAAC;AACD,yBAAiB,QAAQ,QAAQ;AAAA,MACnC,SAAS,GAAG;AAAA,MAGZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,aAAa,oBAAoB;AAC7C,SAAK,kBAAkB,OAAO,UAAU;AACxC,SAAK,iBAAiB,OAAO,UAAU;AACvC,SAAK,UAAU,OAAO,UAAU;AAAA,EAClC;AAAA;AAAA;AAAA,EAGM,SAAS;AAAA;AACb,YAAM,WAAW,MAAM,KAAK,KAAK,UAAU,OAAO,CAAC;AACnD,YAAM,QAAQ,IAAI,CAAC,GAAG,SAAS,OAAO,aAAW,cAAc,OAAO,EAErE,IAAI,aAAW,QAAQ,SAAS,OAAO,CAAC,GAAG,GAAG,SAAS,OAAO,aAAW,aAAa,OAAO,EAE7F,IAAI,aAAW,QAAQ,QAAQ,CAAC,CAAC,CAAC;AAAA,IACrC;AAAA;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,cAAc,aAAa,oBAAoB;AAC7C,WAAO,KAAK,UAAU,IAAI,UAAU;AAAA,EACtC;AAAA,EACA,WAAW,aAAa,oBAAoB;AAC1C,WAAO,KAAK,iBAAiB,IAAI,UAAU,KAAK,CAAC;AAAA,EACnD;AAAA,EACA,WAAW,OAAO,CAAC,GAAG;AACpB,UAAM;AAAA,MACJ,UAAU,CAAC;AAAA,IACb,IAAI;AACJ,UAAM,uBAAuB,KAAK,4BAA4B,KAAK,kBAAkB;AACrF,QAAI,KAAK,cAAc,oBAAoB,GAAG;AAC5C,YAAM,MAAM,GAAG,KAAK,IAAI,IAAI,oBAAoB,gCAAgC;AAAA,IAClF;AACA,QAAI,CAAC,KAAK,eAAe,GAAG;AAC1B,YAAM,MAAM,aAAa,KAAK,IAAI,8BAA8B;AAAA,IAClE;AACA,UAAM,WAAW,KAAK,uBAAuB;AAAA,MAC3C,oBAAoB;AAAA,MACpB;AAAA,IACF,CAAC;AAED,eAAW,CAAC,oBAAoB,gBAAgB,KAAK,KAAK,kBAAkB,QAAQ,GAAG;AACrF,YAAM,+BAA+B,KAAK,4BAA4B,kBAAkB;AACxF,UAAI,yBAAyB,8BAA8B;AACzD,yBAAiB,QAAQ,QAAQ;AAAA,MACnC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,UAAU,YAAY;AAC3B,QAAI;AACJ,UAAM,uBAAuB,KAAK,4BAA4B,UAAU;AACxE,UAAM,qBAAqB,KAAK,KAAK,gBAAgB,IAAI,oBAAoB,OAAO,QAAQ,OAAO,SAAS,KAAK,oBAAI,IAAI;AACzH,sBAAkB,IAAI,QAAQ;AAC9B,SAAK,gBAAgB,IAAI,sBAAsB,iBAAiB;AAChE,UAAM,mBAAmB,KAAK,UAAU,IAAI,oBAAoB;AAChE,QAAI,kBAAkB;AACpB,eAAS,kBAAkB,oBAAoB;AAAA,IACjD;AACA,WAAO,MAAM;AACX,wBAAkB,OAAO,QAAQ;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,UAAU,YAAY;AAC1C,UAAM,YAAY,KAAK,gBAAgB,IAAI,UAAU;AACrD,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AACA,eAAW,YAAY,WAAW;AAChC,UAAI;AACF,iBAAS,UAAU,UAAU;AAAA,MAC/B,SAAS,IAAI;AAAA,MAEb;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB;AAAA,IACrB;AAAA,IACA,UAAU,CAAC;AAAA,EACb,GAAG;AACD,QAAI,WAAW,KAAK,UAAU,IAAI,kBAAkB;AACpD,QAAI,CAAC,YAAY,KAAK,WAAW;AAC/B,iBAAW,KAAK,UAAU,gBAAgB,KAAK,WAAW;AAAA,QACxD,oBAAoB,8BAA8B,kBAAkB;AAAA,QACpE;AAAA,MACF,CAAC;AACD,WAAK,UAAU,IAAI,oBAAoB,QAAQ;AAC/C,WAAK,iBAAiB,IAAI,oBAAoB,OAAO;AAMrD,WAAK,sBAAsB,UAAU,kBAAkB;AAMvD,UAAI,KAAK,UAAU,mBAAmB;AACpC,YAAI;AACF,eAAK,UAAU,kBAAkB,KAAK,WAAW,oBAAoB,QAAQ;AAAA,QAC/E,SAAS,IAAI;AAAA,QAEb;AAAA,MACF;AAAA,IACF;AACA,WAAO,YAAY;AAAA,EACrB;AAAA,EACA,4BAA4B,aAAa,oBAAoB;AAC3D,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,UAAU,oBAAoB,aAAa;AAAA,IACzD,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,WAAO,CAAC,CAAC,KAAK,aAAa,KAAK,UAAU,sBAAsB;AAAA,EAClE;AACF;AAEA,SAAS,8BAA8B,YAAY;AACjD,SAAO,eAAe,qBAAqB,SAAY;AACzD;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,UAAU,sBAAsB;AACzC;AAqBA,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAYA,OAAM;AAChB,SAAK,OAAOA;AACZ,SAAK,YAAY,oBAAI,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,WAAW;AACtB,UAAM,WAAW,KAAK,YAAY,UAAU,IAAI;AAChD,QAAI,SAAS,eAAe,GAAG;AAC7B,YAAM,IAAI,MAAM,aAAa,UAAU,IAAI,qCAAqC,KAAK,IAAI,EAAE;AAAA,IAC7F;AACA,aAAS,aAAa,SAAS;AAAA,EACjC;AAAA,EACA,wBAAwB,WAAW;AACjC,UAAM,WAAW,KAAK,YAAY,UAAU,IAAI;AAChD,QAAI,SAAS,eAAe,GAAG;AAE7B,WAAK,UAAU,OAAO,UAAU,IAAI;AAAA,IACtC;AACA,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAYA,OAAM;AAChB,QAAI,KAAK,UAAU,IAAIA,KAAI,GAAG;AAC5B,aAAO,KAAK,UAAU,IAAIA,KAAI;AAAA,IAChC;AAEA,UAAM,WAAW,IAAI,SAASA,OAAM,IAAI;AACxC,SAAK,UAAU,IAAIA,OAAM,QAAQ;AACjC,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,WAAO,MAAM,KAAK,KAAK,UAAU,OAAO,CAAC;AAAA,EAC3C;AACF;;;ACvXA,IAAM,YAAY,CAAC;AAYnB,IAAI;AAAA,CACH,SAAUC,WAAU;AACnB,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,EAAAA,UAASA,UAAS,SAAS,IAAI,CAAC,IAAI;AACpC,EAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,EAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,EAAAA,UAASA,UAAS,QAAQ,IAAI,CAAC,IAAI;AACrC,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAM,oBAAoB;AAAA,EACxB,SAAS,SAAS;AAAA,EAClB,WAAW,SAAS;AAAA,EACpB,QAAQ,SAAS;AAAA,EACjB,QAAQ,SAAS;AAAA,EACjB,SAAS,SAAS;AAAA,EAClB,UAAU,SAAS;AACrB;AAIA,IAAM,kBAAkB,SAAS;AAOjC,IAAM,gBAAgB;AAAA,EACpB,CAAC,SAAS,KAAK,GAAG;AAAA,EAClB,CAAC,SAAS,OAAO,GAAG;AAAA,EACpB,CAAC,SAAS,IAAI,GAAG;AAAA,EACjB,CAAC,SAAS,IAAI,GAAG;AAAA,EACjB,CAAC,SAAS,KAAK,GAAG;AACpB;AAMA,IAAM,oBAAoB,CAAC,UAAU,YAAY,SAAS;AACxD,MAAI,UAAU,SAAS,UAAU;AAC/B;AAAA,EACF;AACA,QAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AACnC,QAAM,SAAS,cAAc,OAAO;AACpC,MAAI,QAAQ;AACV,YAAQ,MAAM,EAAE,IAAI,GAAG,MAAM,SAAS,IAAI,KAAK,GAAG,IAAI;AAAA,EACxD,OAAO;AACL,UAAM,IAAI,MAAM,8DAA8D,OAAO,GAAG;AAAA,EAC1F;AACF;AACA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOX,YAAYC,OAAM;AAChB,SAAK,OAAOA;AAIZ,SAAK,YAAY;AAKjB,SAAK,cAAc;AAInB,SAAK,kBAAkB;AAIvB,cAAU,KAAK,IAAI;AAAA,EACrB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,QAAI,EAAE,OAAO,WAAW;AACtB,YAAM,IAAI,UAAU,kBAAkB,GAAG,4BAA4B;AAAA,IACvE;AACA,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,YAAY,KAAK;AACf,SAAK,YAAY,OAAO,QAAQ,WAAW,kBAAkB,GAAG,IAAI;AAAA,EACtE;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,KAAK;AAClB,QAAI,OAAO,QAAQ,YAAY;AAC7B,YAAM,IAAI,UAAU,mDAAmD;AAAA,IACzE;AACA,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe,KAAK;AACtB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,MAAM;AACb,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,OAAO,GAAG,IAAI;AAC1E,SAAK,YAAY,MAAM,SAAS,OAAO,GAAG,IAAI;AAAA,EAChD;AAAA,EACA,OAAO,MAAM;AACX,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,SAAS,GAAG,IAAI;AAC5E,SAAK,YAAY,MAAM,SAAS,SAAS,GAAG,IAAI;AAAA,EAClD;AAAA,EACA,QAAQ,MAAM;AACZ,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,MAAM,GAAG,IAAI;AACzE,SAAK,YAAY,MAAM,SAAS,MAAM,GAAG,IAAI;AAAA,EAC/C;AAAA,EACA,QAAQ,MAAM;AACZ,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,MAAM,GAAG,IAAI;AACzE,SAAK,YAAY,MAAM,SAAS,MAAM,GAAG,IAAI;AAAA,EAC/C;AAAA,EACA,SAAS,MAAM;AACb,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,OAAO,GAAG,IAAI;AAC1E,SAAK,YAAY,MAAM,SAAS,OAAO,GAAG,IAAI;AAAA,EAChD;AACF;;;AChKA,IAAM,gBAAgB,CAAC,QAAQ,iBAAiB,aAAa,KAAK,OAAK,kBAAkB,CAAC;AAC1F,IAAI;AACJ,IAAI;AAEJ,SAAS,uBAAuB;AAC9B,SAAO,sBAAsB,oBAAoB,CAAC,aAAa,gBAAgB,UAAU,WAAW,cAAc;AACpH;AAEA,SAAS,0BAA0B;AACjC,SAAO,yBAAyB,uBAAuB,CAAC,UAAU,UAAU,SAAS,UAAU,UAAU,UAAU,UAAU,UAAU,kBAAkB;AAC3J;AACA,IAAM,mBAAmB,oBAAI,QAAQ;AACrC,IAAM,qBAAqB,oBAAI,QAAQ;AACvC,IAAM,2BAA2B,oBAAI,QAAQ;AAC7C,IAAM,iBAAiB,oBAAI,QAAQ;AACnC,IAAM,wBAAwB,oBAAI,QAAQ;AAC1C,SAAS,iBAAiB,SAAS;AACjC,QAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,UAAM,WAAW,MAAM;AACrB,cAAQ,oBAAoB,WAAW,OAAO;AAC9C,cAAQ,oBAAoB,SAAS,KAAK;AAAA,IAC5C;AACA,UAAM,UAAU,MAAM;AACpB,cAAQ,KAAK,QAAQ,MAAM,CAAC;AAC5B,eAAS;AAAA,IACX;AACA,UAAM,QAAQ,MAAM;AAClB,aAAO,QAAQ,KAAK;AACpB,eAAS;AAAA,IACX;AACA,YAAQ,iBAAiB,WAAW,OAAO;AAC3C,YAAQ,iBAAiB,SAAS,KAAK;AAAA,EACzC,CAAC;AACD,UAAQ,KAAK,WAAS;AAGpB,QAAI,iBAAiB,WAAW;AAC9B,uBAAiB,IAAI,OAAO,OAAO;AAAA,IACrC;AAAA,EAEF,CAAC,EAAE,MAAM,MAAM;AAAA,EAAC,CAAC;AAGjB,wBAAsB,IAAI,SAAS,OAAO;AAC1C,SAAO;AACT;AACA,SAAS,+BAA+B,IAAI;AAE1C,MAAI,mBAAmB,IAAI,EAAE,EAAG;AAChC,QAAM,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC5C,UAAM,WAAW,MAAM;AACrB,SAAG,oBAAoB,YAAY,QAAQ;AAC3C,SAAG,oBAAoB,SAAS,KAAK;AACrC,SAAG,oBAAoB,SAAS,KAAK;AAAA,IACvC;AACA,UAAM,WAAW,MAAM;AACrB,cAAQ;AACR,eAAS;AAAA,IACX;AACA,UAAM,QAAQ,MAAM;AAClB,aAAO,GAAG,SAAS,IAAI,aAAa,cAAc,YAAY,CAAC;AAC/D,eAAS;AAAA,IACX;AACA,OAAG,iBAAiB,YAAY,QAAQ;AACxC,OAAG,iBAAiB,SAAS,KAAK;AAClC,OAAG,iBAAiB,SAAS,KAAK;AAAA,EACpC,CAAC;AAED,qBAAmB,IAAI,IAAI,IAAI;AACjC;AACA,IAAI,gBAAgB;AAAA,EAClB,IAAI,QAAQ,MAAM,UAAU;AAC1B,QAAI,kBAAkB,gBAAgB;AAEpC,UAAI,SAAS,OAAQ,QAAO,mBAAmB,IAAI,MAAM;AAEzD,UAAI,SAAS,oBAAoB;AAC/B,eAAO,OAAO,oBAAoB,yBAAyB,IAAI,MAAM;AAAA,MACvE;AAEA,UAAI,SAAS,SAAS;AACpB,eAAO,SAAS,iBAAiB,CAAC,IAAI,SAAY,SAAS,YAAY,SAAS,iBAAiB,CAAC,CAAC;AAAA,MACrG;AAAA,IACF;AAEA,WAAO,KAAK,OAAO,IAAI,CAAC;AAAA,EAC1B;AAAA,EACA,IAAI,QAAQ,MAAM,OAAO;AACvB,WAAO,IAAI,IAAI;AACf,WAAO;AAAA,EACT;AAAA,EACA,IAAI,QAAQ,MAAM;AAChB,QAAI,kBAAkB,mBAAmB,SAAS,UAAU,SAAS,UAAU;AAC7E,aAAO;AAAA,IACT;AACA,WAAO,QAAQ;AAAA,EACjB;AACF;AACA,SAAS,aAAa,UAAU;AAC9B,kBAAgB,SAAS,aAAa;AACxC;AACA,SAAS,aAAa,MAAM;AAI1B,MAAI,SAAS,YAAY,UAAU,eAAe,EAAE,sBAAsB,eAAe,YAAY;AACnG,WAAO,SAAU,eAAe,MAAM;AACpC,YAAM,KAAK,KAAK,KAAK,OAAO,IAAI,GAAG,YAAY,GAAG,IAAI;AACtD,+BAAyB,IAAI,IAAI,WAAW,OAAO,WAAW,KAAK,IAAI,CAAC,UAAU,CAAC;AACnF,aAAO,KAAK,EAAE;AAAA,IAChB;AAAA,EACF;AAMA,MAAI,wBAAwB,EAAE,SAAS,IAAI,GAAG;AAC5C,WAAO,YAAa,MAAM;AAGxB,WAAK,MAAM,OAAO,IAAI,GAAG,IAAI;AAC7B,aAAO,KAAK,iBAAiB,IAAI,IAAI,CAAC;AAAA,IACxC;AAAA,EACF;AACA,SAAO,YAAa,MAAM;AAGxB,WAAO,KAAK,KAAK,MAAM,OAAO,IAAI,GAAG,IAAI,CAAC;AAAA,EAC5C;AACF;AACA,SAAS,uBAAuB,OAAO;AACrC,MAAI,OAAO,UAAU,WAAY,QAAO,aAAa,KAAK;AAG1D,MAAI,iBAAiB,eAAgB,gCAA+B,KAAK;AACzE,MAAI,cAAc,OAAO,qBAAqB,CAAC,EAAG,QAAO,IAAI,MAAM,OAAO,aAAa;AAEvF,SAAO;AACT;AACA,SAAS,KAAK,OAAO;AAGnB,MAAI,iBAAiB,WAAY,QAAO,iBAAiB,KAAK;AAG9D,MAAI,eAAe,IAAI,KAAK,EAAG,QAAO,eAAe,IAAI,KAAK;AAC9D,QAAM,WAAW,uBAAuB,KAAK;AAG7C,MAAI,aAAa,OAAO;AACtB,mBAAe,IAAI,OAAO,QAAQ;AAClC,0BAAsB,IAAI,UAAU,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,IAAM,SAAS,WAAS,sBAAsB,IAAI,KAAK;;;AClJvD,SAAS,OAAOC,OAAMC,UAAS;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,UAAU,KAAKD,OAAMC,QAAO;AAC5C,QAAM,cAAc,KAAK,OAAO;AAChC,MAAI,SAAS;AACX,YAAQ,iBAAiB,iBAAiB,WAAS;AACjD,cAAQ,KAAK,QAAQ,MAAM,GAAG,MAAM,YAAY,MAAM,YAAY,KAAK,QAAQ,WAAW,GAAG,KAAK;AAAA,IACpG,CAAC;AAAA,EACH;AACA,MAAI,SAAS;AACX,YAAQ,iBAAiB,WAAW,WAAS;AAAA;AAAA,MAE7C,MAAM;AAAA,MAAY,MAAM;AAAA,MAAY;AAAA,IAAK,CAAC;AAAA,EAC5C;AACA,cAAY,KAAK,QAAM;AACrB,QAAI,WAAY,IAAG,iBAAiB,SAAS,MAAM,WAAW,CAAC;AAC/D,QAAI,UAAU;AACZ,SAAG,iBAAiB,iBAAiB,WAAS,SAAS,MAAM,YAAY,MAAM,YAAY,KAAK,CAAC;AAAA,IACnG;AAAA,EACF,CAAC,EAAE,MAAM,MAAM;AAAA,EAAC,CAAC;AACjB,SAAO;AACT;AAMA,SAAS,SAASD,OAAM;AAAA,EACtB;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,UAAU,eAAeA,KAAI;AAC7C,MAAI,SAAS;AACX,YAAQ,iBAAiB,WAAW,WAAS;AAAA;AAAA,MAE7C,MAAM;AAAA,MAAY;AAAA,IAAK,CAAC;AAAA,EAC1B;AACA,SAAO,KAAK,OAAO,EAAE,KAAK,MAAM,MAAS;AAC3C;AACA,IAAM,cAAc,CAAC,OAAO,UAAU,UAAU,cAAc,OAAO;AACrE,IAAM,eAAe,CAAC,OAAO,OAAO,UAAU,OAAO;AACrD,IAAM,gBAAgB,oBAAI,IAAI;AAC9B,SAAS,UAAU,QAAQ,MAAM;AAC/B,MAAI,EAAE,kBAAkB,eAAe,EAAE,QAAQ,WAAW,OAAO,SAAS,WAAW;AACrF;AAAA,EACF;AACA,MAAI,cAAc,IAAI,IAAI,EAAG,QAAO,cAAc,IAAI,IAAI;AAC1D,QAAM,iBAAiB,KAAK,QAAQ,cAAc,EAAE;AACpD,QAAM,WAAW,SAAS;AAC1B,QAAM,UAAU,aAAa,SAAS,cAAc;AACpD;AAAA;AAAA,IAEA,EAAE,mBAAmB,WAAW,WAAW,gBAAgB,cAAc,EAAE,WAAW,YAAY,SAAS,cAAc;AAAA,IAAI;AAC3H;AAAA,EACF;AACA,QAAM,SAAS,SAAgB,cAAc,MAAM;AAAA;AAEjD,YAAM,KAAK,KAAK,YAAY,WAAW,UAAU,cAAc,UAAU;AACzE,UAAIE,UAAS,GAAG;AAChB,UAAI,SAAU,CAAAA,UAASA,QAAO,MAAM,KAAK,MAAM,CAAC;AAMhD,cAAQ,MAAM,QAAQ,IAAI,CAACA,QAAO,cAAc,EAAE,GAAG,IAAI,GAAG,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC;AAAA,IACrF;AAAA;AACA,gBAAc,IAAI,MAAM,MAAM;AAC9B,SAAO;AACT;AACA,aAAa,cAAa,iCACrB,WADqB;AAAA,EAExB,KAAK,CAAC,QAAQ,MAAM,aAAa,UAAU,QAAQ,IAAI,KAAK,SAAS,IAAI,QAAQ,MAAM,QAAQ;AAAA,EAC/F,KAAK,CAAC,QAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,IAAI,KAAK,SAAS,IAAI,QAAQ,IAAI;AAC/E,EAAE;;;ACjEF,IAAM,4BAAN,MAAgC;AAAA,EAC9B,YAAY,WAAW;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA,EAGA,wBAAwB;AACtB,UAAM,YAAY,KAAK,UAAU,aAAa;AAG9C,WAAO,UAAU,IAAI,cAAY;AAC/B,UAAI,yBAAyB,QAAQ,GAAG;AACtC,cAAM,UAAU,SAAS,aAAa;AACtC,eAAO,GAAG,QAAQ,OAAO,IAAI,QAAQ,OAAO;AAAA,MAC9C,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC,EAAE,OAAO,eAAa,SAAS,EAAE,KAAK,GAAG;AAAA,EAC5C;AACF;AASA,SAAS,yBAAyB,UAAU;AAC1C,QAAM,YAAY,SAAS,aAAa;AACxC,UAAQ,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,UAAU;AACpF;AACA,IAAM,SAAS;AACf,IAAM,YAAY;AAkBlB,IAAM,SAAS,IAAI,OAAO,eAAe;AACzC,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,OAAO;AAwBb,IAAMC,sBAAqB;AAC3B,IAAM,sBAAsB;AAAA,EAC1B,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,WAAW;AAAA;AAAA,EAEX,CAAC,IAAI,GAAG;AACV;AAqBA,IAAM,QAAQ,oBAAI,IAAI;AAItB,IAAM,cAAc,oBAAI,IAAI;AAO5B,IAAM,cAAc,oBAAI,IAAI;AAM5B,SAAS,cAAc,KAAK,WAAW;AACrC,MAAI;AACF,QAAI,UAAU,aAAa,SAAS;AAAA,EACtC,SAAS,GAAG;AACV,WAAO,MAAM,aAAa,UAAU,IAAI,wCAAwC,IAAI,IAAI,IAAI,CAAC;AAAA,EAC/F;AACF;AAeA,SAAS,mBAAmB,WAAW;AACrC,QAAM,gBAAgB,UAAU;AAChC,MAAI,YAAY,IAAI,aAAa,GAAG;AAClC,WAAO,MAAM,sDAAsD,aAAa,GAAG;AACnF,WAAO;AAAA,EACT;AACA,cAAY,IAAI,eAAe,SAAS;AAExC,aAAW,OAAO,MAAM,OAAO,GAAG;AAChC,kBAAc,KAAK,SAAS;AAAA,EAC9B;AACA,aAAW,aAAa,YAAY,OAAO,GAAG;AAC5C,kBAAc,WAAW,SAAS;AAAA,EACpC;AACA,SAAO;AACT;AAUA,SAAS,aAAa,KAAKC,OAAM;AAC/B,QAAM,sBAAsB,IAAI,UAAU,YAAY,WAAW,EAAE,aAAa;AAAA,IAC9E,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,qBAAqB;AACvB,SAAK,oBAAoB,iBAAiB;AAAA,EAC5C;AACA,SAAO,IAAI,UAAU,YAAYA,KAAI;AACvC;AA8DA,IAAM,SAAS;AAAA,EACb;AAAA,IAAC;AAAA;AAAA,EAA8B,GAAG;AAAA,EAClC;AAAA,IAAC;AAAA;AAAA,EAA0C,GAAG;AAAA,EAC9C;AAAA,IAAC;AAAA;AAAA,EAA4C,GAAG;AAAA,EAChD;AAAA,IAAC;AAAA;AAAA,EAAwC,GAAG;AAAA,EAC5C;AAAA,IAAC;AAAA;AAAA,EAAsD,GAAG;AAAA,EAC1D;AAAA,IAAC;AAAA;AAAA,EAAsC,GAAG;AAAA,EAC1C;AAAA,IAAC;AAAA;AAAA,EAA0D,GAAG;AAAA,EAC9D;AAAA,IAAC;AAAA;AAAA,EAA0D,GAAG;AAAA,EAC9D;AAAA,IAAC;AAAA;AAAA,EAAkC,GAAG;AAAA,EACtC;AAAA,IAAC;AAAA;AAAA,EAAgC,GAAG;AAAA,EACpC;AAAA,IAAC;AAAA;AAAA,EAAkC,GAAG;AAAA,EACtC;AAAA,IAAC;AAAA;AAAA,EAAsC,GAAG;AAAA,EAC1C;AAAA,IAAC;AAAA;AAAA,EAAwF,GAAG;AAAA,EAC5F;AAAA,IAAC;AAAA;AAAA,EAA8E,GAAG;AACpF;AACA,IAAM,gBAAgB,IAAI,aAAa,OAAO,YAAY,MAAM;AAkBhE,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,SAAS,QAAQ,WAAW;AACtC,SAAK,aAAa;AAClB,SAAK,WAAW,OAAO,OAAO,CAAC,GAAG,OAAO;AACzC,SAAK,UAAU,OAAO,OAAO,CAAC,GAAG,MAAM;AACvC,SAAK,QAAQ,OAAO;AACpB,SAAK,kCAAkC,OAAO;AAC9C,SAAK,aAAa;AAClB,SAAK,UAAU,aAAa,IAAI;AAAA,MAAU;AAAA,MAAO,MAAM;AAAA,MAAM;AAAA;AAAA,IAAmC,CAAC;AAAA,EACnG;AAAA,EACA,IAAI,iCAAiC;AACnC,SAAK,eAAe;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,+BAA+B,KAAK;AACtC,SAAK,eAAe;AACpB,SAAK,kCAAkC;AAAA,EACzC;AAAA,EACA,IAAI,OAAO;AACT,SAAK,eAAe;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU;AACZ,SAAK,eAAe;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS;AACX,SAAK,eAAe;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,KAAK;AACjB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,QAAI,KAAK,WAAW;AAClB,YAAM,cAAc,OAAO,eAA0C;AAAA,QACnE,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAoJA,SAAS,cAAc,UAAU,YAAY,CAAC,GAAG;AAC/C,MAAI,UAAU;AACd,MAAI,OAAO,cAAc,UAAU;AACjC,UAAMC,QAAO;AACb,gBAAY;AAAA,MACV,MAAAA;AAAA,IACF;AAAA,EACF;AACA,QAAM,SAAS,OAAO,OAAO;AAAA,IAC3B,MAAMC;AAAA,IACN,gCAAgC;AAAA,EAClC,GAAG,SAAS;AACZ,QAAMD,QAAO,OAAO;AACpB,MAAI,OAAOA,UAAS,YAAY,CAACA,OAAM;AACrC,UAAM,cAAc,OAAO,gBAA4C;AAAA,MACrE,SAAS,OAAOA,KAAI;AAAA,IACtB,CAAC;AAAA,EACH;AACA,cAAY,UAAU,oBAAoB;AAC1C,MAAI,CAAC,SAAS;AACZ,UAAM,cAAc;AAAA,MAAO;AAAA;AAAA,IAAsC;AAAA,EACnE;AACA,QAAM,cAAc,MAAM,IAAIA,KAAI;AAClC,MAAI,aAAa;AAEf,QAAI,UAAU,SAAS,YAAY,OAAO,KAAK,UAAU,QAAQ,YAAY,MAAM,GAAG;AACpF,aAAO;AAAA,IACT,OAAO;AACL,YAAM,cAAc,OAAO,iBAA8C;AAAA,QACvE,SAASA;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,YAAY,IAAI,mBAAmBA,KAAI;AAC7C,aAAW,aAAa,YAAY,OAAO,GAAG;AAC5C,cAAU,aAAa,SAAS;AAAA,EAClC;AACA,QAAM,SAAS,IAAI,gBAAgB,SAAS,QAAQ,SAAS;AAC7D,QAAM,IAAIA,OAAM,MAAM;AACtB,SAAO;AACT;AAyEA,SAAS,OAAOE,QAAOC,qBAAoB;AACzC,QAAM,MAAM,MAAM,IAAID,KAAI;AAC1B,MAAI,CAAC,OAAOA,UAASC,uBAAsB,oBAAoB,GAAG;AAChE,WAAO,cAAc;AAAA,EACvB;AACA,MAAI,CAAC,KAAK;AACR,UAAM,cAAc,OAAO,UAAgC;AAAA,MACzD,SAASD;AAAA,IACX,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAmDA,SAAS,gBAAgB,kBAAkBE,UAAS,SAAS;AAC3D,MAAI;AAGJ,MAAI,WAAW,KAAK,oBAAoB,gBAAgB,OAAO,QAAQ,OAAO,SAAS,KAAK;AAC5F,MAAI,SAAS;AACX,eAAW,IAAI,OAAO;AAAA,EACxB;AACA,QAAM,kBAAkB,QAAQ,MAAM,OAAO;AAC7C,QAAM,kBAAkBA,SAAQ,MAAM,OAAO;AAC7C,MAAI,mBAAmB,iBAAiB;AACtC,UAAM,UAAU,CAAC,+BAA+B,OAAO,mBAAmBA,QAAO,IAAI;AACrF,QAAI,iBAAiB;AACnB,cAAQ,KAAK,iBAAiB,OAAO,mDAAmD;AAAA,IAC1F;AACA,QAAI,mBAAmB,iBAAiB;AACtC,cAAQ,KAAK,KAAK;AAAA,IACpB;AACA,QAAI,iBAAiB;AACnB,cAAQ,KAAK,iBAAiBA,QAAO,mDAAmD;AAAA,IAC1F;AACA,WAAO,KAAK,QAAQ,KAAK,GAAG,CAAC;AAC7B;AAAA,EACF;AACA,qBAAmB,IAAI;AAAA,IAAU,GAAG,OAAO;AAAA,IAAY,OAAO;AAAA,MAC5D;AAAA,MACA,SAAAA;AAAA,IACF;AAAA,IAAI;AAAA;AAAA,EAAqC,CAAC;AAC5C;AA2CA,IAAM,UAAU;AAChB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAI,YAAY;AAChB,SAAS,eAAe;AACtB,MAAI,CAAC,WAAW;AACd,gBAAY,OAAO,SAAS,YAAY;AAAA,MACtC,SAAS,CAAC,IAAI,eAAe;AAM3B,gBAAQ,YAAY;AAAA,UAClB,KAAK;AACH,gBAAI;AACF,iBAAG,kBAAkB,UAAU;AAAA,YACjC,SAAS,GAAG;AAIV,sBAAQ,KAAK,CAAC;AAAA,YAChB;AAAA,QACJ;AAAA,MACF;AAAA,IACF,CAAC,EAAE,MAAM,OAAK;AACZ,YAAM,cAAc,OAAO,YAAoC;AAAA,QAC7D,sBAAsB,EAAE;AAAA,MAC1B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAe,4BAA4B,KAAK;AAAA;AAC9C,QAAI;AACF,YAAM,KAAK,MAAM,aAAa;AAC9B,YAAM,KAAK,GAAG,YAAY,UAAU;AACpC,YAAM,SAAS,MAAM,GAAG,YAAY,UAAU,EAAE,IAAI,WAAW,GAAG,CAAC;AAGnE,YAAM,GAAG;AACT,aAAO;AAAA,IACT,SAAS,GAAG;AACV,UAAI,aAAa,eAAe;AAC9B,eAAO,KAAK,EAAE,OAAO;AAAA,MACvB,OAAO;AACL,cAAM,cAAc,cAAc,OAAO,WAAkC;AAAA,UACzE,sBAAsB,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE;AAAA,QAChE,CAAC;AACD,eAAO,KAAK,YAAY,OAAO;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AACA,SAAe,2BAA2B,KAAK,iBAAiB;AAAA;AAC9D,QAAI;AACF,YAAM,KAAK,MAAM,aAAa;AAC9B,YAAM,KAAK,GAAG,YAAY,YAAY,WAAW;AACjD,YAAM,cAAc,GAAG,YAAY,UAAU;AAC7C,YAAM,YAAY,IAAI,iBAAiB,WAAW,GAAG,CAAC;AACtD,YAAM,GAAG;AAAA,IACX,SAAS,GAAG;AACV,UAAI,aAAa,eAAe;AAC9B,eAAO,KAAK,EAAE,OAAO;AAAA,MACvB,OAAO;AACL,cAAM,cAAc,cAAc,OAAO,WAAoC;AAAA,UAC3E,sBAAsB,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE;AAAA,QAChE,CAAC;AACD,eAAO,KAAK,YAAY,OAAO;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,GAAG,IAAI,IAAI,IAAI,IAAI,QAAQ,KAAK;AACzC;AAkBA,IAAM,mBAAmB;AACzB,IAAM,4BAA4B;AAClC,IAAM,uBAAN,MAA2B;AAAA,EACzB,YAAY,WAAW;AACrB,SAAK,YAAY;AAUjB,SAAK,mBAAmB;AACxB,UAAM,MAAM,KAAK,UAAU,YAAY,KAAK,EAAE,aAAa;AAC3D,SAAK,WAAW,IAAI,qBAAqB,GAAG;AAC5C,SAAK,0BAA0B,KAAK,SAAS,KAAK,EAAE,KAAK,YAAU;AACjE,WAAK,mBAAmB;AACxB,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,mBAAmB;AAAA;AACvB,UAAI,IAAI;AACR,UAAI;AACF,cAAM,iBAAiB,KAAK,UAAU,YAAY,iBAAiB,EAAE,aAAa;AAGlF,cAAM,QAAQ,eAAe,sBAAsB;AACnD,cAAM,OAAO,iBAAiB;AAC9B,cAAM,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,MAAM;AAC7F,eAAK,mBAAmB,MAAM,KAAK;AAEnC,gBAAM,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,MAAM;AAC7F;AAAA,UACF;AAAA,QACF;AAGA,YAAI,KAAK,iBAAiB,0BAA0B,QAAQ,KAAK,iBAAiB,WAAW,KAAK,yBAAuB,oBAAoB,SAAS,IAAI,GAAG;AAC3J;AAAA,QACF,OAAO;AAEL,eAAK,iBAAiB,WAAW,KAAK;AAAA,YACpC;AAAA,YACA;AAAA,UACF,CAAC;AAGD,cAAI,KAAK,iBAAiB,WAAW,SAAS,2BAA2B;AACvE,kBAAM,uBAAuB,wBAAwB,KAAK,iBAAiB,UAAU;AACrF,iBAAK,iBAAiB,WAAW,OAAO,sBAAsB,CAAC;AAAA,UACjE;AAAA,QACF;AACA,eAAO,KAAK,SAAS,UAAU,KAAK,gBAAgB;AAAA,MACtD,SAAS,GAAG;AACV,eAAO,KAAK,CAAC;AAAA,MACf;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,sBAAsB;AAAA;AAC1B,UAAI;AACJ,UAAI;AACF,YAAI,KAAK,qBAAqB,MAAM;AAClC,gBAAM,KAAK;AAAA,QACb;AAEA,cAAM,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,QAAQ,KAAK,iBAAiB,WAAW,WAAW,GAAG;AAC9I,iBAAO;AAAA,QACT;AACA,cAAM,OAAO,iBAAiB;AAE9B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,2BAA2B,KAAK,iBAAiB,UAAU;AAC/D,cAAM,eAAe,8BAA8B,KAAK,UAAU;AAAA,UAChE,SAAS;AAAA,UACT,YAAY;AAAA,QACd,CAAC,CAAC;AAEF,aAAK,iBAAiB,wBAAwB;AAC9C,YAAI,cAAc,SAAS,GAAG;AAE5B,eAAK,iBAAiB,aAAa;AAInC,gBAAM,KAAK,SAAS,UAAU,KAAK,gBAAgB;AAAA,QACrD,OAAO;AACL,eAAK,iBAAiB,aAAa,CAAC;AAEpC,eAAK,KAAK,SAAS,UAAU,KAAK,gBAAgB;AAAA,QACpD;AACA,eAAO;AAAA,MACT,SAAS,GAAG;AACV,eAAO,KAAK,CAAC;AACb,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AACF;AACA,SAAS,mBAAmB;AAC1B,QAAM,QAAQ,oBAAI,KAAK;AAEvB,SAAO,MAAM,YAAY,EAAE,UAAU,GAAG,EAAE;AAC5C;AACA,SAAS,2BAA2B,iBAAiB,UAAU,kBAAkB;AAG/E,QAAM,mBAAmB,CAAC;AAE1B,MAAI,gBAAgB,gBAAgB,MAAM;AAC1C,aAAW,uBAAuB,iBAAiB;AAEjD,UAAM,iBAAiB,iBAAiB,KAAK,QAAM,GAAG,UAAU,oBAAoB,KAAK;AACzF,QAAI,CAAC,gBAAgB;AAEnB,uBAAiB,KAAK;AAAA,QACpB,OAAO,oBAAoB;AAAA,QAC3B,OAAO,CAAC,oBAAoB,IAAI;AAAA,MAClC,CAAC;AACD,UAAI,WAAW,gBAAgB,IAAI,SAAS;AAG1C,yBAAiB,IAAI;AACrB;AAAA,MACF;AAAA,IACF,OAAO;AACL,qBAAe,MAAM,KAAK,oBAAoB,IAAI;AAGlD,UAAI,WAAW,gBAAgB,IAAI,SAAS;AAC1C,uBAAe,MAAM,IAAI;AACzB;AAAA,MACF;AAAA,IACF;AAGA,oBAAgB,cAAc,MAAM,CAAC;AAAA,EACvC;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,uBAAN,MAA2B;AAAA,EACzB,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,0BAA0B,KAAK,6BAA6B;AAAA,EACnE;AAAA,EACM,+BAA+B;AAAA;AACnC,UAAI,CAAC,qBAAqB,GAAG;AAC3B,eAAO;AAAA,MACT,OAAO;AACL,eAAO,0BAA0B,EAAE,KAAK,MAAM,IAAI,EAAE,MAAM,MAAM,KAAK;AAAA,MACvE;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,OAAO;AAAA;AACX,YAAM,kBAAkB,MAAM,KAAK;AACnC,UAAI,CAAC,iBAAiB;AACpB,eAAO;AAAA,UACL,YAAY,CAAC;AAAA,QACf;AAAA,MACF,OAAO;AACL,cAAM,qBAAqB,MAAM,4BAA4B,KAAK,GAAG;AACrE,YAAI,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,YAAY;AACzG,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,YACL,YAAY,CAAC;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA,EAEM,UAAU,kBAAkB;AAAA;AAChC,UAAI;AACJ,YAAM,kBAAkB,MAAM,KAAK;AACnC,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF,OAAO;AACL,cAAM,2BAA2B,MAAM,KAAK,KAAK;AACjD,eAAO,2BAA2B,KAAK,KAAK;AAAA,UAC1C,wBAAwB,KAAK,iBAAiB,2BAA2B,QAAQ,OAAO,SAAS,KAAK,yBAAyB;AAAA,UAC/H,YAAY,iBAAiB;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;AAAA,EAEM,IAAI,kBAAkB;AAAA;AAC1B,UAAI;AACJ,YAAM,kBAAkB,MAAM,KAAK;AACnC,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF,OAAO;AACL,cAAM,2BAA2B,MAAM,KAAK,KAAK;AACjD,eAAO,2BAA2B,KAAK,KAAK;AAAA,UAC1C,wBAAwB,KAAK,iBAAiB,2BAA2B,QAAQ,OAAO,SAAS,KAAK,yBAAyB;AAAA,UAC/H,YAAY,CAAC,GAAG,yBAAyB,YAAY,GAAG,iBAAiB,UAAU;AAAA,QACrF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AACF;AAMA,SAAS,WAAW,iBAAiB;AAEnC,SAAO;AAAA;AAAA,IAEP,KAAK,UAAU;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,IACd,CAAC;AAAA,EAAC,EAAE;AACN;AAKA,SAAS,wBAAwB,YAAY;AAC3C,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,MAAI,uBAAuB;AAC3B,MAAI,wBAAwB,WAAW,CAAC,EAAE;AAC1C,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,QAAI,WAAW,CAAC,EAAE,OAAO,uBAAuB;AAC9C,8BAAwB,WAAW,CAAC,EAAE;AACtC,6BAAuB;AAAA,IACzB;AAAA,EACF;AACA,SAAO;AACT;AAkBA,SAAS,uBAAuB,SAAS;AACvC,qBAAmB,IAAI;AAAA,IAAU;AAAA,IAAmB,eAAa,IAAI,0BAA0B,SAAS;AAAA,IAAG;AAAA;AAAA,EAAqC,CAAC;AACjJ,qBAAmB,IAAI;AAAA,IAAU;AAAA,IAAa,eAAa,IAAI,qBAAqB,SAAS;AAAA,IAAG;AAAA;AAAA,EAAqC,CAAC;AAEtI,kBAAgB,QAAQ,WAAW,OAAO;AAE1C,kBAAgB,QAAQ,WAAW,SAAS;AAE5C,kBAAgB,WAAW,EAAE;AAC/B;AAQA,uBAAuB,EAAE;;;ACtoCzB,IAAMC,QAAO;AACb,IAAM,UAAU;AAkBhB,IAAM,qBAAqB;AAC3B,IAAM,kBAAkB,KAAK,OAAO;AACpC,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAC9B,IAAM,0BAA0B,KAAK,KAAK;AAC1C,IAAM,UAAU;AAChB,IAAM,eAAe;AAkBrB,IAAM,wBAAwB;AAAA,EAC5B;AAAA,IAAC;AAAA;AAAA,EAAqE,GAAG;AAAA,EACzE;AAAA,IAAC;AAAA;AAAA,EAA+C,GAAG;AAAA,EACnD;AAAA,IAAC;AAAA;AAAA,EAA+D,GAAG;AAAA,EACnE;AAAA,IAAC;AAAA;AAAA,EAA+C,GAAG;AAAA,EACnD;AAAA,IAAC;AAAA;AAAA,EAAyC,GAAG;AAAA,EAC7C;AAAA,IAAC;AAAA;AAAA,EAAyE,GAAG;AAC/E;AACA,IAAMC,iBAAgB,IAAI,aAAa,SAAS,cAAc,qBAAqB;AAEnF,SAAS,cAAc,OAAO;AAC5B,SAAO,iBAAiB,iBAAiB,MAAM,KAAK;AAAA,IAAS;AAAA;AAAA,EAA+C;AAC9G;AAkBA,SAAS,yBAAyB;AAAA,EAChC;AACF,GAAG;AACD,SAAO,GAAG,qBAAqB,aAAa,SAAS;AACvD;AACA,SAAS,iCAAiC,UAAU;AAClD,SAAO;AAAA,IACL,OAAO,SAAS;AAAA,IAChB,eAAe;AAAA,IACf,WAAW,kCAAkC,SAAS,SAAS;AAAA,IAC/D,cAAc,KAAK,IAAI;AAAA,EACzB;AACF;AACA,SAAe,qBAAqB,aAAa,UAAU;AAAA;AACzD,UAAM,eAAe,MAAM,SAAS,KAAK;AACzC,UAAM,YAAY,aAAa;AAC/B,WAAOA,eAAc,OAAO,kBAAiD;AAAA,MAC3E;AAAA,MACA,YAAY,UAAU;AAAA,MACtB,eAAe,UAAU;AAAA,MACzB,cAAc,UAAU;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA;AACA,SAAS,WAAW;AAAA,EAClB;AACF,GAAG;AACD,SAAO,IAAI,QAAQ;AAAA,IACjB,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,kBAAkB;AAAA,EACpB,CAAC;AACH;AACA,SAAS,mBAAmB,WAAW;AAAA,EACrC;AACF,GAAG;AACD,QAAM,UAAU,WAAW,SAAS;AACpC,UAAQ,OAAO,iBAAiB,uBAAuB,YAAY,CAAC;AACpE,SAAO;AACT;AAMA,SAAe,mBAAmB,IAAI;AAAA;AACpC,UAAM,SAAS,MAAM,GAAG;AACxB,QAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;AAE/C,aAAO,GAAG;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AAAA;AACA,SAAS,kCAAkC,mBAAmB;AAE5D,SAAO,OAAO,kBAAkB,QAAQ,KAAK,KAAK,CAAC;AACrD;AACA,SAAS,uBAAuB,cAAc;AAC5C,SAAO,GAAG,qBAAqB,IAAI,YAAY;AACjD;AAkBA,SAAe,0BAA0B,IAGtC,IAEA;AAAA,6CALsC;AAAA,IACvC;AAAA,IACA;AAAA,EACF,GAAG;AAAA,IACD;AAAA,EACF,GAAG;AACD,UAAM,WAAW,yBAAyB,SAAS;AACnD,UAAM,UAAU,WAAW,SAAS;AAEpC,UAAM,mBAAmB,yBAAyB,aAAa;AAAA,MAC7D,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,kBAAkB;AACpB,YAAM,mBAAmB,MAAM,iBAAiB,oBAAoB;AACpE,UAAI,kBAAkB;AACpB,gBAAQ,OAAO,qBAAqB,gBAAgB;AAAA,MACtD;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX;AAAA,MACA,aAAa;AAAA,MACb,OAAO,UAAU;AAAA,MACjB,YAAY;AAAA,IACd;AACA,UAAM,UAAU;AAAA,MACd,QAAQ;AAAA,MACR;AAAA,MACA,MAAM,KAAK,UAAU,IAAI;AAAA,IAC3B;AACA,UAAM,WAAW,MAAM,mBAAmB,MAAM,MAAM,UAAU,OAAO,CAAC;AACxE,QAAI,SAAS,IAAI;AACf,YAAM,gBAAgB,MAAM,SAAS,KAAK;AAC1C,YAAM,8BAA8B;AAAA,QAClC,KAAK,cAAc,OAAO;AAAA,QAC1B,oBAAoB;AAAA,QACpB,cAAc,cAAc;AAAA,QAC5B,WAAW,iCAAiC,cAAc,SAAS;AAAA,MACrE;AACA,aAAO;AAAA,IACT,OAAO;AACL,YAAM,MAAM,qBAAqB,uBAAuB,QAAQ;AAAA,IAClE;AAAA,EACF;AAAA;AAmBA,SAAS,MAAM,IAAI;AACjB,SAAO,IAAI,QAAQ,aAAW;AAC5B,eAAW,SAAS,EAAE;AAAA,EACxB,CAAC;AACH;AAkBA,SAAS,sBAAsB,OAAO;AACpC,QAAM,MAAM,KAAK,OAAO,aAAa,GAAG,KAAK,CAAC;AAC9C,SAAO,IAAI,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AACnD;AAkBA,IAAM,oBAAoB;AAC1B,IAAM,cAAc;AAKpB,SAAS,cAAc;AACrB,MAAI;AAGF,UAAM,eAAe,IAAI,WAAW,EAAE;AACtC,UAAM,SAAS,KAAK,UAAU,KAAK;AACnC,WAAO,gBAAgB,YAAY;AAEnC,iBAAa,CAAC,IAAI,MAAa,aAAa,CAAC,IAAI;AACjD,UAAM,MAAM,OAAO,YAAY;AAC/B,WAAO,kBAAkB,KAAK,GAAG,IAAI,MAAM;AAAA,EAC7C,SAAS,IAAI;AAEX,WAAO;AAAA,EACT;AACF;AAEA,SAAS,OAAO,cAAc;AAC5B,QAAM,YAAY,sBAAsB,YAAY;AAGpD,SAAO,UAAU,OAAO,GAAG,EAAE;AAC/B;AAmBA,SAAS,OAAO,WAAW;AACzB,SAAO,GAAG,UAAU,OAAO,IAAI,UAAU,KAAK;AAChD;AAkBA,IAAM,qBAAqB,oBAAI,IAAI;AAKnC,SAAS,WAAW,WAAW,KAAK;AAClC,QAAM,MAAM,OAAO,SAAS;AAC5B,yBAAuB,KAAK,GAAG;AAC/B,qBAAmB,KAAK,GAAG;AAC7B;AA0BA,SAAS,uBAAuB,KAAK,KAAK;AACxC,QAAM,YAAY,mBAAmB,IAAI,GAAG;AAC5C,MAAI,CAAC,WAAW;AACd;AAAA,EACF;AACA,aAAW,YAAY,WAAW;AAChC,aAAS,GAAG;AAAA,EACd;AACF;AACA,SAAS,mBAAmB,KAAK,KAAK;AACpC,QAAM,UAAU,oBAAoB;AACpC,MAAI,SAAS;AACX,YAAQ,YAAY;AAAA,MAClB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,wBAAsB;AACxB;AACA,IAAI,mBAAmB;AAEvB,SAAS,sBAAsB;AAC7B,MAAI,CAAC,oBAAoB,sBAAsB,MAAM;AACnD,uBAAmB,IAAI,iBAAiB,uBAAuB;AAC/D,qBAAiB,YAAY,OAAK;AAChC,6BAAuB,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;AAAA,IAC/C;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,wBAAwB;AAC/B,MAAI,mBAAmB,SAAS,KAAK,kBAAkB;AACrD,qBAAiB,MAAM;AACvB,uBAAmB;AAAA,EACrB;AACF;AAkBA,IAAM,gBAAgB;AACtB,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAC1B,IAAIC,aAAY;AAChB,SAASC,gBAAe;AACtB,MAAI,CAACD,YAAW;AACd,IAAAA,aAAY,OAAO,eAAe,kBAAkB;AAAA,MAClD,SAAS,CAAC,IAAI,eAAe;AAM3B,gBAAQ,YAAY;AAAA,UAClB,KAAK;AACH,eAAG,kBAAkB,iBAAiB;AAAA,QAC1C;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAOA;AACT;AAEA,SAAe,IAAI,WAAW,OAAO;AAAA;AACnC,UAAM,MAAM,OAAO,SAAS;AAC5B,UAAM,KAAK,MAAMC,cAAa;AAC9B,UAAM,KAAK,GAAG,YAAY,mBAAmB,WAAW;AACxD,UAAM,cAAc,GAAG,YAAY,iBAAiB;AACpD,UAAM,WAAW,MAAM,YAAY,IAAI,GAAG;AAC1C,UAAM,YAAY,IAAI,OAAO,GAAG;AAChC,UAAM,GAAG;AACT,QAAI,CAAC,YAAY,SAAS,QAAQ,MAAM,KAAK;AAC3C,iBAAW,WAAW,MAAM,GAAG;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA;AAEA,SAAe,OAAO,WAAW;AAAA;AAC/B,UAAM,MAAM,OAAO,SAAS;AAC5B,UAAM,KAAK,MAAMA,cAAa;AAC9B,UAAM,KAAK,GAAG,YAAY,mBAAmB,WAAW;AACxD,UAAM,GAAG,YAAY,iBAAiB,EAAE,OAAO,GAAG;AAClD,UAAM,GAAG;AAAA,EACX;AAAA;AAOA,SAAe,OAAO,WAAW,UAAU;AAAA;AACzC,UAAM,MAAM,OAAO,SAAS;AAC5B,UAAM,KAAK,MAAMA,cAAa;AAC9B,UAAM,KAAK,GAAG,YAAY,mBAAmB,WAAW;AACxD,UAAM,QAAQ,GAAG,YAAY,iBAAiB;AAC9C,UAAM,WAAW,MAAM,MAAM,IAAI,GAAG;AACpC,UAAM,WAAW,SAAS,QAAQ;AAClC,QAAI,aAAa,QAAW;AAC1B,YAAM,MAAM,OAAO,GAAG;AAAA,IACxB,OAAO;AACL,YAAM,MAAM,IAAI,UAAU,GAAG;AAAA,IAC/B;AACA,UAAM,GAAG;AACT,QAAI,aAAa,CAAC,YAAY,SAAS,QAAQ,SAAS,MAAM;AAC5D,iBAAW,WAAW,SAAS,GAAG;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AAAA;AAsBA,SAAe,qBAAqB,eAAe;AAAA;AACjD,QAAI;AACJ,UAAM,oBAAoB,MAAM,OAAO,cAAc,WAAW,cAAY;AAC1E,YAAMC,qBAAoB,gCAAgC,QAAQ;AAClE,YAAM,mBAAmB,+BAA+B,eAAeA,kBAAiB;AACxF,4BAAsB,iBAAiB;AACvC,aAAO,iBAAiB;AAAA,IAC1B,CAAC;AACD,QAAI,kBAAkB,QAAQ,aAAa;AAEzC,aAAO;AAAA,QACL,mBAAmB,MAAM;AAAA,MAC3B;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAKA,SAAS,gCAAgC,UAAU;AACjD,QAAM,QAAQ,YAAY;AAAA,IACxB,KAAK,YAAY;AAAA,IACjB,oBAAoB;AAAA;AAAA,EACtB;AACA,SAAO,qBAAqB,KAAK;AACnC;AAQA,SAAS,+BAA+B,eAAe,mBAAmB;AACxE,MAAI,kBAAkB,uBAAuB,GAAmC;AAC9E,QAAI,CAAC,UAAU,QAAQ;AAErB,YAAM,+BAA+B,QAAQ,OAAOC,eAAc;AAAA,QAAO;AAAA;AAAA,MAAyC,CAAC;AACnH,aAAO;AAAA,QACL;AAAA,QACA,qBAAqB;AAAA,MACvB;AAAA,IACF;AAEA,UAAM,kBAAkB;AAAA,MACtB,KAAK,kBAAkB;AAAA,MACvB,oBAAoB;AAAA,MACpB,kBAAkB,KAAK,IAAI;AAAA,IAC7B;AACA,UAAM,sBAAsB,qBAAqB,eAAe,eAAe;AAC/E,WAAO;AAAA,MACL,mBAAmB;AAAA,MACnB;AAAA,IACF;AAAA,EACF,WAAW,kBAAkB,uBAAuB,GAAmC;AACrF,WAAO;AAAA,MACL;AAAA,MACA,qBAAqB,yBAAyB,aAAa;AAAA,IAC7D;AAAA,EACF,OAAO;AACL,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAe,qBAAqB,eAAe,mBAAmB;AAAA;AACpE,QAAI;AACF,YAAM,8BAA8B,MAAM,0BAA0B,eAAe,iBAAiB;AACpG,aAAO,IAAI,cAAc,WAAW,2BAA2B;AAAA,IACjE,SAAS,GAAG;AACV,UAAI,cAAc,CAAC,KAAK,EAAE,WAAW,eAAe,KAAK;AAGvD,cAAM,OAAO,cAAc,SAAS;AAAA,MACtC,OAAO;AAEL,cAAM,IAAI,cAAc,WAAW;AAAA,UACjC,KAAK,kBAAkB;AAAA,UACvB,oBAAoB;AAAA;AAAA,QACtB,CAAC;AAAA,MACH;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAEA,SAAe,yBAAyB,eAAe;AAAA;AAIrD,QAAI,QAAQ,MAAM,0BAA0B,cAAc,SAAS;AACnE,WAAO,MAAM,uBAAuB,GAAmC;AAErE,YAAM,MAAM,GAAG;AACf,cAAQ,MAAM,0BAA0B,cAAc,SAAS;AAAA,IACjE;AACA,QAAI,MAAM,uBAAuB,GAAmC;AAElE,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM,qBAAqB,aAAa;AAC5C,UAAI,qBAAqB;AACvB,eAAO;AAAA,MACT,OAAO;AAEL,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AASA,SAAS,0BAA0B,WAAW;AAC5C,SAAO,OAAO,WAAW,cAAY;AACnC,QAAI,CAAC,UAAU;AACb,YAAMA,eAAc;AAAA,QAAO;AAAA;AAAA,MAA+D;AAAA,IAC5F;AACA,WAAO,qBAAqB,QAAQ;AAAA,EACtC,CAAC;AACH;AACA,SAAS,qBAAqB,OAAO;AACnC,MAAI,+BAA+B,KAAK,GAAG;AACzC,WAAO;AAAA,MACL,KAAK,MAAM;AAAA,MACX,oBAAoB;AAAA;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,+BAA+B,mBAAmB;AACzD,SAAO,kBAAkB,uBAAuB,KAAqC,kBAAkB,mBAAmB,qBAAqB,KAAK,IAAI;AAC1J;AAkBA,SAAe,yBAAyB,IAGrC,IAAmB;AAAA,6CAHkB;AAAA,IACtC;AAAA,IACA;AAAA,EACF,GAAG,mBAAmB;AACpB,UAAM,WAAW,6BAA6B,WAAW,iBAAiB;AAC1E,UAAM,UAAU,mBAAmB,WAAW,iBAAiB;AAE/D,UAAM,mBAAmB,yBAAyB,aAAa;AAAA,MAC7D,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,kBAAkB;AACpB,YAAM,mBAAmB,MAAM,iBAAiB,oBAAoB;AACpE,UAAI,kBAAkB;AACpB,gBAAQ,OAAO,qBAAqB,gBAAgB;AAAA,MACtD;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX,cAAc;AAAA,QACZ,YAAY;AAAA,QACZ,OAAO,UAAU;AAAA,MACnB;AAAA,IACF;AACA,UAAM,UAAU;AAAA,MACd,QAAQ;AAAA,MACR;AAAA,MACA,MAAM,KAAK,UAAU,IAAI;AAAA,IAC3B;AACA,UAAM,WAAW,MAAM,mBAAmB,MAAM,MAAM,UAAU,OAAO,CAAC;AACxE,QAAI,SAAS,IAAI;AACf,YAAM,gBAAgB,MAAM,SAAS,KAAK;AAC1C,YAAM,qBAAqB,iCAAiC,aAAa;AACzE,aAAO;AAAA,IACT,OAAO;AACL,YAAM,MAAM,qBAAqB,uBAAuB,QAAQ;AAAA,IAClE;AAAA,EACF;AAAA;AACA,SAAS,6BAA6B,WAAW;AAAA,EAC/C;AACF,GAAG;AACD,SAAO,GAAG,yBAAyB,SAAS,CAAC,IAAI,GAAG;AACtD;AAwBA,SAAe,iBAAiB,eAAe,eAAe,OAAO;AAAA;AACnE,QAAI;AACJ,UAAM,QAAQ,MAAM,OAAO,cAAc,WAAW,cAAY;AAC9D,UAAI,CAAC,kBAAkB,QAAQ,GAAG;AAChC,cAAMA,eAAc;AAAA,UAAO;AAAA;AAAA,QAA+C;AAAA,MAC5E;AACA,YAAM,eAAe,SAAS;AAC9B,UAAI,CAAC,gBAAgB,iBAAiB,YAAY,GAAG;AAEnD,eAAO;AAAA,MACT,WAAW,aAAa,kBAAkB,GAAmC;AAE3E,uBAAe,0BAA0B,eAAe,YAAY;AACpE,eAAO;AAAA,MACT,OAAO;AAEL,YAAI,CAAC,UAAU,QAAQ;AACrB,gBAAMA,eAAc;AAAA,YAAO;AAAA;AAAA,UAAyC;AAAA,QACtE;AACA,cAAM,kBAAkB,oCAAoC,QAAQ;AACpE,uBAAe,yBAAyB,eAAe,eAAe;AACtE,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,UAAM,YAAY,eAAe,MAAM,eAAe,MAAM;AAC5D,WAAO;AAAA,EACT;AAAA;AAOA,SAAe,0BAA0B,eAAe,cAAc;AAAA;AAIpE,QAAI,QAAQ,MAAM,uBAAuB,cAAc,SAAS;AAChE,WAAO,MAAM,UAAU,kBAAkB,GAAmC;AAE1E,YAAM,MAAM,GAAG;AACf,cAAQ,MAAM,uBAAuB,cAAc,SAAS;AAAA,IAC9D;AACA,UAAM,YAAY,MAAM;AACxB,QAAI,UAAU,kBAAkB,GAAmC;AAEjE,aAAO,iBAAiB,eAAe,YAAY;AAAA,IACrD,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AASA,SAAS,uBAAuB,WAAW;AACzC,SAAO,OAAO,WAAW,cAAY;AACnC,QAAI,CAAC,kBAAkB,QAAQ,GAAG;AAChC,YAAMA,eAAc;AAAA,QAAO;AAAA;AAAA,MAA+C;AAAA,IAC5E;AACA,UAAM,eAAe,SAAS;AAC9B,QAAI,4BAA4B,YAAY,GAAG;AAC7C,aAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,GAAG;AAAA,QAChD,WAAW;AAAA,UACT,eAAe;AAAA;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACH;AACA,SAAe,yBAAyB,eAAe,mBAAmB;AAAA;AACxE,QAAI;AACF,YAAM,YAAY,MAAM,yBAAyB,eAAe,iBAAiB;AACjF,YAAM,2BAA2B,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,GAAG;AAAA,QACnF;AAAA,MACF,CAAC;AACD,YAAM,IAAI,cAAc,WAAW,wBAAwB;AAC3D,aAAO;AAAA,IACT,SAAS,GAAG;AACV,UAAI,cAAc,CAAC,MAAM,EAAE,WAAW,eAAe,OAAO,EAAE,WAAW,eAAe,MAAM;AAG5F,cAAM,OAAO,cAAc,SAAS;AAAA,MACtC,OAAO;AACL,cAAM,2BAA2B,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,GAAG;AAAA,UACnF,WAAW;AAAA,YACT,eAAe;AAAA;AAAA,UACjB;AAAA,QACF,CAAC;AACD,cAAM,IAAI,cAAc,WAAW,wBAAwB;AAAA,MAC7D;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AACA,SAAS,kBAAkB,mBAAmB;AAC5C,SAAO,sBAAsB,UAAa,kBAAkB,uBAAuB;AACrF;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,UAAU,kBAAkB,KAAmC,CAAC,mBAAmB,SAAS;AACrG;AACA,SAAS,mBAAmB,WAAW;AACrC,QAAM,MAAM,KAAK,IAAI;AACrB,SAAO,MAAM,UAAU,gBAAgB,UAAU,eAAe,UAAU,YAAY,MAAM;AAC9F;AAEA,SAAS,oCAAoC,UAAU;AACrD,QAAM,sBAAsB;AAAA,IAC1B,eAAe;AAAA,IACf,aAAa,KAAK,IAAI;AAAA,EACxB;AACA,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,GAAG;AAAA,IAChD,WAAW;AAAA,EACb,CAAC;AACH;AACA,SAAS,4BAA4B,WAAW;AAC9C,SAAO,UAAU,kBAAkB,KAAqC,UAAU,cAAc,qBAAqB,KAAK,IAAI;AAChI;AAyBA,SAAe,MAAM,eAAe;AAAA;AAClC,UAAM,oBAAoB;AAC1B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,qBAAqB,iBAAiB;AAChD,QAAI,qBAAqB;AACvB,0BAAoB,MAAM,QAAQ,KAAK;AAAA,IACzC,OAAO;AAGL,uBAAiB,iBAAiB,EAAE,MAAM,QAAQ,KAAK;AAAA,IACzD;AACA,WAAO,kBAAkB;AAAA,EAC3B;AAAA;AA0BA,SAAe,SAAS,eAAe,eAAe,OAAO;AAAA;AAC3D,UAAM,oBAAoB;AAC1B,UAAM,iCAAiC,iBAAiB;AAGxD,UAAM,YAAY,MAAM,iBAAiB,mBAAmB,YAAY;AACxE,WAAO,UAAU;AAAA,EACnB;AAAA;AACA,SAAe,iCAAiC,eAAe;AAAA;AAC7D,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,MAAM,qBAAqB,aAAa;AAC5C,QAAI,qBAAqB;AAEvB,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAmKA,SAAS,iBAAiB,KAAK;AAC7B,MAAI,CAAC,OAAO,CAAC,IAAI,SAAS;AACxB,UAAM,qBAAqB,mBAAmB;AAAA,EAChD;AACA,MAAI,CAAC,IAAI,MAAM;AACb,UAAM,qBAAqB,UAAU;AAAA,EACvC;AAEA,QAAM,aAAa,CAAC,aAAa,UAAU,OAAO;AAClD,aAAW,WAAW,YAAY;AAChC,QAAI,CAAC,IAAI,QAAQ,OAAO,GAAG;AACzB,YAAM,qBAAqB,OAAO;AAAA,IACpC;AAAA,EACF;AACA,SAAO;AAAA,IACL,SAAS,IAAI;AAAA,IACb,WAAW,IAAI,QAAQ;AAAA,IACvB,QAAQ,IAAI,QAAQ;AAAA,IACpB,OAAO,IAAI,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAOC,eAAc,OAAO,6BAAuE;AAAA,IACjG;AAAA,EACF,CAAC;AACH;AAkBA,IAAM,qBAAqB;AAC3B,IAAM,8BAA8B;AACpC,IAAM,gBAAgB,eAAa;AACjC,QAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAa;AAEtD,QAAM,YAAY,iBAAiB,GAAG;AACtC,QAAM,2BAA2B,aAAa,KAAK,WAAW;AAC9D,QAAM,oBAAoB;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,MAAM,QAAQ,QAAQ;AAAA,EACjC;AACA,SAAO;AACT;AACA,IAAM,kBAAkB,eAAa;AACnC,QAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAa;AAEtD,QAAM,gBAAgB,aAAa,KAAK,kBAAkB,EAAE,aAAa;AACzE,QAAM,wBAAwB;AAAA,IAC5B,OAAO,MAAM,MAAM,aAAa;AAAA,IAChC,UAAU,kBAAgB,SAAS,eAAe,YAAY;AAAA,EAChE;AACA,SAAO;AACT;AACA,SAAS,wBAAwB;AAC/B,qBAAmB,IAAI;AAAA,IAAU;AAAA,IAAoB;AAAA,IAAe;AAAA;AAAA,EAAmC,CAAC;AACxG,qBAAmB,IAAI;AAAA,IAAU;AAAA,IAA6B;AAAA,IAAiB;AAAA;AAAA,EAAqC,CAAC;AACvH;AAQA,sBAAsB;AACtB,gBAAgBC,OAAM,OAAO;AAE7B,gBAAgBA,OAAM,SAAS,SAAS;;;AC9nCxC,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAC1B,IAAM,WAAW;AACjB,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAE9B,IAAM,qCAAqC;AAC3C,IAAM,+BAA+B;AACrC,IAAI;AAAA,CACH,SAAUC,cAAa;AACtB,EAAAA,aAAYA,aAAY,cAAc,IAAI,CAAC,IAAI;AAC/C,EAAAA,aAAYA,aAAY,sBAAsB,IAAI,CAAC,IAAI;AACzD,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAgBxC,IAAI;AAAA,CACH,SAAUA,cAAa;AACtB,EAAAA,aAAY,eAAe,IAAI;AAC/B,EAAAA,aAAY,sBAAsB,IAAI;AACxC,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAkBpC,SAAS,cAAc,OAAO;AAC5B,QAAM,aAAa,IAAI,WAAW,KAAK;AACvC,QAAM,eAAe,KAAK,OAAO,aAAa,GAAG,UAAU,CAAC;AAC5D,SAAO,aAAa,QAAQ,MAAM,EAAE,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC9E;AACA,SAAS,cAAc,cAAc;AACnC,QAAM,UAAU,IAAI,QAAQ,IAAI,aAAa,SAAS,KAAK,CAAC;AAC5D,QAAMC,WAAU,eAAe,SAAS,QAAQ,OAAO,GAAG,EAAE,QAAQ,MAAM,GAAG;AAC7E,QAAM,UAAU,KAAKA,OAAM;AAC3B,QAAM,cAAc,IAAI,WAAW,QAAQ,MAAM;AACjD,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,gBAAY,CAAC,IAAI,QAAQ,WAAW,CAAC;AAAA,EACvC;AACA,SAAO;AACT;AAkBA,IAAM,cAAc;AAKpB,IAAM,iBAAiB;AACvB,IAAM,wBAAwB;AAC9B,SAAe,mBAAmB,UAAU;AAAA;AAC1C,QAAI,eAAe,WAAW;AAG5B,YAAM,YAAY,MAAM,UAAU,UAAU;AAC5C,YAAM,UAAU,UAAU,IAAI,CAAAC,QAAMA,IAAG,IAAI;AAC3C,UAAI,CAAC,QAAQ,SAAS,WAAW,GAAG;AAElC,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,eAAe;AACnB,UAAM,KAAK,MAAM,OAAO,aAAa,gBAAgB;AAAA,MACnD,SAAS,CAAOA,KAAI,YAAY,YAAY,uBAAuB;AACjE,YAAI;AACJ,YAAI,aAAa,GAAG;AAElB;AAAA,QACF;AACA,YAAI,CAACA,IAAG,iBAAiB,SAAS,qBAAqB,GAAG;AAExD;AAAA,QACF;AACA,cAAM,cAAc,mBAAmB,YAAY,qBAAqB;AACxE,cAAM,QAAQ,MAAM,YAAY,MAAM,aAAa,EAAE,IAAI,QAAQ;AACjE,cAAM,YAAY,MAAM;AACxB,YAAI,CAAC,OAAO;AAEV;AAAA,QACF;AACA,YAAI,eAAe,GAAG;AACpB,gBAAM,aAAa;AACnB,cAAI,CAAC,WAAW,QAAQ,CAAC,WAAW,UAAU,CAAC,WAAW,UAAU;AAClE;AAAA,UACF;AACA,yBAAe;AAAA,YACb,OAAO,WAAW;AAAA,YAClB,aAAa,KAAK,WAAW,gBAAgB,QAAQ,OAAO,SAAS,KAAK,KAAK,IAAI;AAAA,YACnF,qBAAqB;AAAA,cACnB,MAAM,WAAW;AAAA,cACjB,QAAQ,WAAW;AAAA,cACnB,UAAU,WAAW;AAAA,cACrB,SAAS,WAAW;AAAA,cACpB,UAAU,OAAO,WAAW,aAAa,WAAW,WAAW,WAAW,cAAc,WAAW,QAAQ;AAAA,YAC7G;AAAA,UACF;AAAA,QACF,WAAW,eAAe,GAAG;AAC3B,gBAAM,aAAa;AACnB,yBAAe;AAAA,YACb,OAAO,WAAW;AAAA,YAClB,YAAY,WAAW;AAAA,YACvB,qBAAqB;AAAA,cACnB,MAAM,cAAc,WAAW,IAAI;AAAA,cACnC,QAAQ,cAAc,WAAW,MAAM;AAAA,cACvC,UAAU,WAAW;AAAA,cACrB,SAAS,WAAW;AAAA,cACpB,UAAU,cAAc,WAAW,QAAQ;AAAA,YAC7C;AAAA,UACF;AAAA,QACF,WAAW,eAAe,GAAG;AAC3B,gBAAM,aAAa;AACnB,yBAAe;AAAA,YACb,OAAO,WAAW;AAAA,YAClB,YAAY,WAAW;AAAA,YACvB,qBAAqB;AAAA,cACnB,MAAM,cAAc,WAAW,IAAI;AAAA,cACnC,QAAQ,cAAc,WAAW,MAAM;AAAA,cACvC,UAAU,WAAW;AAAA,cACrB,SAAS,WAAW;AAAA,cACpB,UAAU,cAAc,WAAW,QAAQ;AAAA,YAC7C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,OAAG,MAAM;AAET,UAAM,SAAS,WAAW;AAC1B,UAAM,SAAS,sBAAsB;AACrC,UAAM,SAAS,WAAW;AAC1B,WAAO,kBAAkB,YAAY,IAAI,eAAe;AAAA,EAC1D;AAAA;AACA,SAAS,kBAAkB,cAAc;AACvC,MAAI,CAAC,gBAAgB,CAAC,aAAa,qBAAqB;AACtD,WAAO;AAAA,EACT;AACA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,OAAO,aAAa,eAAe,YAAY,aAAa,aAAa,KAAK,OAAO,aAAa,UAAU,YAAY,aAAa,MAAM,SAAS,KAAK,OAAO,oBAAoB,SAAS,YAAY,oBAAoB,KAAK,SAAS,KAAK,OAAO,oBAAoB,WAAW,YAAY,oBAAoB,OAAO,SAAS,KAAK,OAAO,oBAAoB,aAAa,YAAY,oBAAoB,SAAS,SAAS,KAAK,OAAO,oBAAoB,YAAY,YAAY,oBAAoB,QAAQ,SAAS,KAAK,OAAO,oBAAoB,aAAa,YAAY,oBAAoB,SAAS,SAAS;AAC1mB;AAmBA,IAAMC,iBAAgB;AACtB,IAAMC,oBAAmB;AACzB,IAAMC,qBAAoB;AAC1B,IAAIC,aAAY;AAChB,SAASC,gBAAe;AACtB,MAAI,CAACD,YAAW;AACd,IAAAA,aAAY,OAAOH,gBAAeC,mBAAkB;AAAA,MAClD,SAAS,CAAC,WAAW,eAAe;AAKlC,gBAAQ,YAAY;AAAA,UAClB,KAAK;AACH,sBAAU,kBAAkBC,kBAAiB;AAAA,QACjD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAOC;AACT;AAEA,SAAe,MAAM,sBAAsB;AAAA;AACzC,UAAM,MAAME,QAAO,oBAAoB;AACvC,UAAM,KAAK,MAAMD,cAAa;AAC9B,UAAM,eAAe,MAAM,GAAG,YAAYF,kBAAiB,EAAE,YAAYA,kBAAiB,EAAE,IAAI,GAAG;AACnG,QAAI,cAAc;AAChB,aAAO;AAAA,IACT,OAAO;AAEL,YAAM,kBAAkB,MAAM,mBAAmB,qBAAqB,UAAU,QAAQ;AACxF,UAAI,iBAAiB;AACnB,cAAM,MAAM,sBAAsB,eAAe;AACjD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAEA,SAAe,MAAM,sBAAsB,cAAc;AAAA;AACvD,UAAM,MAAMG,QAAO,oBAAoB;AACvC,UAAM,KAAK,MAAMD,cAAa;AAC9B,UAAM,KAAK,GAAG,YAAYF,oBAAmB,WAAW;AACxD,UAAM,GAAG,YAAYA,kBAAiB,EAAE,IAAI,cAAc,GAAG;AAC7D,UAAM,GAAG;AACT,WAAO;AAAA,EACT;AAAA;AAEA,SAAe,SAAS,sBAAsB;AAAA;AAC5C,UAAM,MAAMG,QAAO,oBAAoB;AACvC,UAAM,KAAK,MAAMD,cAAa;AAC9B,UAAM,KAAK,GAAG,YAAYF,oBAAmB,WAAW;AACxD,UAAM,GAAG,YAAYA,kBAAiB,EAAE,OAAO,GAAG;AAClD,UAAM,GAAG;AAAA,EACX;AAAA;AACA,SAASG,QAAO;AAAA,EACd;AACF,GAAG;AACD,SAAO,UAAU;AACnB;AAkBA,IAAM,YAAY;AAAA,EAChB;AAAA,IAAC;AAAA;AAAA,EAAqE,GAAG;AAAA,EACzE;AAAA,IAAC;AAAA;AAAA,EAA8D,GAAG;AAAA,EAClE;AAAA,IAAC;AAAA;AAAA,EAAsD,GAAG;AAAA,EAC1D;AAAA,IAAC;AAAA;AAAA,EAAuD,GAAG;AAAA,EAC3D;AAAA,IAAC;AAAA;AAAA,EAAuD,GAAG;AAAA,EAC3D;AAAA,IAAC;AAAA;AAAA,EAAyD,GAAG;AAAA,EAC7D;AAAA,IAAC;AAAA;AAAA,EAA+D,GAAG;AAAA,EACnE;AAAA,IAAC;AAAA;AAAA,EAAgF,GAAG;AAAA,EACpF;AAAA,IAAC;AAAA;AAAA,EAA+D,GAAG;AAAA,EACnE;AAAA,IAAC;AAAA;AAAA,EAAmE,GAAG;AAAA,EACvE;AAAA,IAAC;AAAA;AAAA,EAAmE,GAAG;AAAA,EACvE;AAAA,IAAC;AAAA;AAAA,EAAyD,GAAG;AAAA,EAC7D;AAAA,IAAC;AAAA;AAAA,EAA6D,GAAG;AAAA,EACjE;AAAA,IAAC;AAAA;AAAA,EAA+D,GAAG;AAAA,EACnE;AAAA,IAAC;AAAA;AAAA,EAAiE,GAAG;AAAA,EACrE;AAAA,IAAC;AAAA;AAAA,EAAuD,GAAG;AAAA,EAC3D;AAAA,IAAC;AAAA;AAAA,EAAqD,GAAG;AAAA,EACzD;AAAA,IAAC;AAAA;AAAA,EAA6E,GAAG;AACnF;AACA,IAAMC,iBAAgB,IAAI,aAAa,aAAa,aAAa,SAAS;AAkB1E,SAAe,gBAAgB,sBAAsB,qBAAqB;AAAA;AACxE,UAAM,UAAU,MAAMC,YAAW,oBAAoB;AACrD,UAAM,OAAO,QAAQ,mBAAmB;AACxC,UAAM,mBAAmB;AAAA,MACvB,QAAQ;AAAA,MACR;AAAA,MACA,MAAM,KAAK,UAAU,IAAI;AAAA,IAC3B;AACA,QAAI;AACJ,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,YAAY,qBAAqB,SAAS,GAAG,gBAAgB;AAC1F,qBAAe,MAAM,SAAS,KAAK;AAAA,IACrC,SAAS,KAAK;AACZ,YAAMD,eAAc,OAAO,0BAAiE;AAAA,QAC1F,WAAW,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,SAAS;AAAA,MACpE,CAAC;AAAA,IACH;AACA,QAAI,aAAa,OAAO;AACtB,YAAM,UAAU,aAAa,MAAM;AACnC,YAAMA,eAAc,OAAO,0BAAiE;AAAA,QAC1F,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AACA,QAAI,CAAC,aAAa,OAAO;AACvB,YAAMA,eAAc;AAAA,QAAO;AAAA;AAAA,MAAmE;AAAA,IAChG;AACA,WAAO,aAAa;AAAA,EACtB;AAAA;AACA,SAAe,mBAAmB,sBAAsB,cAAc;AAAA;AACpE,UAAM,UAAU,MAAMC,YAAW,oBAAoB;AACrD,UAAM,OAAO,QAAQ,aAAa,mBAAmB;AACrD,UAAM,gBAAgB;AAAA,MACpB,QAAQ;AAAA,MACR;AAAA,MACA,MAAM,KAAK,UAAU,IAAI;AAAA,IAC3B;AACA,QAAI;AACJ,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,GAAG,YAAY,qBAAqB,SAAS,CAAC,IAAI,aAAa,KAAK,IAAI,aAAa;AAClH,qBAAe,MAAM,SAAS,KAAK;AAAA,IACrC,SAAS,KAAK;AACZ,YAAMD,eAAc,OAAO,uBAA2D;AAAA,QACpF,WAAW,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,SAAS;AAAA,MACpE,CAAC;AAAA,IACH;AACA,QAAI,aAAa,OAAO;AACtB,YAAM,UAAU,aAAa,MAAM;AACnC,YAAMA,eAAc,OAAO,uBAA2D;AAAA,QACpF,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AACA,QAAI,CAAC,aAAa,OAAO;AACvB,YAAMA,eAAc;AAAA,QAAO;AAAA;AAAA,MAA6D;AAAA,IAC1F;AACA,WAAO,aAAa;AAAA,EACtB;AAAA;AACA,SAAe,mBAAmB,sBAAsB,OAAO;AAAA;AAC7D,UAAM,UAAU,MAAMC,YAAW,oBAAoB;AACrD,UAAM,qBAAqB;AAAA,MACzB,QAAQ;AAAA,MACR;AAAA,IACF;AACA,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,GAAG,YAAY,qBAAqB,SAAS,CAAC,IAAI,KAAK,IAAI,kBAAkB;AAC1G,YAAM,eAAe,MAAM,SAAS,KAAK;AACzC,UAAI,aAAa,OAAO;AACtB,cAAM,UAAU,aAAa,MAAM;AACnC,cAAMD,eAAc,OAAO,4BAAqE;AAAA,UAC9F,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF,SAAS,KAAK;AACZ,YAAMA,eAAc,OAAO,4BAAqE;AAAA,QAC9F,WAAW,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,SAAS;AAAA,MACpE,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AACA,SAAS,YAAY;AAAA,EACnB;AACF,GAAG;AACD,SAAO,GAAG,QAAQ,aAAa,SAAS;AAC1C;AACA,SAAeC,YAAW,IAGvB;AAAA,6CAHuB;AAAA,IACxB;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,YAAY,MAAM,cAAc,SAAS;AAC/C,WAAO,IAAI,QAAQ;AAAA,MACjB,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,kBAAkB,UAAU;AAAA,MAC5B,sCAAsC,OAAO,SAAS;AAAA,IACxD,CAAC;AAAA,EACH;AAAA;AACA,SAAS,QAAQ;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,OAAO;AAAA,IACX,KAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,mBAAmB;AAClC,SAAK,IAAI,oBAAoB;AAAA,EAC/B;AACA,SAAO;AACT;AAmBA,IAAM,sBAAsB,IAAI,KAAK,KAAK,KAAK;AAC/C,SAAe,iBAAiB,WAAW;AAAA;AACzC,UAAM,mBAAmB,MAAM,oBAAoB,UAAU,gBAAgB,UAAU,QAAQ;AAC/F,UAAM,sBAAsB;AAAA,MAC1B,UAAU,UAAU;AAAA,MACpB,SAAS,UAAU,eAAe;AAAA,MAClC,UAAU,iBAAiB;AAAA,MAC3B,MAAM,cAAc,iBAAiB,OAAO,MAAM,CAAC;AAAA,MACnD,QAAQ,cAAc,iBAAiB,OAAO,QAAQ,CAAC;AAAA,IACzD;AACA,UAAM,eAAe,MAAM,MAAM,UAAU,oBAAoB;AAC/D,QAAI,CAAC,cAAc;AAEjB,aAAO,YAAY,UAAU,sBAAsB,mBAAmB;AAAA,IACxE,WAAW,CAAC,aAAa,aAAa,qBAAqB,mBAAmB,GAAG;AAE/E,UAAI;AACF,cAAM,mBAAmB,UAAU,sBAAsB,aAAa,KAAK;AAAA,MAC7E,SAAS,GAAG;AAEV,gBAAQ,KAAK,CAAC;AAAA,MAChB;AACA,aAAO,YAAY,UAAU,sBAAsB,mBAAmB;AAAA,IACxE,WAAW,KAAK,IAAI,KAAK,aAAa,aAAa,qBAAqB;AAEtE,aAAO,YAAY,WAAW;AAAA,QAC5B,OAAO,aAAa;AAAA,QACpB,YAAY,KAAK,IAAI;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AAEL,aAAO,aAAa;AAAA,IACtB;AAAA,EACF;AAAA;AAKA,SAAe,oBAAoB,WAAW;AAAA;AAC5C,UAAM,eAAe,MAAM,MAAM,UAAU,oBAAoB;AAC/D,QAAI,cAAc;AAChB,YAAM,mBAAmB,UAAU,sBAAsB,aAAa,KAAK;AAC3E,YAAM,SAAS,UAAU,oBAAoB;AAAA,IAC/C;AAEA,UAAM,mBAAmB,MAAM,UAAU,eAAe,YAAY,gBAAgB;AACpF,QAAI,kBAAkB;AACpB,aAAO,iBAAiB,YAAY;AAAA,IACtC;AAEA,WAAO;AAAA,EACT;AAAA;AACA,SAAe,YAAY,WAAW,cAAc;AAAA;AAClD,QAAI;AACF,YAAM,eAAe,MAAM,mBAAmB,UAAU,sBAAsB,YAAY;AAC1F,YAAM,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,QACzE,OAAO;AAAA,QACP,YAAY,KAAK,IAAI;AAAA,MACvB,CAAC;AACD,YAAM,MAAM,UAAU,sBAAsB,mBAAmB;AAC/D,aAAO;AAAA,IACT,SAAS,GAAG;AACV,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AACA,SAAe,YAAY,sBAAsB,qBAAqB;AAAA;AACpE,UAAM,QAAQ,MAAM,gBAAgB,sBAAsB,mBAAmB;AAC7E,UAAM,eAAe;AAAA,MACnB;AAAA,MACA,YAAY,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AACA,UAAM,MAAM,sBAAsB,YAAY;AAC9C,WAAO,aAAa;AAAA,EACtB;AAAA;AAIA,SAAe,oBAAoB,gBAAgB,UAAU;AAAA;AAC3D,UAAM,eAAe,MAAM,eAAe,YAAY,gBAAgB;AACtE,QAAI,cAAc;AAChB,aAAO;AAAA,IACT;AACA,WAAO,eAAe,YAAY,UAAU;AAAA,MAC1C,iBAAiB;AAAA;AAAA;AAAA,MAGjB,sBAAsB,cAAc,QAAQ;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA;AAIA,SAAS,aAAa,WAAW,gBAAgB;AAC/C,QAAM,kBAAkB,eAAe,aAAa,UAAU;AAC9D,QAAM,kBAAkB,eAAe,aAAa,UAAU;AAC9D,QAAM,cAAc,eAAe,SAAS,UAAU;AACtD,QAAM,gBAAgB,eAAe,WAAW,UAAU;AAC1D,SAAO,mBAAmB,mBAAmB,eAAe;AAC9D;AAkBA,SAAS,mBAAmB,iBAAiB;AAC3C,QAAM,UAAU;AAAA,IACd,MAAM,gBAAgB;AAAA;AAAA,IAEtB,aAAa,gBAAgB;AAAA;AAAA,IAE7B,WAAW,gBAAgB;AAAA,EAC7B;AACA,+BAA6B,SAAS,eAAe;AACrD,uBAAqB,SAAS,eAAe;AAC7C,sBAAoB,SAAS,eAAe;AAC5C,SAAO;AACT;AACA,SAAS,6BAA6B,SAAS,wBAAwB;AACrE,MAAI,CAAC,uBAAuB,cAAc;AACxC;AAAA,EACF;AACA,UAAQ,eAAe,CAAC;AACxB,QAAM,QAAQ,uBAAuB,aAAa;AAClD,MAAI,CAAC,CAAC,OAAO;AACX,YAAQ,aAAa,QAAQ;AAAA,EAC/B;AACA,QAAM,OAAO,uBAAuB,aAAa;AACjD,MAAI,CAAC,CAAC,MAAM;AACV,YAAQ,aAAa,OAAO;AAAA,EAC9B;AACA,QAAM,QAAQ,uBAAuB,aAAa;AAClD,MAAI,CAAC,CAAC,OAAO;AACX,YAAQ,aAAa,QAAQ;AAAA,EAC/B;AACA,QAAM,OAAO,uBAAuB,aAAa;AACjD,MAAI,CAAC,CAAC,MAAM;AACV,YAAQ,aAAa,OAAO;AAAA,EAC9B;AACF;AACA,SAAS,qBAAqB,SAAS,wBAAwB;AAC7D,MAAI,CAAC,uBAAuB,MAAM;AAChC;AAAA,EACF;AACA,UAAQ,OAAO,uBAAuB;AACxC;AACA,SAAS,oBAAoB,SAAS,wBAAwB;AAC5D,MAAI,IAAI,IAAI,IAAI,IAAI;AAEpB,MAAI,CAAC,uBAAuB,cAAc,GAAG,KAAK,uBAAuB,kBAAkB,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe;AAC5I;AAAA,EACF;AACA,UAAQ,aAAa,CAAC;AACtB,QAAM,QAAQ,MAAM,KAAK,uBAAuB,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,QAAQ,OAAO,SAAS,MAAM,KAAK,uBAAuB,kBAAkB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC9N,MAAI,CAAC,CAAC,MAAM;AACV,YAAQ,WAAW,OAAO;AAAA,EAC5B;AAEA,QAAM,kBAAkB,KAAK,uBAAuB,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG;AACxG,MAAI,CAAC,CAAC,gBAAgB;AACpB,YAAQ,WAAW,iBAAiB;AAAA,EACtC;AACF;AAkBA,SAAS,iBAAiB,MAAM;AAE9B,SAAO,OAAO,SAAS,YAAY,CAAC,CAAC,QAAQ,uBAAuB;AACtE;AAkBA,cAAc,wBAAwB,qBAAqB;AAC3D,SAAS,cAAc,IAAI,IAAI;AAC7B,QAAM,cAAc,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,gBAAY,KAAK,GAAG,OAAO,CAAC,CAAC;AAC7B,QAAI,IAAI,GAAG,QAAQ;AACjB,kBAAY,KAAK,GAAG,OAAO,CAAC,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,SAAO,YAAY,KAAK,EAAE;AAC5B;AAkBA,SAASC,kBAAiB,KAAK;AAC7B,MAAI,CAAC,OAAO,CAAC,IAAI,SAAS;AACxB,UAAMC,sBAAqB,0BAA0B;AAAA,EACvD;AACA,MAAI,CAAC,IAAI,MAAM;AACb,UAAMA,sBAAqB,UAAU;AAAA,EACvC;AAEA,QAAM,aAAa,CAAC,aAAa,UAAU,SAAS,mBAAmB;AACvE,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,aAAW,WAAW,YAAY;AAChC,QAAI,CAAC,QAAQ,OAAO,GAAG;AACrB,YAAMA,sBAAqB,OAAO;AAAA,IACpC;AAAA,EACF;AACA,SAAO;AAAA,IACL,SAAS,IAAI;AAAA,IACb,WAAW,QAAQ;AAAA,IACnB,QAAQ,QAAQ;AAAA,IAChB,OAAO,QAAQ;AAAA,IACf,UAAU,QAAQ;AAAA,EACpB;AACF;AACA,SAASA,sBAAqB,WAAW;AACvC,SAAOH,eAAc,OAAO,6BAAuE;AAAA,IACjG;AAAA,EACF,CAAC;AACH;AAkBA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,KAAK,eAAe,mBAAmB;AAEjD,SAAK,2CAA2C;AAChD,SAAK,6BAA6B;AAClC,SAAK,mBAAmB;AACxB,SAAK,YAAY,CAAC;AAClB,SAAK,sBAAsB;AAC3B,UAAM,YAAYE,kBAAiB,GAAG;AACtC,SAAK,uBAAuB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AACR,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACF;AAkBA,SAAe,kBAAkB,WAAW;AAAA;AAC1C,QAAI;AACF,gBAAU,iBAAiB,MAAM,UAAU,cAAc,SAAS,iBAAiB;AAAA,QACjF,OAAO;AAAA,MACT,CAAC;AAMD,gBAAU,eAAe,OAAO,EAAE,MAAM,MAAM;AAAA,MAE9C,CAAC;AACD,YAAM,0BAA0B,UAAU,cAAc;AAAA,IAC1D,SAAS,GAAG;AACV,YAAMF,eAAc,OAAO,sCAAkF;AAAA,QAC3G,qBAAqB,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE;AAAA,MAC/D,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAUA,SAAe,0BAA0B,cAAc;AAAA;AACrD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,gBAAgB,WAAW,MAAM,OAAO,IAAI,MAAM,uCAAuC,4BAA4B,KAAK,CAAC,GAAG,4BAA4B;AAChK,YAAM,aAAa,aAAa,cAAc,aAAa;AAC3D,UAAI,aAAa,QAAQ;AACvB,qBAAa,aAAa;AAC1B,gBAAQ;AAAA,MACV,WAAW,YAAY;AACrB,mBAAW,gBAAgB,QAAM;AAC/B,cAAI;AACJ,gBAAM,KAAK,GAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,aAAa;AACpF,uBAAW,gBAAgB;AAC3B,yBAAa,aAAa;AAC1B,oBAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF,OAAO;AACL,qBAAa,aAAa;AAC1B,eAAO,IAAI,MAAM,mCAAmC,CAAC;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAkBA,SAAe,YAAY,WAAW,gBAAgB;AAAA;AACpD,QAAI,CAAC,kBAAkB,CAAC,UAAU,gBAAgB;AAChD,YAAM,kBAAkB,SAAS;AAAA,IACnC;AACA,QAAI,CAAC,kBAAkB,CAAC,CAAC,UAAU,gBAAgB;AACjD;AAAA,IACF;AACA,QAAI,EAAE,0BAA0B,4BAA4B;AAC1D,YAAMA,eAAc;AAAA,QAAO;AAAA;AAAA,MAAiE;AAAA,IAC9F;AACA,cAAU,iBAAiB;AAAA,EAC7B;AAAA;AAkBA,SAAe,eAAe,WAAW,UAAU;AAAA;AACjD,QAAI,CAAC,CAAC,UAAU;AACd,gBAAU,WAAW;AAAA,IACvB,WAAW,CAAC,UAAU,UAAU;AAC9B,gBAAU,WAAW;AAAA,IACvB;AAAA,EACF;AAAA;AAkBA,SAAe,WAAW,WAAW,SAAS;AAAA;AAC5C,QAAI,CAAC,WAAW;AACd,YAAMA,eAAc;AAAA,QAAO;AAAA;AAAA,MAA8D;AAAA,IAC3F;AACA,QAAI,aAAa,eAAe,WAAW;AACzC,YAAM,aAAa,kBAAkB;AAAA,IACvC;AACA,QAAI,aAAa,eAAe,WAAW;AACzC,YAAMA,eAAc;AAAA,QAAO;AAAA;AAAA,MAAuD;AAAA,IACpF;AACA,UAAM,eAAe,WAAW,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ;AAClG,UAAM,YAAY,WAAW,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,yBAAyB;AAChH,WAAO,iBAAiB,SAAS;AAAA,EACnC;AAAA;AAkBA,SAAe,WAAW,WAAW,aAAa,MAAM;AAAA;AACtD,UAAM,YAAY,aAAa,WAAW;AAC1C,UAAM,YAAY,MAAM,UAAU,qBAAqB,kBAAkB,IAAI;AAC7E,cAAU,SAAS,WAAW;AAAA;AAAA,MAE5B,YAAY,KAAK,mBAAmB;AAAA,MACpC,cAAc,KAAK,qBAAqB;AAAA,MACxC,cAAc,KAAK,qBAAqB;AAAA,MACxC,qBAAqB,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAAA;AAAA,IAEnD,CAAC;AAAA,EACH;AAAA;AACA,SAAS,aAAa,aAAa;AACjC,UAAQ,aAAa;AAAA,IACnB,KAAK,YAAY;AACf,aAAO;AAAA,IACT,KAAK,YAAY;AACf,aAAO;AAAA,IACT;AACE,YAAM,IAAI,MAAM;AAAA,EACpB;AACF;AAkBA,SAAe,qBAAqB,WAAW,OAAO;AAAA;AACpD,UAAM,kBAAkB,MAAM;AAC9B,QAAI,CAAC,gBAAgB,qBAAqB;AACxC;AAAA,IACF;AACA,QAAI,UAAU,oBAAoB,gBAAgB,gBAAgB,YAAY,eAAe;AAC3F,UAAI,OAAO,UAAU,qBAAqB,YAAY;AACpD,kBAAU,iBAAiB,mBAAmB,eAAe,CAAC;AAAA,MAChE,OAAO;AACL,kBAAU,iBAAiB,KAAK,mBAAmB,eAAe,CAAC;AAAA,MACrE;AAAA,IACF;AAEA,UAAM,cAAc,gBAAgB;AACpC,QAAI,iBAAiB,WAAW,KAAK,YAAY,kCAAkC,MAAM,KAAK;AAC5F,YAAM,WAAW,WAAW,gBAAgB,aAAa,WAAW;AAAA,IACtE;AAAA,EACF;AAAA;AACA,IAAMI,QAAO;AACb,IAAMC,WAAU;AAkBhB,IAAM,yBAAyB,eAAa;AAC1C,QAAM,YAAY,IAAI,iBAAiB,UAAU,YAAY,KAAK,EAAE,aAAa,GAAG,UAAU,YAAY,wBAAwB,EAAE,aAAa,GAAG,UAAU,YAAY,oBAAoB,CAAC;AAC/L,YAAU,cAAc,iBAAiB,WAAW,OAAK,qBAAqB,WAAW,CAAC,CAAC;AAC3F,SAAO;AACT;AACA,IAAM,iCAAiC,eAAa;AAClD,QAAM,YAAY,UAAU,YAAY,WAAW,EAAE,aAAa;AAClE,QAAM,oBAAoB;AAAA,IACxB,UAAU,aAAW,WAAW,WAAW,OAAO;AAAA,EACpD;AACA,SAAO;AACT;AACA,SAAS,4BAA4B;AACnC,qBAAmB,IAAI;AAAA,IAAU;AAAA,IAAa;AAAA,IAAwB;AAAA;AAAA,EAAmC,CAAC;AAC1G,qBAAmB,IAAI;AAAA,IAAU;AAAA,IAAsB;AAAA,IAAgC;AAAA;AAAA,EAAqC,CAAC;AAC7H,kBAAgBD,OAAMC,QAAO;AAE7B,kBAAgBD,OAAMC,UAAS,SAAS;AAC1C;AAwBA,SAAe,oBAAoB;AAAA;AACjC,QAAI;AAGF,YAAM,0BAA0B;AAAA,IAClC,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAIA,WAAO,OAAO,WAAW,eAAe,qBAAqB,KAAK,kBAAkB,KAAK,mBAAmB,aAAa,iBAAiB,UAAU,kBAAkB,UAAU,WAAW,UAAU,0BAA0B,UAAU,eAAe,kBAAkB,KAAK,iBAAiB,UAAU,eAAe,QAAQ;AAAA,EACnU;AAAA;AAkBA,SAAe,cAAc,WAAW;AAAA;AACtC,QAAI,CAAC,WAAW;AACd,YAAML,eAAc;AAAA,QAAO;AAAA;AAAA,MAA8D;AAAA,IAC3F;AACA,QAAI,CAAC,UAAU,gBAAgB;AAC7B,YAAM,kBAAkB,SAAS;AAAA,IACnC;AACA,WAAO,oBAAoB,SAAS;AAAA,EACtC;AAAA;AAkBA,SAAS,YAAY,WAAW,gBAAgB;AAC9C,MAAI,CAAC,WAAW;AACd,UAAMA,eAAc;AAAA,MAAO;AAAA;AAAA,IAA8D;AAAA,EAC3F;AACA,YAAU,mBAAmB;AAC7B,SAAO,MAAM;AACX,cAAU,mBAAmB;AAAA,EAC/B;AACF;AAyBA,SAAS,qBAAqB,MAAM,OAAO,GAAG;AAK5C,oBAAkB,EAAE,KAAK,iBAAe;AAEtC,QAAI,CAAC,aAAa;AAChB,YAAMA,eAAc;AAAA,QAAO;AAAA;AAAA,MAAyD;AAAA,IACtF;AAAA,EACF,GAAG,OAAK;AAEN,UAAMA,eAAc;AAAA,MAAO;AAAA;AAAA,IAA+D;AAAA,EAC5F,CAAC;AACD,SAAO,aAAa,mBAAmB,GAAG,GAAG,WAAW,EAAE,aAAa;AACzE;AAgBA,SAAeM,UAAS,WAAW,SAAS;AAAA;AAC1C,gBAAY,mBAAmB,SAAS;AACxC,WAAO,WAAW,WAAW,OAAO;AAAA,EACtC;AAAA;AAWA,SAAS,YAAY,WAAW;AAC9B,cAAY,mBAAmB,SAAS;AACxC,SAAO,cAAc,SAAS;AAChC;AAcA,SAAS,UAAU,WAAW,gBAAgB;AAC5C,cAAY,mBAAmB,SAAS;AACxC,SAAO,YAAY,WAAW,cAAc;AAC9C;AAQA,0BAA0B;;;ACtsCnB,IAAM,uBAAN,MAAM,8BAA6B,UAAU;AAAA,EAClD,cAAc;AACZ,UAAM;AACN,sBAAiB,EAAE,KAAK,eAAa;AACnC,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,YAAM,YAAY,qBAAa;AAC/B,gBAAU,WAAW,aAAW,KAAK,2BAA2B,OAAO,CAAC;AAAA,IAC1E,CAAC;AAAA,EACH;AAAA,EACM,mBAAmB;AAAA;AACvB,YAAM,cAAc,MAAM,kBAAiB;AAC3C,UAAI,CAAC,aAAa;AAChB,eAAO;AAAA,UACL,SAAS;AAAA,QACX;AAAA,MACF;AACA,YAAM,UAAU,KAAK,+CAA+C,aAAa,UAAU;AAC3F,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACM,qBAAqB;AAAA;AACzB,YAAM,cAAc,MAAM,kBAAiB;AAC3C,UAAI,CAAC,aAAa;AAChB,eAAO;AAAA,UACL,SAAS;AAAA,QACX;AAAA,MACF;AACA,YAAM,yBAAyB,MAAM,aAAa,kBAAkB;AACpE,YAAM,UAAU,KAAK,+CAA+C,sBAAsB;AAC1F,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACM,cAAc;AAAA;AAClB,YAAM,cAAc,MAAM,kBAAiB;AAC3C,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACM,SAAS,SAAS;AAAA;AACtB,YAAM,YAAY,qBAAa;AAC/B,YAAM,QAAQ,MAAMC,UAAS,WAAW;AAAA,QACtC,UAAU,QAAQ;AAAA,QAClB,2BAA2B,QAAQ;AAAA,MACrC,CAAC;AACD,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACM,cAAc;AAAA;AAClB,YAAM,YAAY,qBAAa;AAC/B,YAAM,YAAY,SAAS;AAAA,IAC7B;AAAA;AAAA,EACM,4BAA4B;AAAA;AAChC,WAAK,sBAAsB;AAAA,IAC7B;AAAA;AAAA,EACM,6BAA6B,UAAU;AAAA;AAC3C,WAAK,sBAAsB;AAAA,IAC7B;AAAA;AAAA,EACM,kCAAkC;AAAA;AACtC,WAAK,sBAAsB;AAAA,IAC7B;AAAA;AAAA,EACM,iBAAiB,UAAU;AAAA;AAC/B,WAAK,sBAAsB;AAAA,IAC7B;AAAA;AAAA,EACM,qBAAqB,UAAU;AAAA;AACnC,WAAK,sBAAsB;AAAA,IAC7B;AAAA;AAAA,EACM,cAAc,UAAU;AAAA;AAC5B,WAAK,sBAAsB;AAAA,IAC7B;AAAA;AAAA,EACM,cAAc,UAAU;AAAA;AAC5B,WAAK,sBAAsB;AAAA,IAC7B;AAAA;AAAA,EACM,eAAe;AAAA;AACnB,WAAK,sBAAsB;AAAA,IAC7B;AAAA;AAAA,EACA,2BAA2B,gBAAgB;AACzC,UAAM,eAAe,KAAK,yBAAyB,cAAc;AACjE,UAAM,QAAQ;AAAA,MACZ;AAAA,IACF;AACA,SAAK,gBAAgB,sBAAqB,2BAA2B,KAAK;AAAA,EAC5E;AAAA,EACA,yBAAyB,gBAAgB;AACvC,QAAI,IAAI,IAAI;AACZ,UAAM,eAAe;AAAA,MACnB,OAAO,KAAK,eAAe,kBAAkB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MACjF,MAAM,eAAe;AAAA,MACrB,IAAI,eAAe;AAAA,MACnB,QAAQ,KAAK,eAAe,kBAAkB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAClF,QAAQ,KAAK,eAAe,kBAAkB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,IACpF;AACA,WAAO;AAAA,EACT;AAAA,EACA,+CAA+C,YAAY;AACzD,QAAI,QAAQ;AACZ,YAAQ,YAAY;AAAA,MAClB,KAAK;AACH,gBAAQ;AACR;AAAA,MACF,KAAK;AACH,gBAAQ;AACR;AAAA,IACJ;AACA,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AACtB,UAAM,KAAK,YAAY,uBAAuB;AAAA,EAChD;AACF;AACA,qBAAqB,4BAA4B;", "names": ["name", "LogLevel", "name", "name", "version", "target", "DEFAULT_ENTRY_NAME", "name", "name", "DEFAULT_ENTRY_NAME", "name", "DEFAULT_ENTRY_NAME", "version", "name", "ERROR_FACTORY", "db<PERSON><PERSON><PERSON>", "getDbPromise", "installationEntry", "ERROR_FACTORY", "ERROR_FACTORY", "name", "MessageType", "base64", "db", "DATABASE_NAME", "DATABASE_VERSION", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "<PERSON><PERSON><PERSON>", "ERROR_FACTORY", "getHeaders", "extractAppConfig", "getMissingValueError", "name", "version", "getToken", "getToken"]}