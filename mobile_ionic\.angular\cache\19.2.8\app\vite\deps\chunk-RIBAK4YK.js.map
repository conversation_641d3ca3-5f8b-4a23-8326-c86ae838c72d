{"version": 3, "sources": ["../../../../../../node_modules/@stencil/core/internal/app-data/index.js", "../../../../../../node_modules/@stencil/core/internal/client/index.js"], "sourcesContent": ["// src/app-data/index.ts\nvar BUILD = {\n  allRenderFn: false,\n  cmpDidLoad: true,\n  cmpDidUnload: false,\n  cmpDidUpdate: true,\n  cmpDidRender: true,\n  cmpWillLoad: true,\n  cmpWillUpdate: true,\n  cmpWillRender: true,\n  connectedCallback: true,\n  disconnectedCallback: true,\n  element: true,\n  event: true,\n  hasRenderFn: true,\n  lifecycle: true,\n  hostListener: true,\n  hostListenerTargetWindow: true,\n  hostListenerTargetDocument: true,\n  hostListenerTargetBody: true,\n  hostListenerTargetParent: false,\n  hostListenerTarget: true,\n  member: true,\n  method: true,\n  mode: true,\n  observeAttribute: true,\n  prop: true,\n  propMutable: true,\n  reflect: true,\n  scoped: true,\n  shadowDom: true,\n  slot: true,\n  cssAnnotations: true,\n  state: true,\n  style: true,\n  formAssociated: false,\n  svg: true,\n  updatable: true,\n  vdomAttribute: true,\n  vdomXlink: true,\n  vdomClass: true,\n  vdomFunctional: true,\n  vdomKey: true,\n  vdomListener: true,\n  vdomRef: true,\n  vdomPropOrAttr: true,\n  vdomRender: true,\n  vdomStyle: true,\n  vdomText: true,\n  watchCallback: true,\n  taskQueue: true,\n  hotModuleReplacement: false,\n  isDebug: false,\n  isDev: false,\n  isTesting: false,\n  hydrateServerSide: false,\n  hydrateClientSide: false,\n  lifecycleDOMEvents: false,\n  lazyLoad: false,\n  profile: false,\n  slotRelocation: true,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  appendChildSlotFix: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  cloneNodeFix: false,\n  hydratedAttribute: false,\n  hydratedClass: true,\n  // TODO(STENCIL-1305): remove this option\n  scriptDataOpts: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  scopedSlotTextContentFix: false,\n  // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n  shadowDomShim: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  slotChildNodesFix: false,\n  invisiblePrehydration: true,\n  propBoolean: true,\n  propNumber: true,\n  propString: true,\n  constructableCSS: true,\n  cmpShouldUpdate: true,\n  devTools: false,\n  shadowDelegatesFocus: true,\n  initializeNextTick: false,\n  asyncLoading: false,\n  asyncQueue: false,\n  transformTagName: false,\n  attachStyles: true,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  experimentalSlotFixes: false\n};\nvar Env = {};\nvar NAMESPACE = /* default */\n\"app\";\nexport { BUILD, Env, NAMESPACE };", "/*\n Stencil Client Platform v4.20.0 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\n\n// src/client/client-build.ts\nimport { BUILD } from \"@stencil/core/internal/app-data\";\nvar Build = {\n  isDev: BUILD.isDev ? true : false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: BUILD.isTesting ? true : false\n};\n\n// src/client/client-host-ref.ts\nimport { BUILD as BUILD2 } from \"@stencil/core/internal/app-data\";\nvar hostRefs = BUILD2.hotModuleReplacement ? window.__STENCIL_HOSTREFS__ || (window.__STENCIL_HOSTREFS__ = /* @__PURE__ */new WeakMap()) : /* @__PURE__ */new WeakMap();\nvar getHostRef = ref => hostRefs.get(ref);\nvar registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */new Map()\n  };\n  if (BUILD2.isDev) {\n    hostRef.$renderCount$ = 0;\n  }\n  if (BUILD2.method && BUILD2.lazyLoad) {\n    hostRef.$onInstancePromise$ = new Promise(r => hostRef.$onInstanceResolve$ = r);\n  }\n  if (BUILD2.asyncLoading) {\n    hostRef.$onReadyPromise$ = new Promise(r => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  return hostRefs.set(hostElement, hostRef);\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\n\n// src/client/client-load-module.ts\nimport { BUILD as BUILD4 } from \"@stencil/core/internal/app-data\";\n\n// src/client/client-log.ts\nimport { BUILD as BUILD3 } from \"@stencil/core/internal/app-data\";\nvar customError;\nvar consoleError = (e, el) => (customError || console.error)(e, el);\nvar STENCIL_DEV_MODE = BUILD3.isTesting ? [\"STENCIL:\"] : [\"%cstencil\", \"color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px\"];\nvar consoleDevError = (...m) => console.error(...STENCIL_DEV_MODE, ...m);\nvar consoleDevWarn = (...m) => console.warn(...STENCIL_DEV_MODE, ...m);\nvar consoleDevInfo = (...m) => console.info(...STENCIL_DEV_MODE, ...m);\nvar setErrorHandler = handler => customError = handler;\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */new Map();\nvar MODULE_IMPORT_PREFIX = \"./\";\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (BUILD4.isDev && typeof bundleId !== \"string\") {\n    consoleDevError(`Trying to lazily load component <${cmpMeta.$tagName$}> with style mode \"${hostRef.$modeName$}\", but it does not exist.`);\n    return void 0;\n  } else if (!bundleId) {\n    return void 0;\n  }\n  const module = !BUILD4.hotModuleReplacement ? cmpModules.get(bundleId) : false;\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(/* @vite-ignore */\n  /* webpackInclude: /\\.entry\\.js$/ */\n  /* webpackExclude: /\\.system\\.entry\\.js$/ */\n  /* webpackMode: \"lazy\" */\n  `./${bundleId}.entry.js${BUILD4.hotModuleReplacement && hmrVersionId ? \"?s-hmr=\" + hmrVersionId : \"\"}`).then(importedModule => {\n    if (!BUILD4.hotModuleReplacement) {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, consoleError);\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */new Map();\nvar modeResolutionChain = [];\n\n// src/client/client-task-queue.ts\nimport { BUILD as BUILD6 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS = [\"formAssociatedCallback\", \"formResetCallback\", \"formDisabledCallback\", \"formStateRestoreCallback\"];\n\n// src/client/client-window.ts\nimport { BUILD as BUILD5 } from \"@stencil/core/internal/app-data\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar doc = win.document || {\n  head: {}\n};\nvar H = win.HTMLElement || class {};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: h2 => h2(),\n  raf: h2 => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar setPlatformHelpers = helpers => {\n  Object.assign(plt, helpers);\n};\nvar supportsShadow = BUILD5.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */(() => {\n  let supportsListenerOptions2 = false;\n  try {\n    doc.addEventListener(\"e\", null, Object.defineProperty({}, \"passive\", {\n      get() {\n        supportsListenerOptions2 = true;\n      }\n    }));\n  } catch (e) {}\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = v => Promise.resolve(v);\nvar supportsConstructableStylesheets = BUILD5.constructableCSS ? /* @__PURE__ */(() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {}\n  return false;\n})() : false;\n\n// src/client/client-task-queue.ts\nvar queueCongestion = 0;\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueDomWritesLow = [];\nvar queueTask = (queue, write) => cb => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = queue => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar consumeTimeout = (queue, timeout) => {\n  let i2 = 0;\n  let ts = 0;\n  while (i2 < queue.length && (ts = performance.now()) < timeout) {\n    try {\n      queue[i2++](ts);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  if (i2 === queue.length) {\n    queue.length = 0;\n  } else if (i2 !== 0) {\n    queue.splice(0, i2);\n  }\n};\nvar flush = () => {\n  if (BUILD6.asyncQueue) {\n    queueCongestion++;\n  }\n  consume(queueDomReads);\n  if (BUILD6.asyncQueue) {\n    const timeout = (plt.$flags$ & 6 /* queueMask */) === 2 /* appLoaded */ ? performance.now() + 14 * Math.ceil(queueCongestion * (1 / 10)) : Infinity;\n    consumeTimeout(queueDomWrites, timeout);\n    consumeTimeout(queueDomWritesLow, timeout);\n    if (queueDomWrites.length > 0) {\n      queueDomWritesLow.push(...queueDomWrites);\n      queueDomWrites.length = 0;\n    }\n    if (queuePending = queueDomReads.length + queueDomWrites.length + queueDomWritesLow.length > 0) {\n      plt.raf(flush);\n    } else {\n      queueCongestion = 0;\n    }\n  } else {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = cb => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */queueTask(queueDomWrites, true);\n\n// src/client/index.ts\nimport { BUILD as BUILD27, Env, NAMESPACE as NAMESPACE2 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/asset-path.ts\nvar getAssetPath = path => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nvar setAssetPath = path => plt.$resourcesUrl$ = path;\n\n// src/runtime/bootstrap-custom-element.ts\nimport { BUILD as BUILD24 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/constants.ts\nvar EMPTY_OBJ = {};\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\n\n// src/utils/helpers.ts\nvar isDef = v => v != null;\nvar isComplexType = o => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc2) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc2.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = value => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = value => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then(newVal => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = result => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = result => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\n\n// src/runtime/connected-callback.ts\nimport { BUILD as BUILD21 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/client-hydrate.ts\nimport { BUILD as BUILD9 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/profile.ts\nimport { BUILD as BUILD7 } from \"@stencil/core/internal/app-data\";\nvar i = 0;\nvar createTime = (fnName, tagName = \"\") => {\n  if (BUILD7.profile && performance.mark) {\n    const key = `st:${fnName}:${tagName}:${i++}`;\n    performance.mark(key);\n    return () => performance.measure(`[Stencil] ${fnName}() <${tagName}>`, key);\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  if (BUILD7.profile && performance.mark) {\n    if (performance.getEntriesByName(key, \"mark\").length === 0) {\n      performance.mark(key);\n    }\n    return () => {\n      if (performance.getEntriesByName(measureText, \"measure\").length === 0) {\n        performance.measure(measureText, key);\n      }\n    };\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar inspect = ref => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    return void 0;\n  }\n  const flags = hostRef.$flags$;\n  const hostElement = hostRef.$hostElement$;\n  return {\n    renderCount: hostRef.$renderCount$,\n    flags: {\n      hasRendered: !!(flags & 2 /* hasRendered */),\n      hasConnected: !!(flags & 1 /* hasConnected */),\n      isWaitingForChildren: !!(flags & 4 /* isWaitingForChildren */),\n      isConstructingInstance: !!(flags & 8 /* isConstructingInstance */),\n      isQueuedForUpdate: !!(flags & 16 /* isQueuedForUpdate */),\n      hasInitializedComponent: !!(flags & 32 /* hasInitializedComponent */),\n      hasLoadedComponent: !!(flags & 64 /* hasLoadedComponent */),\n      isWatchReady: !!(flags & 128 /* isWatchReady */),\n      isListenReady: !!(flags & 256 /* isListenReady */),\n      needsRerender: !!(flags & 512 /* needsRerender */)\n    },\n    instanceValues: hostRef.$instanceValues$,\n    ancestorComponent: hostRef.$ancestorComponent$,\n    hostElement,\n    lazyInstance: hostRef.$lazyInstance$,\n    vnode: hostRef.$vnode$,\n    modeName: hostRef.$modeName$,\n    onReadyPromise: hostRef.$onReadyPromise$,\n    onReadyResolve: hostRef.$onReadyResolve$,\n    onInstancePromise: hostRef.$onInstancePromise$,\n    onInstanceResolve: hostRef.$onInstanceResolve$,\n    onRenderResolve: hostRef.$onRenderResolve$,\n    queuedListeners: hostRef.$queuedListeners$,\n    rmListeners: hostRef.$rmListeners$,\n    [\"s-id\"]: hostElement[\"s-id\"],\n    [\"s-cr\"]: hostElement[\"s-cr\"],\n    [\"s-lr\"]: hostElement[\"s-lr\"],\n    [\"s-p\"]: hostElement[\"s-p\"],\n    [\"s-rc\"]: hostElement[\"s-rc\"],\n    [\"s-sc\"]: hostElement[\"s-sc\"]\n  };\n};\nvar installDevTools = () => {\n  if (BUILD7.devTools) {\n    const stencil = win.stencil = win.stencil || {};\n    const originalInspect = stencil.inspect;\n    stencil.inspect = ref => {\n      let result = inspect(ref);\n      if (!result && typeof originalInspect === \"function\") {\n        result = originalInspect(ref);\n      }\n      return result;\n    };\n  }\n};\n\n// src/runtime/vdom/h.ts\nimport { BUILD as BUILD8 } from \"@stencil/core/internal/app-data\";\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = c => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        } else if (BUILD8.isDev && typeof nodeName !== \"function\" && child.$flags$ === void 0) {\n          consoleDevError(`vNode passed as children has unexpected type.\nMake sure it's using the correct h() function.\nEmpty objects can also be the cause, look for JSX comments that became objects.`);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (BUILD8.isDev && nodeName === \"input\") {\n      validateInputProperties(vnodeData);\n    }\n    if (BUILD8.vdomKey && vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (BUILD8.slotRelocation && vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    if (BUILD8.vdomClass) {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter(k => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (BUILD8.isDev && vNodeChildren.some(isHost)) {\n    consoleDevError(`The <Host> must be the single root component. Make sure:\n- You are NOT using hostData() and <Host> in the same component.\n- <Host> is used once, and it's the single root component of the render() function.`);\n  }\n  if (BUILD8.vdomFunctional && typeof nodeName === \"function\") {\n    return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  if (BUILD8.vdomKey) {\n    vnode.$key$ = key;\n  }\n  if (BUILD8.slotRelocation) {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  if (BUILD8.vdomAttribute) {\n    vnode.$attrs$ = null;\n  }\n  if (BUILD8.vdomKey) {\n    vnode.$key$ = null;\n  }\n  if (BUILD8.slotRelocation) {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = node => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = node => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = node => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = {\n      ...node.vattrs\n    };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...(node.vchildren || []));\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\nvar validateInputProperties = inputElm => {\n  const props = Object.keys(inputElm);\n  const value = props.indexOf(\"value\");\n  if (value === -1) {\n    return;\n  }\n  const typeIndex = props.indexOf(\"type\");\n  const minIndex = props.indexOf(\"min\");\n  const maxIndex = props.indexOf(\"max\");\n  const stepIndex = props.indexOf(\"step\");\n  if (value < typeIndex || value < minIndex || value < maxIndex || value < stepIndex) {\n    consoleDevWarn(`The \"value\" prop of <input> should be set after \"min\", \"max\", \"type\" and \"step\"`);\n  }\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const shadowRootNodes = BUILD9.shadowDom && shadowRoot ? [] : null;\n  const vnode = hostRef.$vnode$ = newVNode(tagName, null);\n  if (!plt.$orgLocNodes$) {\n    initializeDocumentHydrate(doc.body, plt.$orgLocNodes$ = /* @__PURE__ */new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n  childRenderNodes.map(c => {\n    const orgLocationId = c.$hostId$ + \".\" + c.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = c.$elm$;\n    if (orgLocationNode && supportsShadow && orgLocationNode[\"s-en\"] === \"\") {\n      orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n    }\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName;\n      if (orgLocationNode) {\n        node[\"s-ol\"] = orgLocationNode;\n        node[\"s-ol\"][\"s-nr\"] = node;\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  });\n  if (BUILD9.shadowDom && shadowRoot) {\n    shadowRootNodes.map(shadowRootNode => {\n      if (shadowRootNode) {\n        shadowRoot.appendChild(shadowRootNode);\n      }\n    });\n  }\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = {\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          $attrs$: null,\n          $children$: null,\n          $key$: null,\n          $name$: null,\n          $text$: null\n        };\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        parentVNode.$children$[childVNode.$index$] = childVNode;\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i2], hostId);\n      }\n    }\n    for (i2 = node.childNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.childNodes[i2], hostId);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = {\n        $flags$: 0,\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4],\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      };\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = node.nextSibling;\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          childVNode.$tag$ = \"slot\";\n          if (childIdSplt[5]) {\n            node[\"s-sn\"] = childVNode.$name$ = childIdSplt[5];\n          } else {\n            node[\"s-sn\"] = \"\";\n          }\n          node[\"s-sr\"] = true;\n          if (BUILD9.shadowDom && shadowRootNodes) {\n            childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n            if (childVNode.$name$) {\n              childVNode.$elm$.setAttribute(\"name\", childVNode.$name$);\n            }\n            node.parentNode.insertBefore(childVNode.$elm$, node);\n            node.remove();\n            if (childVNode.$depth$ === \"0\") {\n              shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n            }\n          }\n          slotNodes.push(childVNode);\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (BUILD9.shadowDom && shadowRootNodes) {\n            node.remove();\n          } else if (BUILD9.slotRelocation) {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  }\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    for (i2 = 0; i2 < node.childNodes.length; i2++) {\n      initializeDocumentHydrate(node.childNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\n\n// src/runtime/initialize-component.ts\nimport { BUILD as BUILD20 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/mode.ts\nvar computeMode = elm => modeResolutionChain.map(h2 => h2(elm)).find(m => !!m);\nvar setMode = handler => modeResolutionChain.push(handler);\nvar getMode = ref => getHostRef(ref).$modeName$;\n\n// src/runtime/proxy-component.ts\nimport { BUILD as BUILD19 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/set-value.ts\nimport { BUILD as BUILD18 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/parse-property-value.ts\nimport { BUILD as BUILD10 } from \"@stencil/core/internal/app-data\";\nvar parsePropertyValue = (propValue, propType) => {\n  if (propValue != null && !isComplexType(propValue)) {\n    if (BUILD10.propBoolean && propType & 4 /* Boolean */) {\n      return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n    }\n    if (BUILD10.propNumber && propType & 2 /* Number */) {\n      return parseFloat(propValue);\n    }\n    if (BUILD10.propString && propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\n\n// src/runtime/update-component.ts\nimport { BUILD as BUILD17, NAMESPACE } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/event-emitter.ts\nimport { BUILD as BUILD12 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/element.ts\nimport { BUILD as BUILD11 } from \"@stencil/core/internal/app-data\";\nvar getElement = ref => BUILD11.lazyLoad ? getHostRef(ref).$hostElement$ : ref;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: detail => {\n      if (BUILD12.isDev && !elm.isConnected) {\n        consoleDevWarn(`The \"${name}\" event was emitted, but the dispatcher node is no longer connected to the dom.`);\n      }\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\n\n// src/runtime/styles.ts\nimport { BUILD as BUILD13 } from \"@stencil/core/internal/app-data\";\nvar rootAppliedStyles = /* @__PURE__ */new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  if (!BUILD13.attachStyles) {\n    return scopeId2;\n  }\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : doc;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (BUILD13.hydrateClientSide && styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = doc.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          if ((BUILD13.hydrateServerSide || BUILD13.hotModuleReplacement) && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n            styleElm.setAttribute(HYDRATED_STYLE_ID, scopeId2);\n          }\n          const injectStyle =\n          /**\n           * we render a scoped component\n           */\n          !(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) ||\n          /**\n          * we are using shadow dom and render the style tag within the shadowRoot\n          */\n          cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */ && styleContainerNode.nodeName !== \"HEAD\";\n          if (injectStyle) {\n            styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector(\"link\"));\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (BUILD13.constructableCSS && !styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = hostRef => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(BUILD13.shadowDom && supportsShadow && elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);\n  if ((BUILD13.shadowDom || BUILD13.scoped) && BUILD13.cssAnnotations && flags & 10 /* needsScopedEncapsulation */ && flags & 2 /* scopedCssEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n    if (BUILD13.scoped && flags & 2 /* scopedCssEncapsulation */) {\n      elm.classList.add(scopeId2 + \"-s\");\n    }\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (BUILD13.mode && mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\n\n// src/runtime/vdom/vdom-render.ts\nimport { BUILD as BUILD16 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/update-element.ts\nimport { BUILD as BUILD15 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/set-accessor.ts\nimport { BUILD as BUILD14 } from \"@stencil/core/internal/app-data\";\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n  if (oldValue !== newValue) {\n    let isProp = isMemberInElement(elm, memberName);\n    let ln = memberName.toLowerCase();\n    if (BUILD14.vdomClass && memberName === \"class\") {\n      const classList = elm.classList;\n      const oldClasses = parseClassList(oldValue);\n      const newClasses = parseClassList(newValue);\n      classList.remove(...oldClasses.filter(c => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter(c => c && !oldClasses.includes(c)));\n    } else if (BUILD14.vdomStyle && memberName === \"style\") {\n      if (BUILD14.updatable) {\n        for (const prop in oldValue) {\n          if (!newValue || newValue[prop] == null) {\n            if (!BUILD14.hydrateServerSide && prop.includes(\"-\")) {\n              elm.style.removeProperty(prop);\n            } else {\n              elm.style[prop] = \"\";\n            }\n          }\n        }\n      }\n      for (const prop in newValue) {\n        if (!oldValue || newValue[prop] !== oldValue[prop]) {\n          if (!BUILD14.hydrateServerSide && prop.includes(\"-\")) {\n            elm.style.setProperty(prop, newValue[prop]);\n          } else {\n            elm.style[prop] = newValue[prop];\n          }\n        }\n      }\n    } else if (BUILD14.vdomKey && memberName === \"key\") {} else if (BUILD14.vdomRef && memberName === \"ref\") {\n      if (newValue) {\n        newValue(elm);\n      }\n    } else if (BUILD14.vdomListener && (BUILD14.lazyLoad ? !isProp : !elm.__lookupSetter__(memberName)) && memberName[0] === \"o\" && memberName[1] === \"n\") {\n      if (memberName[2] === \"-\") {\n        memberName = memberName.slice(3);\n      } else if (isMemberInElement(win, ln)) {\n        memberName = ln.slice(2);\n      } else {\n        memberName = ln[2] + memberName.slice(3);\n      }\n      if (oldValue || newValue) {\n        const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n        memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n        if (oldValue) {\n          plt.rel(elm, memberName, oldValue, capture);\n        }\n        if (newValue) {\n          plt.ael(elm, memberName, newValue, capture);\n        }\n      }\n    } else if (BUILD14.vdomPropOrAttr) {\n      const isComplex = isComplexType(newValue);\n      if ((isProp || isComplex && newValue !== null) && !isSvg) {\n        try {\n          if (!elm.tagName.includes(\"-\")) {\n            const n = newValue == null ? \"\" : newValue;\n            if (memberName === \"list\") {\n              isProp = false;\n            } else if (oldValue == null || elm[memberName] != n) {\n              elm[memberName] = n;\n            }\n          } else {\n            elm[memberName] = newValue;\n          }\n        } catch (e) {}\n      }\n      let xlink = false;\n      if (BUILD14.vdomXlink) {\n        if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n          memberName = ln;\n          xlink = true;\n        }\n      }\n      if (newValue == null || newValue === false) {\n        if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n          if (BUILD14.vdomXlink && xlink) {\n            elm.removeAttributeNS(XLINK_NS, memberName);\n          } else {\n            elm.removeAttribute(memberName);\n          }\n        }\n      } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex) {\n        newValue = newValue === true ? \"\" : newValue;\n        if (BUILD14.vdomXlink && xlink) {\n          elm.setAttributeNS(XLINK_NS, memberName, newValue);\n        } else {\n          elm.setAttribute(memberName, newValue);\n        }\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = value => !value ? [] : value.split(parseClassListRegex);\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;\n  const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n  if (BUILD15.updatable) {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], void 0, isSvgMode2, newVnode.$flags$);\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode2, newVnode.$flags$);\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ?\n  // we need to sort these to ensure that `'ref'` is the last attr\n  [...attrNames.filter(attr => attr !== \"ref\"), \"ref\"] :\n  // no need to sort, return the original array\n  attrNames;\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (BUILD16.slotRelocation && !useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      if (scopeId) {\n        parentElm.classList.add(scopeId + \"-s\");\n      }\n      newVNode2.$flags$ |= newVNode2.$children$ ?\n      // slot element has fallback content\n      // still create an element that \"mocks\" the slot element\n      2 /* isSlotFallback */ :\n      // slot element does not have fallback content\n      // create an html comment we'll use to always reference\n      // where actual slot content should sit next to\n      1 /* isSlotReference */;\n    }\n  }\n  if (BUILD16.isDev && newVNode2.$elm$) {\n    consoleDevError(`The JSX ${newVNode2.$text$ !== null ? `\"${newVNode2.$text$}\" text` : `\"${newVNode2.$tag$}\" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`);\n  }\n  if (BUILD16.vdomText && newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = doc.createTextNode(newVNode2.$text$);\n  } else if (BUILD16.slotRelocation && newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = BUILD16.isDebug || BUILD16.hydrateServerSide ? slotReferenceDebugNode(newVNode2) : doc.createTextNode(\"\");\n  } else {\n    if (BUILD16.svg && !isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    elm = newVNode2.$elm$ = BUILD16.svg ? doc.createElementNS(isSvgMode ? SVG_NS : HTML_NS, !useNativeShadowDom && BUILD16.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$) : doc.createElement(!useNativeShadowDom && BUILD16.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$);\n    if (BUILD16.svg && isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    if (BUILD16.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    const rootNode = elm.getRootNode();\n    const isElementWithinShadowRoot = !rootNode.querySelector(\"body\");\n    if (!isElementWithinShadowRoot && BUILD16.scoped && isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    if (BUILD16.scoped) {\n      updateElementScopeIds(elm, parentElm);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2, elm);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    if (BUILD16.svg) {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  if (BUILD16.slotRelocation) {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        if (BUILD16.experimentalSlotFixes) {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        } else {\n          putBackInOriginalLocation(oldParentVNode.$elm$, false);\n        }\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = parentElm => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.childNodes).find(ref => ref[\"s-cr\"]);\n    const childNodeArray = Array.from(parentElm.childNodes);\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD16.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(parentReferenceNode(childNode), childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = BUILD16.slotRelocation && parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (BUILD16.shadowDom && containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx, parentElm);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, BUILD16.slotRelocation ? referenceNode(before) : before);\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        if (BUILD16.slotRelocation) {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if (BUILD16.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if (BUILD16.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      if (BUILD16.vdomKey) {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (BUILD16.vdomKey && idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld, parentElm);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx, parentElm);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        if (BUILD16.slotRelocation) {\n          insertBefore(parentReferenceNode(oldStartVnode.$elm$), node, referenceNode(oldStartVnode.$elm$));\n        } else {\n          insertBefore(oldStartVnode.$elm$.parentNode, node, oldStartVnode.$elm$);\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode2, newCh, newStartIdx, newEndIdx);\n  } else if (BUILD16.updatable && newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (BUILD16.slotRelocation && leftVNode.$tag$ === \"slot\") {\n      if (\n      // The component gets hydrated and no VDOM has been initialized.\n      // Here the comparison can't happen as $name$ property is not set for `leftNode`.\n      \"$nodeId$\" in leftVNode && isInitialRender &&\n      // `leftNode` is not from type HTMLComment which would cause many\n      // hydration comments to be removed\n      leftVNode.$elm$.nodeType !== 8) {\n        return false;\n      }\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (BUILD16.vdomKey && !isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = node => {\n  return node && node[\"s-ol\"] || node;\n};\nvar parentReferenceNode = node => (node[\"s-ol\"] ? node[\"s-ol\"] : node).parentNode;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (!BUILD16.vdomText || text === null) {\n    if (BUILD16.svg) {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    if (BUILD16.vdomAttribute || BUILD16.reflect) {\n      if (BUILD16.slot && tag === \"slot\" && !useNativeShadowDom) {\n        if (BUILD16.experimentalSlotFixes && oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      } else {\n        updateElement(oldVNode, newVNode2, isSvgMode);\n      }\n    }\n    if (BUILD16.updatable && oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (BUILD16.updatable && BUILD16.vdomText && oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n    // don't do this on initial render as it can cause non-hydrated content to be removed\n    !isInitialRender && BUILD16.updatable && oldChildren !== null) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (BUILD16.svg && isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if (BUILD16.vdomText && BUILD16.slotRelocation && (defaultHolder = elm[\"s-cr\"])) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (BUILD16.vdomText && oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar updateFallbackSlotVisibility = elm => {\n  const childNodes = elm.childNodes;\n  for (const childNode of childNodes) {\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      if (childNode[\"s-sr\"]) {\n        const slotName = childNode[\"s-sn\"];\n        childNode.hidden = false;\n        for (const siblingNode of childNodes) {\n          if (siblingNode !== childNode) {\n            if (siblingNode[\"s-hn\"] !== childNode[\"s-hn\"] || slotName !== \"\") {\n              if (siblingNode.nodeType === 1 /* ElementNode */ && (slotName === siblingNode.getAttribute(\"slot\") || slotName === siblingNode[\"s-sn\"]) || siblingNode.nodeType === 3 /* TextNode */ && slotName === siblingNode[\"s-sn\"]) {\n                childNode.hidden = true;\n                break;\n              }\n            } else {\n              if (siblingNode.nodeType === 1 /* ElementNode */ || siblingNode.nodeType === 3 /* TextNode */ && siblingNode.textContent.trim() !== \"\") {\n                childNode.hidden = true;\n                break;\n              }\n            }\n          }\n        }\n      }\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = elm => {\n  let node;\n  let hostContentNodes;\n  let j;\n  for (const childNode of elm.childNodes) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!BUILD16.experimentalSlotFixes || !node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map(relocateNode => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some(r => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar nullifyVNodeRefs = vNode => {\n  if (BUILD16.vdomRef) {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  const inserted = parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  if (BUILD16.scoped) {\n    updateElementScopeIds(newNode, parent);\n  }\n  return inserted;\n};\nvar findScopeIds = element => {\n  const scopeIds = [];\n  if (element) {\n    scopeIds.push(...(element[\"s-scs\"] || []), element[\"s-si\"], element[\"s-sc\"], ...findScopeIds(element.parentElement));\n  }\n  return scopeIds;\n};\nvar updateElementScopeIds = (element, parent, iterateChildNodes = false) => {\n  var _a;\n  if (element && parent && element.nodeType === 1 /* ElementNode */) {\n    const scopeIds = new Set(findScopeIds(parent).filter(Boolean));\n    if (scopeIds.size) {\n      (_a = element.classList) == null ? void 0 : _a.add(...(element[\"s-scs\"] = [...scopeIds]));\n      if (element[\"s-ol\"] || iterateChildNodes) {\n        for (const childNode of Array.from(element.childNodes)) {\n          updateElementScopeIds(childNode, element, true);\n        }\n      }\n    }\n  }\n};\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (BUILD16.isDev && Array.isArray(renderFnResults) && renderFnResults.some(isHost)) {\n    throw new Error(`The <Host> must be the single root component.\nLooks like the render() function of \"${hostTagName.toLowerCase()}\" is returning an array that contains the <Host>.\n\nThe render() function should look like this instead:\n\nrender() {\n  // Do not return an array\n  return (\n    <Host>{content}</Host>\n  );\n}\n  `);\n  }\n  if (BUILD16.reflect && cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]);\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = BUILD16.shadowDom ? hostElm.shadowRoot || hostElm : hostElm;\n  if (BUILD16.scoped || BUILD16.shadowDom) {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = supportsShadow && (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) !== 0;\n  if (BUILD16.slotRelocation) {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  if (BUILD16.slotRelocation) {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"]) {\n          const orgLocationNode = BUILD16.isDebug || BUILD16.hydrateServerSide ? originalLocationDebugNode(nodeToRelocate) : doc.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (!BUILD16.experimentalSlotFixes || insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === refNode.parentNode) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!BUILD16.experimentalSlotFixes && !nodeToRelocate[\"s-hn\"] && nodeToRelocate[\"s-ol\"]) {\n                nodeToRelocate[\"s-hn\"] = nodeToRelocate[\"s-ol\"].parentNode.nodeName;\n              }\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](nodeToRelocate);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= ~1 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (BUILD16.experimentalScopedSlotChanges && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    for (const childNode of rootVnode.$elm$.childNodes) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\nvar slotReferenceDebugNode = slotVNode => doc.createComment(`<slot${slotVNode.$name$ ? ' name=\"' + slotVNode.$name$ + '\"' : \"\"}> (host=${hostTagName.toLowerCase()})`);\nvar originalLocationDebugNode = nodeToRelocate => doc.createComment(`org-location for ` + (nodeToRelocate.localName ? `<${nodeToRelocate.localName}> (host=${nodeToRelocate[\"s-hn\"]})` : `[${nodeToRelocate.textContent}]`));\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (BUILD17.asyncLoading && ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    ancestorComponent[\"s-p\"].push(new Promise(r => hostRef.$onRenderResolve$ = r));\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  if (BUILD17.taskQueue && BUILD17.updatable) {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (BUILD17.asyncLoading && hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return BUILD17.taskQueue ? writeTask(dispatch) : dispatch();\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = BUILD17.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  if (!instance) {\n    throw new Error(`Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    if (BUILD17.lazyLoad && BUILD17.hostListener) {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentWillLoad\");\n    if (BUILD17.cmpWillLoad) {\n      maybePromise = safeCall(instance, \"componentWillLoad\");\n    }\n  } else {\n    emitLifecycleEvent(elm, \"componentWillUpdate\");\n    if (BUILD17.cmpWillUpdate) {\n      maybePromise = safeCall(instance, \"componentWillUpdate\");\n    }\n  }\n  emitLifecycleEvent(elm, \"componentWillRender\");\n  if (BUILD17.cmpWillRender) {\n    maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\"));\n  }\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch(err2 => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = maybePromise => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = async (hostRef, instance, isInitialLoad) => {\n  var _a;\n  const elm = hostRef.$hostElement$;\n  const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n  const rc = elm[\"s-rc\"];\n  if (BUILD17.style && isInitialLoad) {\n    attachStyles(hostRef);\n  }\n  const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n  if (BUILD17.isDev) {\n    hostRef.$flags$ |= 1024 /* devOnRender */;\n  }\n  if (BUILD17.hydrateServerSide) {\n    await callRender(hostRef, instance, elm, isInitialLoad);\n  } else {\n    callRender(hostRef, instance, elm, isInitialLoad);\n  }\n  if (BUILD17.isDev) {\n    hostRef.$renderCount$ = hostRef.$renderCount$ === void 0 ? 1 : hostRef.$renderCount$ + 1;\n    hostRef.$flags$ &= ~1024 /* devOnRender */;\n  }\n  if (BUILD17.hydrateServerSide) {\n    try {\n      serverSideConnected(elm);\n      if (isInitialLoad) {\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          elm[\"s-en\"] = \"\";\n        } else if (hostRef.$cmpMeta$.$flags$ & 2 /* scopedCssEncapsulation */) {\n          elm[\"s-en\"] = \"c\";\n        }\n      }\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  if (BUILD17.asyncLoading && rc) {\n    rc.map(cb => cb());\n    elm[\"s-rc\"] = void 0;\n  }\n  endRender();\n  endUpdate();\n  if (BUILD17.asyncLoading) {\n    const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n    const postUpdate = () => postUpdateComponent(hostRef);\n    if (childrenPromises.length === 0) {\n      postUpdate();\n    } else {\n      Promise.all(childrenPromises).then(postUpdate);\n      hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n      childrenPromises.length = 0;\n    }\n  } else {\n    postUpdateComponent(hostRef);\n  }\n};\nvar renderingRef = null;\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  const allRenderFn = BUILD17.allRenderFn ? true : false;\n  const lazyLoad = BUILD17.lazyLoad ? true : false;\n  const taskQueue = BUILD17.taskQueue ? true : false;\n  const updatable = BUILD17.updatable ? true : false;\n  try {\n    renderingRef = instance;\n    instance = allRenderFn ? instance.render() : instance.render && instance.render();\n    if (updatable && taskQueue) {\n      hostRef.$flags$ &= ~16 /* isQueuedForUpdate */;\n    }\n    if (updatable || lazyLoad) {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    if (BUILD17.hasRenderFn || BUILD17.reflect) {\n      if (BUILD17.vdomRender || BUILD17.reflect) {\n        if (BUILD17.hydrateServerSide) {\n          return Promise.resolve(instance).then(value => renderVdom(hostRef, value, isInitialLoad));\n        } else {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      } else {\n        const shadowRoot = elm.shadowRoot;\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          shadowRoot.textContent = instance;\n        } else {\n          elm.textContent = instance;\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  renderingRef = null;\n  return null;\n};\nvar getRenderingRef = () => renderingRef;\nvar postUpdateComponent = hostRef => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = BUILD17.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  if (BUILD17.cmpDidRender) {\n    if (BUILD17.isDev) {\n      hostRef.$flags$ |= 1024 /* devOnRender */;\n    }\n    safeCall(instance, \"componentDidRender\");\n    if (BUILD17.isDev) {\n      hostRef.$flags$ &= ~1024 /* devOnRender */;\n    }\n  }\n  emitLifecycleEvent(elm, \"componentDidRender\");\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    if (BUILD17.asyncLoading && BUILD17.cssAnnotations) {\n      addHydratedFlag(elm);\n    }\n    if (BUILD17.cmpDidLoad) {\n      if (BUILD17.isDev) {\n        hostRef.$flags$ |= 2048 /* devOnDidLoad */;\n      }\n      safeCall(instance, \"componentDidLoad\");\n      if (BUILD17.isDev) {\n        hostRef.$flags$ &= ~2048 /* devOnDidLoad */;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentDidLoad\");\n    endPostUpdate();\n    if (BUILD17.asyncLoading) {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad(tagName);\n      }\n    }\n  } else {\n    if (BUILD17.cmpDidUpdate) {\n      if (BUILD17.isDev) {\n        hostRef.$flags$ |= 1024 /* devOnRender */;\n      }\n      safeCall(instance, \"componentDidUpdate\");\n      if (BUILD17.isDev) {\n        hostRef.$flags$ &= ~1024 /* devOnRender */;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentDidUpdate\");\n    endPostUpdate();\n  }\n  if (BUILD17.method && BUILD17.lazyLoad) {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  if (BUILD17.asyncLoading) {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* isWaitingForChildren */ | 512 /* needsRerender */);\n  }\n};\nvar forceUpdate = ref => {\n  if (BUILD17.updatable && (Build.isBrowser || Build.isTesting)) {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n  return false;\n};\nvar appDidLoad = who => {\n  if (BUILD17.cssAnnotations) {\n    addHydratedFlag(doc.documentElement);\n  }\n  if (BUILD17.asyncQueue) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  nextTick(() => emitEvent(win, \"appload\", {\n    detail: {\n      namespace: NAMESPACE\n    }\n  }));\n  if (BUILD17.profile && performance.measure) {\n    performance.measure(`[Stencil] ${NAMESPACE} initial load (by ${who})`, \"st:app:start\");\n  }\n};\nvar safeCall = (instance, method, arg) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return void 0;\n};\nvar emitLifecycleEvent = (elm, lifecycleName) => {\n  if (BUILD17.lifecycleDOMEvents) {\n    emitEvent(elm, \"stencil_\" + lifecycleName, {\n      bubbles: true,\n      composed: true,\n      detail: {\n        namespace: NAMESPACE\n      }\n    });\n  }\n};\nvar addHydratedFlag = elm => {\n  var _a, _b;\n  return BUILD17.hydratedClass ? elm.classList.add((_a = BUILD17.hydratedSelectorName) != null ? _a : \"hydrated\") : BUILD17.hydratedAttribute ? elm.setAttribute((_b = BUILD17.hydratedSelectorName) != null ? _b : \"hydrated\", \"\") : void 0;\n};\nvar serverSideConnected = elm => {\n  const children = elm.children;\n  if (children != null) {\n    for (let i2 = 0, ii = children.length; i2 < ii; i2++) {\n      const childElm = children[i2];\n      if (typeof childElm.connectedCallback === \"function\") {\n        childElm.connectedCallback();\n      }\n      serverSideConnected(childElm);\n    }\n  }\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (BUILD18.lazyLoad && !hostRef) {\n    throw new Error(`Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/ionic-team/stencil/issues/5457).`);\n  }\n  const elm = BUILD18.lazyLoad ? hostRef.$hostElement$ : ref;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = BUILD18.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!BUILD18.lazyLoad || !(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (BUILD18.isDev) {\n      if (hostRef.$flags$ & 1024 /* devOnRender */) {\n        consoleDevWarn(`The state/prop \"${propName}\" changed during rendering. This can potentially lead to infinite-loops and other bugs.`, \"\\nElement\", elm, \"\\nNew value\", newVal, \"\\nOld value\", oldVal);\n      } else if (hostRef.$flags$ & 2048 /* devOnDidLoad */) {\n        consoleDevWarn(`The state/prop \"${propName}\" changed during \"componentDidLoad()\", this triggers extra re-renders, try to setup on \"componentWillLoad()\"`, \"\\nElement\", elm, \"\\nNew value\", newVal, \"\\nOld value\", oldVal);\n      }\n    }\n    if (!BUILD18.lazyLoad || instance) {\n      if (BUILD18.watchCallback && cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map(watchMethodName => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if (BUILD18.updatable && (flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        if (BUILD18.cmpShouldUpdate && instance.componentShouldUpdate) {\n          if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n            return;\n          }\n        }\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (BUILD19.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */ && flags & 1 /* isElementConstructor */) {\n    FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS.forEach(cbName => Object.defineProperty(prototype, cbName, {\n      value(...args) {\n        const hostRef = getHostRef(this);\n        const elm = BUILD19.lazyLoad ? hostRef.$hostElement$ : this;\n        const instance = BUILD19.lazyLoad ? hostRef.$lazyInstance$ : elm;\n        if (!instance) {\n          hostRef.$onReadyPromise$.then(instance2 => {\n            const cb = instance2[cbName];\n            typeof cb === \"function\" && cb.call(instance2, ...args);\n          });\n        } else {\n          const cb = instance[cbName];\n          typeof cb === \"function\" && cb.call(instance, ...args);\n        }\n      }\n    }));\n  }\n  if (BUILD19.member && cmpMeta.$members$ || BUILD19.watchCallback && (cmpMeta.$watchers$ || Cstr.watchers)) {\n    if (BUILD19.watchCallback && Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if ((BUILD19.prop || BUILD19.state) && (memberFlags & 31 /* Prop */ || (!BUILD19.lazyLoad || flags & 2 /* proxyState */) && memberFlags & 32 /* State */)) {\n        Object.defineProperty(prototype, memberName, {\n          get() {\n            return getValue(this, memberName);\n          },\n          set(newValue) {\n            if (BUILD19.isDev) {\n              const ref = getHostRef(this);\n              if (\n              // we are proxying the instance (not element)\n              (flags & 1 /* isElementConstructor */) === 0 &&\n              // the element is not constructing\n              (ref && ref.$flags$ & 8 /* isConstructingInstance */) === 0 &&\n              // the member is a prop\n              (memberFlags & 31 /* Prop */) !== 0 &&\n              // the member is not mutable\n              (memberFlags & 1024 /* Mutable */) === 0) {\n                consoleDevWarn(`@Prop() \"${memberName}\" on <${cmpMeta.$tagName$}> is immutable but was modified from within the component.\nMore information: https://stenciljs.com/docs/properties#prop-mutability`);\n              }\n            }\n            setValue(this, memberName, newValue, cmpMeta);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      } else if (BUILD19.lazyLoad && BUILD19.method && flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (BUILD19.observeAttribute && (!BUILD19.lazyLoad || flags & 1 /* isElementConstructor */)) {\n      const attrNameToPropName = /* @__PURE__ */new Map();\n      prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName)) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" &&\n          // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const elm = BUILD19.lazyLoad ? hostRef.$hostElement$ : this;\n              const instance = BUILD19.lazyLoad ? hostRef.$lazyInstance$ : elm;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach(callbackName => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          this[propName] = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n        });\n      };\n      Cstr.observedAttributes = Array.from(/* @__PURE__ */new Set([...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}), ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n        var _a2;\n        const attrName = m[1] || propName;\n        attrNameToPropName.set(attrName, propName);\n        if (BUILD19.reflect && m[0] & 512 /* ReflectAttr */) {\n          (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n        }\n        return attrName;\n      })]));\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n  let Cstr;\n  if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n    hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n    const bundleId = cmpMeta.$lazyBundleId$;\n    if ((BUILD20.lazyLoad || BUILD20.hydrateClientSide) && bundleId) {\n      const CstrImport = loadModule(cmpMeta, hostRef, hmrVersionId);\n      if (CstrImport && \"then\" in CstrImport) {\n        const endLoad = uniqueTime(`st:load:${cmpMeta.$tagName$}:${hostRef.$modeName$}`, `[Stencil] Load module for <${cmpMeta.$tagName$}>`);\n        Cstr = await CstrImport;\n        endLoad();\n      } else {\n        Cstr = CstrImport;\n      }\n      if (!Cstr) {\n        throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n      }\n      if (BUILD20.member && !Cstr.isProxied) {\n        if (BUILD20.watchCallback) {\n          cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n        Cstr.isProxied = true;\n      }\n      const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n      if (BUILD20.member) {\n        hostRef.$flags$ |= 8 /* isConstructingInstance */;\n      }\n      try {\n        new Cstr(hostRef);\n      } catch (e) {\n        consoleError(e);\n      }\n      if (BUILD20.member) {\n        hostRef.$flags$ &= ~8 /* isConstructingInstance */;\n      }\n      if (BUILD20.watchCallback) {\n        hostRef.$flags$ |= 128 /* isWatchReady */;\n      }\n      endNewInstance();\n      fireConnectedCallback(hostRef.$lazyInstance$);\n    } else {\n      Cstr = elm.constructor;\n      const cmpTag = elm.localName;\n      customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n    }\n    if (BUILD20.style && Cstr && Cstr.style) {\n      let style;\n      if (typeof Cstr.style === \"string\") {\n        style = Cstr.style;\n      } else if (BUILD20.mode && typeof Cstr.style !== \"string\") {\n        hostRef.$modeName$ = computeMode(elm);\n        if (hostRef.$modeName$) {\n          style = Cstr.style[hostRef.$modeName$];\n        }\n        if (BUILD20.hydrateServerSide && hostRef.$modeName$) {\n          elm.setAttribute(\"s-mode\", hostRef.$modeName$);\n        }\n      }\n      const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n      if (!styles.has(scopeId2)) {\n        const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n        if (!BUILD20.hydrateServerSide && BUILD20.shadowDom &&\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        BUILD20.shadowDomShim && cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n          style = await import(\"./shadow-css.js\").then(m => m.scopeCss(style, scopeId2));\n        }\n        registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n        endRegisterStyles();\n      }\n    }\n  }\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  const schedule = () => scheduleUpdate(hostRef, true);\n  if (BUILD20.asyncLoading && ancestorComponent && ancestorComponent[\"s-rc\"]) {\n    ancestorComponent[\"s-rc\"].push(schedule);\n  } else {\n    schedule();\n  }\n};\nvar fireConnectedCallback = instance => {\n  if (BUILD20.lazyLoad && BUILD20.connectedCallback) {\n    safeCall(instance, \"connectedCallback\");\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = elm => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (BUILD21.hostListenerTargetParent) {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, true);\n    }\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      if (BUILD21.hydrateClientSide) {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (BUILD21.shadowDom && supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = BUILD21.mode ? addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\")) : addStyle(elm.shadowRoot, cmpMeta);\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (BUILD21.slotRelocation && !hostId) {\n        if (BUILD21.hydrateServerSide || (BUILD21.slot || BUILD21.shadowDom) &&\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      if (BUILD21.asyncLoading) {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (BUILD21.hydrateClientSide && ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (BUILD21.prop && !BUILD21.hydrateServerSide && cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      if (BUILD21.initializeNextTick) {\n        nextTick(() => initializeComponent(elm, hostRef, cmpMeta));\n      } else {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, false);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = elm => {\n  const contentRefElm = elm[\"s-cr\"] = doc.createComment(BUILD21.isDebug ? `content-ref (host=${elm.localName})` : \"\");\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\n\n// src/runtime/disconnected-callback.ts\nimport { BUILD as BUILD22 } from \"@stencil/core/internal/app-data\";\nvar disconnectInstance = instance => {\n  if (BUILD22.lazyLoad && BUILD22.disconnectedCallback) {\n    safeCall(instance, \"disconnectedCallback\");\n  }\n  if (BUILD22.cmpDidUnload) {\n    safeCall(instance, \"componentDidUnload\");\n  }\n};\nvar disconnectedCallback = async elm => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    if (BUILD22.hostListener) {\n      if (hostRef.$rmListeners$) {\n        hostRef.$rmListeners$.map(rmListener => rmListener());\n        hostRef.$rmListeners$ = void 0;\n      }\n    }\n    if (!BUILD22.lazyLoad) {\n      disconnectInstance(elm);\n    } else if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n      disconnectInstance(hostRef.$lazyInstance$);\n    } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n      hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n    }\n  }\n};\n\n// src/runtime/dom-extras.ts\nimport { BUILD as BUILD23 } from \"@stencil/core/internal/app-data\";\nvar patchPseudoShadowDom = (hostElementPrototype, descriptorPrototype) => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype, descriptorPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = HostElementPrototype => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function (deep) {\n    const srcNode = this;\n    const isShadowDom = BUILD23.shadowDom ? srcNode.shadowRoot && supportsShadow : false;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (BUILD23.slot && !isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\"s-id\", \"s-cr\", \"s-lr\", \"s-rc\", \"s-sc\", \"s-p\", \"s-cn\", \"s-sr\", \"s-sn\", \"s-hn\", \"s-ol\", \"s-nr\", \"s-si\", \"s-rf\", \"s-scs\"];\n      for (; i2 < srcNode.childNodes.length; i2++) {\n        slotted = srcNode.childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every(privateField => !srcNode.childNodes[i2][privateField]);\n        if (slotted) {\n          if (BUILD23.appendChildSlotFix && clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(srcNode.childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = HostElementPrototype => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function (newChild) {\n    const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n    const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n    if (slotNode) {\n      const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const insertedNode = insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = ElementPrototype => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function (toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const slotNode = getHostSlotNode(this.childNodes, toRemove[\"s-sn\"], this.tagName);\n      if (slotNode) {\n        const slotChildNodes = getHostSlotChildNodes(slotNode, toRemove[\"s-sn\"]);\n        const existingNode = slotChildNodes.find(n => n === toRemove);\n        if (existingNode) {\n          existingNode.remove();\n          updateFallbackSlotVisibility(this);\n          return;\n        }\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = HostElementPrototype => {\n  const originalPrepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n      const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n      if (slotNode) {\n        const slotPlaceholder = document.createTextNode(\"\");\n        slotPlaceholder[\"s-nr\"] = newChild;\n        slotNode[\"s-cr\"].parentNode.__appendChild(slotPlaceholder);\n        newChild[\"s-ol\"] = slotPlaceholder;\n        const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        return insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return originalPrepend.call(this, newChild);\n    });\n  };\n};\nvar patchSlotAppend = HostElementPrototype => {\n  HostElementPrototype.append = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = HostElementPrototype => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function (position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = HostElementPrototype => {\n  HostElementPrototype.insertAdjacentText = function (position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchSlotInsertAdjacentElement = HostElementPrototype => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function (position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = hostElementPrototype => {\n  const descriptor = Object.getOwnPropertyDescriptor(Node.prototype, \"textContent\");\n  Object.defineProperty(hostElementPrototype, \"__textContent\", descriptor);\n  if (BUILD23.experimentalScopedSlotChanges) {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      // To mimic shadow root behavior, we need to return the text content of all\n      // nodes in a slot reference node\n      get() {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        const textContent = slotRefNodes.map(node => {\n          var _a, _b;\n          const text = [];\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            if (slotContent.nodeType === 3 /* TEXT_NODE */ || slotContent.nodeType === 1 /* ELEMENT_NODE */) {\n              text.push((_b = (_a = slotContent.textContent) == null ? void 0 : _a.trim()) != null ? _b : \"\");\n            }\n            slotContent = slotContent.nextSibling;\n          }\n          return text.filter(ref => ref !== \"\").join(\" \");\n        }).filter(text => text !== \"\").join(\" \");\n        return \" \" + textContent + \" \";\n      },\n      // To mimic shadow root behavior, we need to overwrite all nodes in a slot\n      // reference node. If a default slot reference node exists, the text content will be\n      // placed there. Otherwise, the new text node will be hidden\n      set(value) {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        slotRefNodes.forEach(node => {\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            const tmp = slotContent;\n            slotContent = slotContent.nextSibling;\n            tmp.remove();\n          }\n          if (node[\"s-sn\"] === \"\") {\n            const textNode = this.ownerDocument.createTextNode(value);\n            textNode[\"s-sn\"] = \"\";\n            insertBefore(node.parentElement, textNode, node.nextSibling);\n          } else {\n            node.remove();\n          }\n        });\n      }\n    });\n  } else {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      get() {\n        var _a;\n        const slotNode = getHostSlotNode(this.childNodes, \"\", this.tagName);\n        if (((_a = slotNode == null ? void 0 : slotNode.nextSibling) == null ? void 0 : _a.nodeType) === 3 /* TEXT_NODE */) {\n          return slotNode.nextSibling.textContent;\n        } else if (slotNode) {\n          return slotNode.textContent;\n        } else {\n          return this.__textContent;\n        }\n      },\n      set(value) {\n        var _a;\n        const slotNode = getHostSlotNode(this.childNodes, \"\", this.tagName);\n        if (((_a = slotNode == null ? void 0 : slotNode.nextSibling) == null ? void 0 : _a.nodeType) === 3 /* TEXT_NODE */) {\n          slotNode.nextSibling.textContent = value;\n        } else if (slotNode) {\n          slotNode.textContent = value;\n        } else {\n          this.__textContent = value;\n          const contentRefElm = this[\"s-cr\"];\n          if (contentRefElm) {\n            insertBefore(this, contentRefElm, this.firstChild);\n          }\n        }\n      }\n    });\n  }\n};\nvar patchChildSlotNodes = (elm, cmpMeta) => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  if (cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n    const childNodesFn = elm.__lookupGetter__(\"childNodes\");\n    Object.defineProperty(elm, \"children\", {\n      get() {\n        return this.childNodes.map(n => n.nodeType === 1);\n      }\n    });\n    Object.defineProperty(elm, \"childElementCount\", {\n      get() {\n        return elm.children.length;\n      }\n    });\n    Object.defineProperty(elm, \"childNodes\", {\n      get() {\n        const childNodes = childNodesFn.call(this);\n        if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0 && getHostRef(this).$flags$ & 2 /* hasRendered */) {\n          const result = new FakeNodeList();\n          for (let i2 = 0; i2 < childNodes.length; i2++) {\n            const slot = childNodes[i2][\"s-nr\"];\n            if (slot) {\n              result.push(slot);\n            }\n          }\n          return result;\n        }\n        return FakeNodeList.from(childNodes);\n      }\n    });\n  }\n};\nvar getAllChildSlotNodes = childNodes => {\n  const slotRefNodes = [];\n  for (const childNode of Array.from(childNodes)) {\n    if (childNode[\"s-sr\"]) {\n      slotRefNodes.push(childNode);\n    }\n    slotRefNodes.push(...getAllChildSlotNodes(childNode.childNodes));\n  }\n  return slotRefNodes;\n};\nvar getSlotName = node => node[\"s-sn\"] || node.nodeType === 1 && node.getAttribute(\"slot\") || \"\";\nvar getHostSlotNode = (childNodes, slotName, hostName) => {\n  let i2 = 0;\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && childNode[\"s-sn\"] === slotName && childNode[\"s-hn\"] === hostName) {\n      return childNode;\n    }\n    childNode = getHostSlotNode(childNode.childNodes, slotName, hostName);\n    if (childNode) {\n      return childNode;\n    }\n  }\n  return null;\n};\nvar getHostSlotChildNodes = (n, slotName) => {\n  const childNodes = [n];\n  while ((n = n.nextSibling) && n[\"s-sn\"] === slotName) {\n    childNodes.push(n);\n  }\n  return childNodes;\n};\n\n// src/runtime/bootstrap-custom-element.ts\nvar defineCustomElement = (Cstr, compactMeta) => {\n  customElements.define(compactMeta[1], proxyCustomElement(Cstr, compactMeta));\n};\nvar proxyCustomElement = (Cstr, compactMeta) => {\n  const cmpMeta = {\n    $flags$: compactMeta[0],\n    $tagName$: compactMeta[1]\n  };\n  if (BUILD24.member) {\n    cmpMeta.$members$ = compactMeta[2];\n  }\n  if (BUILD24.hostListener) {\n    cmpMeta.$listeners$ = compactMeta[3];\n  }\n  if (BUILD24.watchCallback) {\n    cmpMeta.$watchers$ = Cstr.$watchers$;\n  }\n  if (BUILD24.reflect) {\n    cmpMeta.$attrsToReflect$ = [];\n  }\n  if (BUILD24.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n    cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n  }\n  if (BUILD24.experimentalSlotFixes) {\n    if (BUILD24.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchPseudoShadowDom(Cstr.prototype, cmpMeta);\n    }\n  } else {\n    if (BUILD24.slotChildNodesFix) {\n      patchChildSlotNodes(Cstr.prototype, cmpMeta);\n    }\n    if (BUILD24.cloneNodeFix) {\n      patchCloneNode(Cstr.prototype);\n    }\n    if (BUILD24.appendChildSlotFix) {\n      patchSlotAppendChild(Cstr.prototype);\n    }\n    if (BUILD24.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchTextContent(Cstr.prototype);\n    }\n  }\n  const originalConnectedCallback = Cstr.prototype.connectedCallback;\n  const originalDisconnectedCallback = Cstr.prototype.disconnectedCallback;\n  Object.assign(Cstr.prototype, {\n    __registerHost() {\n      registerHost(this, cmpMeta);\n    },\n    connectedCallback() {\n      const hostRef = getHostRef(this);\n      addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n      connectedCallback(this);\n      if (BUILD24.connectedCallback && originalConnectedCallback) {\n        originalConnectedCallback.call(this);\n      }\n    },\n    disconnectedCallback() {\n      disconnectedCallback(this);\n      if (BUILD24.disconnectedCallback && originalDisconnectedCallback) {\n        originalDisconnectedCallback.call(this);\n      }\n    },\n    __attachShadow() {\n      if (supportsShadow) {\n        if (!this.shadowRoot) {\n          if (BUILD24.shadowDelegatesFocus) {\n            this.attachShadow({\n              mode: \"open\",\n              delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n            });\n          } else {\n            this.attachShadow({\n              mode: \"open\"\n            });\n          }\n        } else {\n          if (this.shadowRoot.mode !== \"open\") {\n            throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`);\n          }\n        }\n      } else {\n        this.shadowRoot = this;\n      }\n    }\n  });\n  Cstr.is = cmpMeta.$tagName$;\n  return proxyComponent(Cstr, cmpMeta, 1 /* isElementConstructor */ | 2 /* proxyState */);\n};\nvar forceModeUpdate = elm => {\n  if (BUILD24.style && BUILD24.mode && !BUILD24.lazyLoad) {\n    const mode = computeMode(elm);\n    const hostRef = getHostRef(elm);\n    if (hostRef.$modeName$ !== mode) {\n      const cmpMeta = hostRef.$cmpMeta$;\n      const oldScopeId = elm[\"s-sc\"];\n      const scopeId2 = getScopeId(cmpMeta, mode);\n      const style = elm.constructor.style[mode];\n      const flags = cmpMeta.$flags$;\n      if (style) {\n        if (!styles.has(scopeId2)) {\n          registerStyle(scopeId2, style, !!(flags & 1 /* shadowDomEncapsulation */));\n        }\n        hostRef.$modeName$ = mode;\n        elm.classList.remove(oldScopeId + \"-h\", oldScopeId + \"-s\");\n        attachStyles(hostRef);\n        forceUpdate(elm);\n      }\n    }\n  }\n};\n\n// src/runtime/bootstrap-lazy.ts\nimport { BUILD as BUILD25 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/hmr-component.ts\nvar hmrStart = (hostElement, cmpMeta, hmrVersionId) => {\n  const hostRef = getHostRef(hostElement);\n  hostRef.$flags$ = 1 /* hasConnected */;\n  initializeComponent(hostElement, hostRef, cmpMeta, hmrVersionId);\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  if (BUILD25.profile && performance.mark) {\n    performance.mark(\"st:app:start\");\n  }\n  installDevTools();\n  const endBootstrap = createTime(\"bootstrapLazy\");\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = doc.head;\n  const metaCharset = /* @__PURE__ */head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */doc.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", doc.baseURI).href;\n  if (BUILD25.asyncQueue) {\n    if (options.syncQueue) {\n      plt.$flags$ |= 4 /* queueSync */;\n    }\n  }\n  if (BUILD25.hydrateClientSide) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map(lazyBundle => {\n    lazyBundle[1].map(compactMeta => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      if (BUILD25.member) {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      if (BUILD25.hostListener) {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      if (BUILD25.reflect) {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      if (BUILD25.watchCallback) {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      if (BUILD25.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n        cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n      }\n      const tagName = BUILD25.transformTagName && options.transformTagName ? options.transformTagName(cmpMeta.$tagName$) : cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (BUILD25.shadowDom && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            if (supportsShadow) {\n              if (!self.shadowRoot) {\n                if (BUILD25.shadowDelegatesFocus) {\n                  self.attachShadow({\n                    mode: \"open\",\n                    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n                  });\n                } else {\n                  self.attachShadow({\n                    mode: \"open\"\n                  });\n                }\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`);\n                }\n              }\n            } else if (!BUILD25.hydrateServerSide && !(\"shadowRoot\" in self)) {\n              self.shadowRoot = self;\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      if (BUILD25.experimentalSlotFixes) {\n        if (BUILD25.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype, cmpMeta);\n        }\n      } else {\n        if (BUILD25.slotChildNodesFix) {\n          patchChildSlotNodes(HostElement.prototype, cmpMeta);\n        }\n        if (BUILD25.cloneNodeFix) {\n          patchCloneNode(HostElement.prototype);\n        }\n        if (BUILD25.appendChildSlotFix) {\n          patchSlotAppendChild(HostElement.prototype);\n        }\n        if (BUILD25.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchTextContent(HostElement.prototype);\n        }\n      }\n      if (BUILD25.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */) {\n        HostElement.formAssociated = true;\n      }\n      if (BUILD25.hotModuleReplacement) {\n        HostElement.prototype[\"s-hmr\"] = function (hmrVersionId) {\n          hmrStart(this, cmpMeta, hmrVersionId);\n        };\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */));\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    if (BUILD25.invisiblePrehydration && (BUILD25.hydratedClass || BUILD25.hydratedAttribute)) {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map(host => host.connectedCallback());\n  } else {\n    if (BUILD25.profile) {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30, \"timeout\"));\n    } else {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\n\n// src/runtime/fragment.ts\nvar Fragment = (_, children) => children;\n\n// src/runtime/host-listener.ts\nimport { BUILD as BUILD26 } from \"@stencil/core/internal/app-data\";\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (BUILD26.hostListener && listeners) {\n    if (BUILD26.hostListenerTargetParent) {\n      if (attachParentListeners) {\n        listeners = listeners.filter(([flags]) => flags & 32 /* TargetParent */);\n      } else {\n        listeners = listeners.filter(([flags]) => !(flags & 32 /* TargetParent */));\n      }\n    }\n    listeners.map(([flags, name, method]) => {\n      const target = BUILD26.hostListenerTarget ? getHostListenerTarget(elm, flags) : elm;\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => ev => {\n  var _a;\n  try {\n    if (BUILD26.lazyLoad) {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    } else {\n      hostRef.$hostElement$[methodName](ev);\n    }\n  } catch (e) {\n    consoleError(e);\n  }\n};\nvar getHostListenerTarget = (elm, flags) => {\n  if (BUILD26.hostListenerTargetDocument && flags & 4 /* TargetDocument */) return doc;\n  if (BUILD26.hostListenerTargetWindow && flags & 8 /* TargetWindow */) return win;\n  if (BUILD26.hostListenerTargetBody && flags & 16 /* TargetBody */) return doc.body;\n  if (BUILD26.hostListenerTargetParent && flags & 32 /* TargetParent */ && elm.parentElement) return elm.parentElement;\n  return elm;\n};\nvar hostListenerOpts = flags => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = nonce => plt.$nonce$ = nonce;\n\n// src/runtime/platform-options.ts\nvar setPlatformOptions = opts => Object.assign(plt, opts);\n\n// src/runtime/vdom/vdom-annotations.ts\nvar insertVdomAnnotations = (doc2, staticComponents) => {\n  if (doc2 != null) {\n    const docData = {\n      hostIds: 0,\n      rootLevelIds: 0,\n      staticComponents: new Set(staticComponents)\n    };\n    const orgLocationNodes = [];\n    parseVNodeAnnotations(doc2, doc2.body, docData, orgLocationNodes);\n    orgLocationNodes.forEach(orgLocationNode => {\n      var _a;\n      if (orgLocationNode != null && orgLocationNode[\"s-nr\"]) {\n        const nodeRef = orgLocationNode[\"s-nr\"];\n        let hostId = nodeRef[\"s-host-id\"];\n        let nodeId = nodeRef[\"s-node-id\"];\n        let childId = `${hostId}.${nodeId}`;\n        if (hostId == null) {\n          hostId = 0;\n          docData.rootLevelIds++;\n          nodeId = docData.rootLevelIds;\n          childId = `${hostId}.${nodeId}`;\n          if (nodeRef.nodeType === 1 /* ElementNode */) {\n            nodeRef.setAttribute(HYDRATE_CHILD_ID, childId);\n          } else if (nodeRef.nodeType === 3 /* TextNode */) {\n            if (hostId === 0) {\n              const textContent = (_a = nodeRef.nodeValue) == null ? void 0 : _a.trim();\n              if (textContent === \"\") {\n                orgLocationNode.remove();\n                return;\n              }\n            }\n            const commentBeforeTextNode = doc2.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${TEXT_NODE_ID}.${childId}`;\n            insertBefore(nodeRef.parentNode, commentBeforeTextNode, nodeRef);\n          }\n        }\n        let orgLocationNodeId = `${ORG_LOCATION_ID}.${childId}`;\n        const orgLocationParentNode = orgLocationNode.parentElement;\n        if (orgLocationParentNode) {\n          if (orgLocationParentNode[\"s-en\"] === \"\") {\n            orgLocationNodeId += `.`;\n          } else if (orgLocationParentNode[\"s-en\"] === \"c\") {\n            orgLocationNodeId += `.c`;\n          }\n        }\n        orgLocationNode.nodeValue = orgLocationNodeId;\n      }\n    });\n  }\n};\nvar parseVNodeAnnotations = (doc2, node, docData, orgLocationNodes) => {\n  var _a;\n  if (node == null) {\n    return;\n  }\n  if (node[\"s-nr\"] != null) {\n    orgLocationNodes.push(node);\n  }\n  if (node.nodeType === 1 /* ElementNode */) {\n    const childNodes = [...Array.from(node.childNodes), ...Array.from(((_a = node.shadowRoot) == null ? void 0 : _a.childNodes) || [])];\n    childNodes.forEach(childNode => {\n      const hostRef = getHostRef(childNode);\n      if (hostRef != null && !docData.staticComponents.has(childNode.nodeName.toLowerCase())) {\n        const cmpData = {\n          nodeIds: 0\n        };\n        insertVNodeAnnotations(doc2, childNode, hostRef.$vnode$, docData, cmpData);\n      }\n      parseVNodeAnnotations(doc2, childNode, docData, orgLocationNodes);\n    });\n  }\n};\nvar insertVNodeAnnotations = (doc2, hostElm, vnode, docData, cmpData) => {\n  if (vnode != null) {\n    const hostId = ++docData.hostIds;\n    hostElm.setAttribute(HYDRATE_ID, hostId);\n    if (hostElm[\"s-cr\"] != null) {\n      hostElm[\"s-cr\"].nodeValue = `${CONTENT_REF_ID}.${hostId}`;\n    }\n    if (vnode.$children$ != null) {\n      const depth = 0;\n      vnode.$children$.forEach((vnodeChild, index) => {\n        insertChildVNodeAnnotations(doc2, vnodeChild, cmpData, hostId, depth, index);\n      });\n    }\n    if (hostElm && vnode && vnode.$elm$ && !hostElm.hasAttribute(HYDRATE_CHILD_ID)) {\n      const parent = hostElm.parentElement;\n      if (parent && parent.childNodes) {\n        const parentChildNodes = Array.from(parent.childNodes);\n        const comment = parentChildNodes.find(node => node.nodeType === 8 /* CommentNode */ && node[\"s-sr\"]);\n        if (comment) {\n          const index = parentChildNodes.indexOf(hostElm) - 1;\n          vnode.$elm$.setAttribute(HYDRATE_CHILD_ID, `${comment[\"s-host-id\"]}.${comment[\"s-node-id\"]}.0.${index}`);\n        }\n      }\n    }\n  }\n};\nvar insertChildVNodeAnnotations = (doc2, vnodeChild, cmpData, hostId, depth, index) => {\n  const childElm = vnodeChild.$elm$;\n  if (childElm == null) {\n    return;\n  }\n  const nodeId = cmpData.nodeIds++;\n  const childId = `${hostId}.${nodeId}.${depth}.${index}`;\n  childElm[\"s-host-id\"] = hostId;\n  childElm[\"s-node-id\"] = nodeId;\n  if (childElm.nodeType === 1 /* ElementNode */) {\n    childElm.setAttribute(HYDRATE_CHILD_ID, childId);\n  } else if (childElm.nodeType === 3 /* TextNode */) {\n    const parentNode = childElm.parentNode;\n    const nodeName = parentNode == null ? void 0 : parentNode.nodeName;\n    if (nodeName !== \"STYLE\" && nodeName !== \"SCRIPT\") {\n      const textNodeId = `${TEXT_NODE_ID}.${childId}`;\n      const commentBeforeTextNode = doc2.createComment(textNodeId);\n      insertBefore(parentNode, commentBeforeTextNode, childElm);\n    }\n  } else if (childElm.nodeType === 8 /* CommentNode */) {\n    if (childElm[\"s-sr\"]) {\n      const slotName = childElm[\"s-sn\"] || \"\";\n      const slotNodeId = `${SLOT_NODE_ID}.${childId}.${slotName}`;\n      childElm.nodeValue = slotNodeId;\n    }\n  }\n  if (vnodeChild.$children$ != null) {\n    const childDepth = depth + 1;\n    vnodeChild.$children$.forEach((vnode, index2) => {\n      insertChildVNodeAnnotations(doc2, vnode, cmpData, hostId, childDepth, index2);\n    });\n  }\n};\nexport { BUILD27 as BUILD, Build, Env, Fragment, H, H as HTMLElement, Host, NAMESPACE2 as NAMESPACE, STENCIL_DEV_MODE, addHostEventListeners, bootstrapLazy, cmpModules, connectedCallback, consoleDevError, consoleDevInfo, consoleDevWarn, consoleError, createEvent, defineCustomElement, disconnectedCallback, doc, forceModeUpdate, forceUpdate, getAssetPath, getElement, getHostRef, getMode, getRenderingRef, getValue, h, insertVdomAnnotations, isMemberInElement, loadModule, modeResolutionChain, nextTick, parsePropertyValue, plt, postUpdateComponent, promiseResolve, proxyComponent, proxyCustomElement, readTask, registerHost, registerInstance, renderVdom, setAssetPath, setErrorHandler, setMode, setNonce, setPlatformHelpers, setPlatformOptions, setValue, styles, supportsConstructableStylesheets, supportsListenerOptions, supportsShadow, win, writeTask };"], "mappings": ";;;;;;;AACA,IAAI,QAAQ;AAAA,EACV,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,EACb,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,SAAS;AAAA,EACT,OAAO;AAAA,EACP,aAAa;AAAA,EACb,WAAW;AAAA,EACX,cAAc;AAAA,EACd,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,oBAAoB;AAAA,EACpB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,kBAAkB;AAAA,EAClB,MAAM;AAAA,EACN,aAAa;AAAA,EACb,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,KAAK;AAAA,EACL,WAAW;AAAA,EACX,eAAe;AAAA,EACf,WAAW;AAAA,EACX,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,eAAe;AAAA,EACf,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,gBAAgB;AAAA;AAAA,EAEhB,oBAAoB;AAAA;AAAA,EAEpB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,eAAe;AAAA;AAAA,EAEf,gBAAgB;AAAA;AAAA,EAEhB,0BAA0B;AAAA;AAAA,EAE1B,eAAe;AAAA;AAAA,EAEf,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,cAAc;AAAA;AAAA,EAEd,uBAAuB;AACzB;AAEA,IAAI;AAAA;AAAA,EACJ;AAAA;;;;;;AC1FA,IAAI,YAAY,OAAO;AACvB,IAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,WAAS,QAAQ,IAAK,WAAU,QAAQ,MAAM;AAAA,IAC5C,KAAK,IAAI,IAAI;AAAA,IACb,YAAY;AAAA,EACd,CAAC;AACH;AAIA,IAAI,QAAQ;AAAA,EACV,OAAO,MAAM,QAAQ,OAAO;AAAA,EAC5B,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW,MAAM,YAAY,OAAO;AACtC;AAIA,IAAI,WAAW,MAAO,uBAAuB,OAAO,yBAAyB,OAAO,uBAAsC,oBAAI,QAAQ,KAAoB,oBAAI,QAAQ;AACtK,IAAI,aAAa,SAAO,SAAS,IAAI,GAAG;AAExC,IAAI,eAAe,CAAC,aAAa,YAAY;AAC3C,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,eAAe;AAAA,IACf,WAAW;AAAA,IACX,kBAAiC,oBAAI,IAAI;AAAA,EAC3C;AACA,MAAI,MAAO,OAAO;AAChB,YAAQ,gBAAgB;AAAA,EAC1B;AACA,MAAI,MAAO,UAAU,MAAO,UAAU;AACpC,YAAQ,sBAAsB,IAAI,QAAQ,OAAK,QAAQ,sBAAsB,CAAC;AAAA,EAChF;AACA,MAAI,MAAO,cAAc;AACvB,YAAQ,mBAAmB,IAAI,QAAQ,OAAK,QAAQ,mBAAmB,CAAC;AACxE,gBAAY,KAAK,IAAI,CAAC;AACtB,gBAAY,MAAM,IAAI,CAAC;AAAA,EACzB;AACA,SAAO,SAAS,IAAI,aAAa,OAAO;AAC1C;AACA,IAAI,oBAAoB,CAAC,KAAK,eAAe,cAAc;AAO3D,IAAI;AACJ,IAAI,eAAe,CAAC,GAAG,QAAQ,eAAe,QAAQ,OAAO,GAAG,EAAE;AAClE,IAAI,mBAAmB,MAAO,YAAY,CAAC,UAAU,IAAI,CAAC,aAAa,wGAAwG;AAC/K,IAAI,kBAAkB,IAAI,MAAM,QAAQ,MAAM,GAAG,kBAAkB,GAAG,CAAC;AACvE,IAAI,iBAAiB,IAAI,MAAM,QAAQ,KAAK,GAAG,kBAAkB,GAAG,CAAC;AAKrE,IAAI,aAA4B,oBAAI,IAAI;AAExC,IAAI,aAAa,CAAC,SAAS,SAAS,iBAAiB;AACnD,QAAM,aAAa,QAAQ,UAAU,QAAQ,MAAM,GAAG;AACtD,QAAM,WAAW,QAAQ;AACzB,MAAI,MAAO,SAAS,OAAO,aAAa,UAAU;AAChD,oBAAgB,oCAAoC,QAAQ,SAAS,sBAAsB,QAAQ,UAAU,2BAA2B;AACxI,WAAO;AAAA,EACT,WAAW,CAAC,UAAU;AACpB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC,MAAO,uBAAuB,WAAW,IAAI,QAAQ,IAAI;AACzE,MAAI,QAAQ;AACV,WAAO,OAAO,UAAU;AAAA,EAC1B;AAEA,wIAIA,yBAAK,QAAQ,YAAY,MAAO,wBAAwB,eAAe,YAAY,eAAe,EAAE,IAAI,KAAK,oBAAkB;AAC7H,QAAI,CAAC,MAAO,sBAAsB;AAChC,iBAAW,IAAI,UAAU,cAAc;AAAA,IACzC;AACA,WAAO,eAAe,UAAU;AAAA,EAClC,GAAG,YAAY;AACjB;AAGA,IAAI,SAAwB,oBAAI,IAAI;AACpC,IAAI,sBAAsB,CAAC;AAM3B,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AAEvB,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,2CAA2C,CAAC,0BAA0B,qBAAqB,wBAAwB,0BAA0B;AAIjJ,IAAI,MAAM,OAAO,WAAW,cAAc,SAAS,CAAC;AACpD,IAAI,MAAM,IAAI,YAAY;AAAA,EACxB,MAAM,CAAC;AACT;AACA,IAAI,IAAI,IAAI,eAAe,MAAM;AAAC;AAClC,IAAI,MAAM;AAAA,EACR,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,KAAK,QAAM,GAAG;AAAA,EACd,KAAK,QAAM,sBAAsB,EAAE;AAAA,EACnC,KAAK,CAAC,IAAI,WAAW,UAAU,SAAS,GAAG,iBAAiB,WAAW,UAAU,IAAI;AAAA,EACrF,KAAK,CAAC,IAAI,WAAW,UAAU,SAAS,GAAG,oBAAoB,WAAW,UAAU,IAAI;AAAA,EACxF,IAAI,CAAC,WAAW,SAAS,IAAI,YAAY,WAAW,IAAI;AAC1D;AAIA,IAAI,iBAAiB,MAAO;AAC5B,IAAI,2BAA0C,MAAM;AAClD,MAAI,2BAA2B;AAC/B,MAAI;AACF,QAAI,iBAAiB,KAAK,MAAM,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,MACnE,MAAM;AACJ,mCAA2B;AAAA,MAC7B;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,SAAS,GAAG;AAAA,EAAC;AACb,SAAO;AACT,GAAG;AACH,IAAI,iBAAiB,OAAK,QAAQ,QAAQ,CAAC;AAC3C,IAAI,mCAAmC,MAAO,oBAAmC,MAAM;AACrF,MAAI;AACF,QAAI,cAAc;AAClB,WAAO,OAAO,IAAI,cAAc,EAAE,gBAAgB;AAAA,EACpD,SAAS,GAAG;AAAA,EAAC;AACb,SAAO;AACT,GAAG,IAAI;AAGP,IAAI,kBAAkB;AACtB,IAAI,eAAe;AACnB,IAAI,gBAAgB,CAAC;AACrB,IAAI,iBAAiB,CAAC;AACtB,IAAI,oBAAoB,CAAC;AACzB,IAAI,YAAY,CAAC,OAAO,UAAU,QAAM;AACtC,QAAM,KAAK,EAAE;AACb,MAAI,CAAC,cAAc;AACjB,mBAAe;AACf,QAAI,SAAS,IAAI,UAAU,GAAmB;AAC5C,eAAS,KAAK;AAAA,IAChB,OAAO;AACL,UAAI,IAAI,KAAK;AAAA,IACf;AAAA,EACF;AACF;AACA,IAAI,UAAU,WAAS;AACrB,WAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,MAAM;AACxC,QAAI;AACF,YAAM,EAAE,EAAE,YAAY,IAAI,CAAC;AAAA,IAC7B,SAAS,GAAG;AACV,mBAAa,CAAC;AAAA,IAChB;AAAA,EACF;AACA,QAAM,SAAS;AACjB;AACA,IAAI,iBAAiB,CAAC,OAAO,YAAY;AACvC,MAAI,KAAK;AACT,MAAI,KAAK;AACT,SAAO,KAAK,MAAM,WAAW,KAAK,YAAY,IAAI,KAAK,SAAS;AAC9D,QAAI;AACF,YAAM,IAAI,EAAE,EAAE;AAAA,IAChB,SAAS,GAAG;AACV,mBAAa,CAAC;AAAA,IAChB;AAAA,EACF;AACA,MAAI,OAAO,MAAM,QAAQ;AACvB,UAAM,SAAS;AAAA,EACjB,WAAW,OAAO,GAAG;AACnB,UAAM,OAAO,GAAG,EAAE;AAAA,EACpB;AACF;AACA,IAAI,QAAQ,MAAM;AAChB,MAAI,MAAO,YAAY;AACrB;AAAA,EACF;AACA,UAAQ,aAAa;AACrB,MAAI,MAAO,YAAY;AACrB,UAAM,WAAW,IAAI,UAAU,OAAuB,IAAoB,YAAY,IAAI,IAAI,KAAK,KAAK,KAAK,mBAAmB,IAAI,GAAG,IAAI;AAC3I,mBAAe,gBAAgB,OAAO;AACtC,mBAAe,mBAAmB,OAAO;AACzC,QAAI,eAAe,SAAS,GAAG;AAC7B,wBAAkB,KAAK,GAAG,cAAc;AACxC,qBAAe,SAAS;AAAA,IAC1B;AACA,QAAI,eAAe,cAAc,SAAS,eAAe,SAAS,kBAAkB,SAAS,GAAG;AAC9F,UAAI,IAAI,KAAK;AAAA,IACf,OAAO;AACL,wBAAkB;AAAA,IACpB;AAAA,EACF,OAAO;AACL,YAAQ,cAAc;AACtB,QAAI,eAAe,cAAc,SAAS,GAAG;AAC3C,UAAI,IAAI,KAAK;AAAA,IACf;AAAA,EACF;AACF;AACA,IAAI,WAAW,QAAM,eAAe,EAAE,KAAK,EAAE;AAC7C,IAAI,WAA0B,UAAU,eAAe,KAAK;AAC5D,IAAI,YAA2B,UAAU,gBAAgB,IAAI;AAM7D,IAAI,eAAe,UAAQ;AACzB,QAAM,WAAW,IAAI,IAAI,MAAM,IAAI,cAAc;AACjD,SAAO,SAAS,WAAW,IAAI,SAAS,SAAS,SAAS,OAAO,SAAS;AAC5E;AAOA,IAAI,YAAY,CAAC;AACjB,IAAI,SAAS;AACb,IAAI,UAAU;AAGd,IAAI,QAAQ,OAAK,KAAK;AACtB,IAAI,gBAAgB,OAAK;AACvB,MAAI,OAAO;AACX,SAAO,MAAM,YAAY,MAAM;AACjC;AAGA,SAAS,yBAAyB,MAAM;AACtC,MAAI,IAAI,IAAI;AACZ,UAAQ,MAAM,MAAM,KAAK,KAAK,SAAS,OAAO,SAAS,GAAG,cAAc,wBAAwB,MAAM,OAAO,SAAS,GAAG,aAAa,SAAS,MAAM,OAAO,KAAK;AACnK;AAGA,IAAI,iBAAiB,CAAC;AACtB,SAAS,gBAAgB;AAAA,EACvB,KAAK,MAAM;AAAA,EACX,KAAK,MAAM;AAAA,EACX,IAAI,MAAM;AAAA,EACV,QAAQ,MAAM;AAAA,EACd,WAAW,MAAM;AACnB,CAAC;AACD,IAAI,KAAK,YAAU;AAAA,EACjB,MAAM;AAAA,EACN,OAAO;AAAA,EACP;AACF;AACA,IAAI,MAAM,YAAU;AAAA,EAClB,MAAM;AAAA,EACN,OAAO;AAAA,EACP;AACF;AACA,SAAS,IAAI,QAAQ,IAAI;AACvB,MAAI,OAAO,MAAM;AACf,UAAM,MAAM,GAAG,OAAO,KAAK;AAC3B,QAAI,eAAe,SAAS;AAC1B,aAAO,IAAI,KAAK,YAAU,GAAG,MAAM,CAAC;AAAA,IACtC,OAAO;AACL,aAAO,GAAG,GAAG;AAAA,IACf;AAAA,EACF;AACA,MAAI,OAAO,OAAO;AAChB,UAAM,QAAQ,OAAO;AACrB,WAAO,IAAI,KAAK;AAAA,EAClB;AACA,QAAM;AACR;AACA,IAAI,SAAS,YAAU;AACrB,MAAI,OAAO,MAAM;AACf,WAAO,OAAO;AAAA,EAChB,OAAO;AACL,UAAM,OAAO;AAAA,EACf;AACF;AACA,IAAI,YAAY,YAAU;AACxB,MAAI,OAAO,OAAO;AAChB,WAAO,OAAO;AAAA,EAChB,OAAO;AACL,UAAM,OAAO;AAAA,EACf;AACF;AAUA,IAAI,IAAI;AACR,IAAI,aAAa,CAAC,QAAQ,UAAU,OAAO;AACzC,MAAI,MAAO,WAAW,YAAY,MAAM;AACtC,UAAM,MAAM,MAAM,MAAM,IAAI,OAAO,IAAI,GAAG;AAC1C,gBAAY,KAAK,GAAG;AACpB,WAAO,MAAM,YAAY,QAAQ,aAAa,MAAM,OAAO,OAAO,KAAK,GAAG;AAAA,EAC5E,OAAO;AACL,WAAO,MAAM;AACX;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,aAAa,CAAC,KAAK,gBAAgB;AACrC,MAAI,MAAO,WAAW,YAAY,MAAM;AACtC,QAAI,YAAY,iBAAiB,KAAK,MAAM,EAAE,WAAW,GAAG;AAC1D,kBAAY,KAAK,GAAG;AAAA,IACtB;AACA,WAAO,MAAM;AACX,UAAI,YAAY,iBAAiB,aAAa,SAAS,EAAE,WAAW,GAAG;AACrE,oBAAY,QAAQ,aAAa,GAAG;AAAA,MACtC;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,MAAM;AACX;AAAA,IACF;AAAA,EACF;AACF;AA2DA,IAAI,IAAI,CAAC,UAAU,cAAc,aAAa;AAC5C,MAAI,QAAQ;AACZ,MAAI,MAAM;AACV,MAAI,WAAW;AACf,MAAI,SAAS;AACb,MAAI,aAAa;AACjB,QAAM,gBAAgB,CAAC;AACvB,QAAM,OAAO,OAAK;AAChB,aAAS,KAAK,GAAG,KAAK,EAAE,QAAQ,MAAM;AACpC,cAAQ,EAAE,EAAE;AACZ,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAK,KAAK;AAAA,MACZ,WAAW,SAAS,QAAQ,OAAO,UAAU,WAAW;AACtD,YAAI,SAAS,OAAO,aAAa,cAAc,CAAC,cAAc,KAAK,GAAG;AACpE,kBAAQ,OAAO,KAAK;AAAA,QACtB,WAAW,MAAO,SAAS,OAAO,aAAa,cAAc,MAAM,YAAY,QAAQ;AACrF,0BAAgB;AAAA;AAAA,gFAEsD;AAAA,QACxE;AACA,YAAI,UAAU,YAAY;AACxB,wBAAc,cAAc,SAAS,CAAC,EAAE,UAAU;AAAA,QACpD,OAAO;AACL,wBAAc,KAAK,SAAS,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,QAC3D;AACA,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,OAAK,QAAQ;AACb,MAAI,WAAW;AACb,QAAI,MAAO,SAAS,aAAa,SAAS;AACxC,8BAAwB,SAAS;AAAA,IACnC;AACA,QAAI,MAAO,WAAW,UAAU,KAAK;AACnC,YAAM,UAAU;AAAA,IAClB;AACA,QAAI,MAAO,kBAAkB,UAAU,MAAM;AAC3C,iBAAW,UAAU;AAAA,IACvB;AACA,QAAI,MAAO,WAAW;AACpB,YAAM,YAAY,UAAU,aAAa,UAAU;AACnD,UAAI,WAAW;AACb,kBAAU,QAAQ,OAAO,cAAc,WAAW,YAAY,OAAO,KAAK,SAAS,EAAE,OAAO,OAAK,UAAU,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,MACzH;AAAA,IACF;AAAA,EACF;AACA,MAAI,MAAO,SAAS,cAAc,KAAK,MAAM,GAAG;AAC9C,oBAAgB;AAAA;AAAA,oFAEgE;AAAA,EAClF;AACA,MAAI,MAAO,kBAAkB,OAAO,aAAa,YAAY;AAC3D,WAAO,SAAS,cAAc,OAAO,CAAC,IAAI,WAAW,eAAe,WAAW;AAAA,EACjF;AACA,QAAM,QAAQ,SAAS,UAAU,IAAI;AACrC,QAAM,UAAU;AAChB,MAAI,cAAc,SAAS,GAAG;AAC5B,UAAM,aAAa;AAAA,EACrB;AACA,MAAI,MAAO,SAAS;AAClB,UAAM,QAAQ;AAAA,EAChB;AACA,MAAI,MAAO,gBAAgB;AACzB,UAAM,SAAS;AAAA,EACjB;AACA,SAAO;AACT;AACA,IAAI,WAAW,CAAC,KAAK,SAAS;AAC5B,QAAM,QAAQ;AAAA,IACZ,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AACA,MAAI,MAAO,eAAe;AACxB,UAAM,UAAU;AAAA,EAClB;AACA,MAAI,MAAO,SAAS;AAClB,UAAM,QAAQ;AAAA,EAChB;AACA,MAAI,MAAO,gBAAgB;AACzB,UAAM,SAAS;AAAA,EACjB;AACA,SAAO;AACT;AACA,IAAI,OAAO,CAAC;AACZ,IAAI,SAAS,UAAQ,QAAQ,KAAK,UAAU;AAC5C,IAAI,cAAc;AAAA,EAChB,SAAS,CAAC,UAAU,OAAO,SAAS,IAAI,eAAe,EAAE,QAAQ,EAAE;AAAA,EACnE,KAAK,CAAC,UAAU,OAAO,SAAS,IAAI,eAAe,EAAE,IAAI,EAAE,EAAE,IAAI,gBAAgB;AACnF;AACA,IAAI,kBAAkB,WAAS;AAAA,EAC7B,QAAQ,KAAK;AAAA,EACb,WAAW,KAAK;AAAA,EAChB,MAAM,KAAK;AAAA,EACX,OAAO,KAAK;AAAA,EACZ,MAAM,KAAK;AAAA,EACX,OAAO,KAAK;AACd;AACA,IAAI,mBAAmB,UAAQ;AAC7B,MAAI,OAAO,KAAK,SAAS,YAAY;AACnC,UAAM,YAAY,mBACb,KAAK;AAEV,QAAI,KAAK,MAAM;AACb,gBAAU,MAAM,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,OAAO;AACd,gBAAU,OAAO,KAAK;AAAA,IACxB;AACA,WAAO,EAAE,KAAK,MAAM,WAAW,GAAI,KAAK,aAAa,CAAC,CAAE;AAAA,EAC1D;AACA,QAAM,QAAQ,SAAS,KAAK,MAAM,KAAK,KAAK;AAC5C,QAAM,UAAU,KAAK;AACrB,QAAM,aAAa,KAAK;AACxB,QAAM,QAAQ,KAAK;AACnB,QAAM,SAAS,KAAK;AACpB,SAAO;AACT;AACA,IAAI,0BAA0B,cAAY;AACxC,QAAM,QAAQ,OAAO,KAAK,QAAQ;AAClC,QAAM,QAAQ,MAAM,QAAQ,OAAO;AACnC,MAAI,UAAU,IAAI;AAChB;AAAA,EACF;AACA,QAAM,YAAY,MAAM,QAAQ,MAAM;AACtC,QAAM,WAAW,MAAM,QAAQ,KAAK;AACpC,QAAM,WAAW,MAAM,QAAQ,KAAK;AACpC,QAAM,YAAY,MAAM,QAAQ,MAAM;AACtC,MAAI,QAAQ,aAAa,QAAQ,YAAY,QAAQ,YAAY,QAAQ,WAAW;AAClF,mBAAe,iFAAiF;AAAA,EAClG;AACF;AAGA,IAAI,0BAA0B,CAAC,SAAS,SAAS,QAAQ,YAAY;AACnE,QAAM,aAAa,WAAW,iBAAiB,OAAO;AACtD,QAAM,aAAa,QAAQ;AAC3B,QAAM,mBAAmB,CAAC;AAC1B,QAAM,YAAY,CAAC;AACnB,QAAM,kBAAkB,MAAO,aAAa,aAAa,CAAC,IAAI;AAC9D,QAAM,QAAQ,QAAQ,UAAU,SAAS,SAAS,IAAI;AACtD,MAAI,CAAC,IAAI,eAAe;AACtB,8BAA0B,IAAI,MAAM,IAAI,gBAA+B,oBAAI,IAAI,CAAC;AAAA,EAClF;AACA,UAAQ,UAAU,IAAI;AACtB,UAAQ,gBAAgB,UAAU;AAClC,gBAAc,OAAO,kBAAkB,WAAW,iBAAiB,SAAS,SAAS,MAAM;AAC3F,mBAAiB,IAAI,OAAK;AACxB,UAAM,gBAAgB,EAAE,WAAW,MAAM,EAAE;AAC3C,UAAM,kBAAkB,IAAI,cAAc,IAAI,aAAa;AAC3D,UAAM,OAAO,EAAE;AACf,QAAI,mBAAmB,kBAAkB,gBAAgB,MAAM,MAAM,IAAI;AACvE,sBAAgB,WAAW,aAAa,MAAM,gBAAgB,WAAW;AAAA,IAC3E;AACA,QAAI,CAAC,YAAY;AACf,WAAK,MAAM,IAAI;AACf,UAAI,iBAAiB;AACnB,aAAK,MAAM,IAAI;AACf,aAAK,MAAM,EAAE,MAAM,IAAI;AAAA,MACzB;AAAA,IACF;AACA,QAAI,cAAc,OAAO,aAAa;AAAA,EACxC,CAAC;AACD,MAAI,MAAO,aAAa,YAAY;AAClC,oBAAgB,IAAI,oBAAkB;AACpC,UAAI,gBAAgB;AAClB,mBAAW,YAAY,cAAc;AAAA,MACvC;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAW;AACb;AACA,IAAI,gBAAgB,CAAC,aAAa,kBAAkB,WAAW,iBAAiB,SAAS,MAAM,WAAW;AACxG,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,KAAK,aAAa,GAAqB;AACzC,oBAAgB,KAAK,aAAa,gBAAgB;AAClD,QAAI,eAAe;AACjB,oBAAc,cAAc,MAAM,GAAG;AACrC,UAAI,YAAY,CAAC,MAAM,UAAU,YAAY,CAAC,MAAM,KAAK;AACvD,qBAAa;AAAA,UACX,SAAS;AAAA,UACT,UAAU,YAAY,CAAC;AAAA,UACvB,UAAU,YAAY,CAAC;AAAA,UACvB,SAAS,YAAY,CAAC;AAAA,UACtB,SAAS,YAAY,CAAC;AAAA,UACtB,OAAO,KAAK,QAAQ,YAAY;AAAA,UAChC,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AACA,yBAAiB,KAAK,UAAU;AAChC,aAAK,gBAAgB,gBAAgB;AACrC,YAAI,CAAC,YAAY,YAAY;AAC3B,sBAAY,aAAa,CAAC;AAAA,QAC5B;AACA,oBAAY,WAAW,WAAW,OAAO,IAAI;AAC7C,sBAAc;AACd,YAAI,mBAAmB,WAAW,YAAY,KAAK;AACjD,0BAAgB,WAAW,OAAO,IAAI,WAAW;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,YAAY;AACnB,WAAK,KAAK,KAAK,WAAW,WAAW,SAAS,GAAG,MAAM,GAAG,MAAM;AAC9D,sBAAc,aAAa,kBAAkB,WAAW,iBAAiB,SAAS,KAAK,WAAW,WAAW,EAAE,GAAG,MAAM;AAAA,MAC1H;AAAA,IACF;AACA,SAAK,KAAK,KAAK,WAAW,SAAS,GAAG,MAAM,GAAG,MAAM;AACnD,oBAAc,aAAa,kBAAkB,WAAW,iBAAiB,SAAS,KAAK,WAAW,EAAE,GAAG,MAAM;AAAA,IAC/G;AAAA,EACF,WAAW,KAAK,aAAa,GAAqB;AAChD,kBAAc,KAAK,UAAU,MAAM,GAAG;AACtC,QAAI,YAAY,CAAC,MAAM,UAAU,YAAY,CAAC,MAAM,KAAK;AACvD,sBAAgB,YAAY,CAAC;AAC7B,mBAAa;AAAA,QACX,SAAS;AAAA,QACT,UAAU,YAAY,CAAC;AAAA,QACvB,UAAU,YAAY,CAAC;AAAA,QACvB,SAAS,YAAY,CAAC;AAAA,QACtB,SAAS,YAAY,CAAC;AAAA,QACtB,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,UAAI,kBAAkB,cAAc;AAClC,mBAAW,QAAQ,KAAK;AACxB,YAAI,WAAW,SAAS,WAAW,MAAM,aAAa,GAAkB;AACtE,qBAAW,SAAS,WAAW,MAAM;AACrC,2BAAiB,KAAK,UAAU;AAChC,eAAK,OAAO;AACZ,cAAI,CAAC,YAAY,YAAY;AAC3B,wBAAY,aAAa,CAAC;AAAA,UAC5B;AACA,sBAAY,WAAW,WAAW,OAAO,IAAI;AAC7C,cAAI,mBAAmB,WAAW,YAAY,KAAK;AACjD,4BAAgB,WAAW,OAAO,IAAI,WAAW;AAAA,UACnD;AAAA,QACF;AAAA,MACF,WAAW,WAAW,aAAa,QAAQ;AACzC,YAAI,kBAAkB,cAAc;AAClC,qBAAW,QAAQ;AACnB,cAAI,YAAY,CAAC,GAAG;AAClB,iBAAK,MAAM,IAAI,WAAW,SAAS,YAAY,CAAC;AAAA,UAClD,OAAO;AACL,iBAAK,MAAM,IAAI;AAAA,UACjB;AACA,eAAK,MAAM,IAAI;AACf,cAAI,MAAO,aAAa,iBAAiB;AACvC,uBAAW,QAAQ,IAAI,cAAc,WAAW,KAAK;AACrD,gBAAI,WAAW,QAAQ;AACrB,yBAAW,MAAM,aAAa,QAAQ,WAAW,MAAM;AAAA,YACzD;AACA,iBAAK,WAAW,aAAa,WAAW,OAAO,IAAI;AACnD,iBAAK,OAAO;AACZ,gBAAI,WAAW,YAAY,KAAK;AAC9B,8BAAgB,WAAW,OAAO,IAAI,WAAW;AAAA,YACnD;AAAA,UACF;AACA,oBAAU,KAAK,UAAU;AACzB,cAAI,CAAC,YAAY,YAAY;AAC3B,wBAAY,aAAa,CAAC;AAAA,UAC5B;AACA,sBAAY,WAAW,WAAW,OAAO,IAAI;AAAA,QAC/C,WAAW,kBAAkB,gBAAgB;AAC3C,cAAI,MAAO,aAAa,iBAAiB;AACvC,iBAAK,OAAO;AAAA,UACd,WAAW,MAAO,gBAAgB;AAChC,oBAAQ,MAAM,IAAI;AAClB,iBAAK,MAAM,IAAI;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAW,eAAe,YAAY,UAAU,SAAS;AACvD,UAAM,QAAQ,SAAS,MAAM,KAAK,WAAW;AAC7C,UAAM,QAAQ;AACd,UAAM,UAAU;AAChB,gBAAY,aAAa,CAAC,KAAK;AAAA,EACjC;AACF;AACA,IAAI,4BAA4B,CAAC,MAAM,gBAAgB;AACrD,MAAI,KAAK,aAAa,GAAqB;AACzC,QAAI,KAAK;AACT,QAAI,KAAK,YAAY;AACnB,aAAO,KAAK,KAAK,WAAW,WAAW,QAAQ,MAAM;AACnD,kCAA0B,KAAK,WAAW,WAAW,EAAE,GAAG,WAAW;AAAA,MACvE;AAAA,IACF;AACA,SAAK,KAAK,GAAG,KAAK,KAAK,WAAW,QAAQ,MAAM;AAC9C,gCAA0B,KAAK,WAAW,EAAE,GAAG,WAAW;AAAA,IAC5D;AAAA,EACF,WAAW,KAAK,aAAa,GAAqB;AAChD,UAAM,cAAc,KAAK,UAAU,MAAM,GAAG;AAC5C,QAAI,YAAY,CAAC,MAAM,iBAAiB;AACtC,kBAAY,IAAI,YAAY,CAAC,IAAI,MAAM,YAAY,CAAC,GAAG,IAAI;AAC3D,WAAK,YAAY;AACjB,WAAK,MAAM,IAAI,YAAY,CAAC;AAAA,IAC9B;AAAA,EACF;AACF;AAMA,IAAI,cAAc,SAAO,oBAAoB,IAAI,QAAM,GAAG,GAAG,CAAC,EAAE,KAAK,OAAK,CAAC,CAAC,CAAC;AAC7E,IAAI,UAAU,aAAW,oBAAoB,KAAK,OAAO;AACzD,IAAI,UAAU,SAAO,WAAW,GAAG,EAAE;AAUrC,IAAI,qBAAqB,CAAC,WAAW,aAAa;AAChD,MAAI,aAAa,QAAQ,CAAC,cAAc,SAAS,GAAG;AAClD,QAAI,MAAQ,eAAe,WAAW,GAAiB;AACrD,aAAO,cAAc,UAAU,QAAQ,cAAc,MAAM,CAAC,CAAC;AAAA,IAC/D;AACA,QAAI,MAAQ,cAAc,WAAW,GAAgB;AACnD,aAAO,WAAW,SAAS;AAAA,IAC7B;AACA,QAAI,MAAQ,cAAc,WAAW,GAAgB;AACnD,aAAO,OAAO,SAAS;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAUA,IAAI,aAAa,SAAO,MAAQ,WAAW,WAAW,GAAG,EAAE,gBAAgB;AAG3E,IAAI,cAAc,CAAC,KAAK,MAAM,UAAU;AACtC,QAAM,MAAM,WAAW,GAAG;AAC1B,SAAO;AAAA,IACL,MAAM,YAAU;AACd,UAAI,MAAQ,SAAS,CAAC,IAAI,aAAa;AACrC,uBAAe,QAAQ,IAAI,iFAAiF;AAAA,MAC9G;AACA,aAAO,UAAU,KAAK,MAAM;AAAA,QAC1B,SAAS,CAAC,EAAE,QAAQ;AAAA,QACpB,UAAU,CAAC,EAAE,QAAQ;AAAA,QACrB,YAAY,CAAC,EAAE,QAAQ;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,IAAI,YAAY,CAAC,KAAK,MAAM,SAAS;AACnC,QAAM,KAAK,IAAI,GAAG,MAAM,IAAI;AAC5B,MAAI,cAAc,EAAE;AACpB,SAAO;AACT;AAIA,IAAI,oBAAmC,oBAAI,QAAQ;AACnD,IAAI,gBAAgB,CAAC,UAAU,SAAS,YAAY;AAClD,MAAI,QAAQ,OAAO,IAAI,QAAQ;AAC/B,MAAI,oCAAoC,SAAS;AAC/C,YAAQ,SAAS,IAAI,cAAc;AACnC,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ;AAAA,IACV,OAAO;AACL,YAAM,YAAY,OAAO;AAAA,IAC3B;AAAA,EACF,OAAO;AACL,YAAQ;AAAA,EACV;AACA,SAAO,IAAI,UAAU,KAAK;AAC5B;AACA,IAAI,WAAW,CAAC,oBAAoB,SAAS,SAAS;AACpD,MAAI;AACJ,QAAM,WAAW,WAAW,SAAS,IAAI;AACzC,QAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,MAAI,CAAC,MAAQ,cAAc;AACzB,WAAO;AAAA,EACT;AACA,uBAAqB,mBAAmB,aAAa,KAA4B,qBAAqB;AACtG,MAAI,OAAO;AACT,QAAI,OAAO,UAAU,UAAU;AAC7B,2BAAqB,mBAAmB,QAAQ;AAChD,UAAI,gBAAgB,kBAAkB,IAAI,kBAAkB;AAC5D,UAAI;AACJ,UAAI,CAAC,eAAe;AAClB,0BAAkB,IAAI,oBAAoB,gBAA+B,oBAAI,IAAI,CAAC;AAAA,MACpF;AACA,UAAI,CAAC,cAAc,IAAI,QAAQ,GAAG;AAChC,YAAI,MAAQ,qBAAqB,mBAAmB,SAAS,WAAW,mBAAmB,cAAc,IAAI,iBAAiB,KAAK,QAAQ,IAAI,IAAI;AACjJ,mBAAS,YAAY;AAAA,QACvB,OAAO;AACL,qBAAW,IAAI,cAAc,OAAO;AACpC,mBAAS,YAAY;AACrB,gBAAM,SAAS,KAAK,IAAI,YAAY,OAAO,KAAK,yBAAyB,GAAG;AAC5E,cAAI,SAAS,MAAM;AACjB,qBAAS,aAAa,SAAS,KAAK;AAAA,UACtC;AACA,eAAK,MAAQ,qBAAqB,MAAQ,yBAAyB,QAAQ,UAAU,GAAgC;AACnH,qBAAS,aAAa,mBAAmB,QAAQ;AAAA,UACnD;AACA,gBAAM;AAAA;AAAA;AAAA;AAAA,YAIN,EAAE,QAAQ,UAAU;AAAA;AAAA;AAAA,YAIpB,QAAQ,UAAU,KAAkC,mBAAmB,aAAa;AAAA;AACpF,cAAI,aAAa;AACf,+BAAmB,aAAa,UAAU,mBAAmB,cAAc,MAAM,CAAC;AAAA,UACpF;AAAA,QACF;AACA,YAAI,QAAQ,UAAU,GAA2B;AAC/C,mBAAS,aAAa;AAAA,QACxB;AACA,YAAI,eAAe;AACjB,wBAAc,IAAI,QAAQ;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,WAAW,MAAQ,oBAAoB,CAAC,mBAAmB,mBAAmB,SAAS,KAAK,GAAG;AAC7F,yBAAmB,qBAAqB,CAAC,GAAG,mBAAmB,oBAAoB,KAAK;AAAA,IAC1F;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,eAAe,aAAW;AAC5B,QAAM,UAAU,QAAQ;AACxB,QAAM,MAAM,QAAQ;AACpB,QAAM,QAAQ,QAAQ;AACtB,QAAM,kBAAkB,WAAW,gBAAgB,QAAQ,SAAS;AACpE,QAAM,WAAW,SAAS,MAAQ,aAAa,kBAAkB,IAAI,aAAa,IAAI,aAAa,IAAI,YAAY,GAAG,SAAS,QAAQ,UAAU;AACjJ,OAAK,MAAQ,aAAa,MAAQ,WAAW,MAAQ,kBAAkB,QAAQ,MAAqC,QAAQ,GAAgC;AAC1J,QAAI,MAAM,IAAI;AACd,QAAI,UAAU,IAAI,WAAW,IAAI;AACjC,QAAI,MAAQ,UAAU,QAAQ,GAAgC;AAC5D,UAAI,UAAU,IAAI,WAAW,IAAI;AAAA,IACnC;AAAA,EACF;AACA,kBAAgB;AAClB;AACA,IAAI,aAAa,CAAC,KAAK,SAAS,SAAS,MAAQ,QAAQ,QAAQ,IAAI,UAAU,KAAmB,IAAI,YAAY,MAAM,OAAO,IAAI;AAUnI,IAAI,cAAc,CAAC,KAAK,YAAY,UAAU,UAAU,OAAO,UAAU;AACvE,MAAI,aAAa,UAAU;AACzB,QAAI,SAAS,kBAAkB,KAAK,UAAU;AAC9C,QAAI,KAAK,WAAW,YAAY;AAChC,QAAI,MAAQ,aAAa,eAAe,SAAS;AAC/C,YAAM,YAAY,IAAI;AACtB,YAAM,aAAa,eAAe,QAAQ;AAC1C,YAAM,aAAa,eAAe,QAAQ;AAC1C,gBAAU,OAAO,GAAG,WAAW,OAAO,OAAK,KAAK,CAAC,WAAW,SAAS,CAAC,CAAC,CAAC;AACxE,gBAAU,IAAI,GAAG,WAAW,OAAO,OAAK,KAAK,CAAC,WAAW,SAAS,CAAC,CAAC,CAAC;AAAA,IACvE,WAAW,MAAQ,aAAa,eAAe,SAAS;AACtD,UAAI,MAAQ,WAAW;AACrB,mBAAW,QAAQ,UAAU;AAC3B,cAAI,CAAC,YAAY,SAAS,IAAI,KAAK,MAAM;AACvC,gBAAI,CAAC,MAAQ,qBAAqB,KAAK,SAAS,GAAG,GAAG;AACpD,kBAAI,MAAM,eAAe,IAAI;AAAA,YAC/B,OAAO;AACL,kBAAI,MAAM,IAAI,IAAI;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,iBAAW,QAAQ,UAAU;AAC3B,YAAI,CAAC,YAAY,SAAS,IAAI,MAAM,SAAS,IAAI,GAAG;AAClD,cAAI,CAAC,MAAQ,qBAAqB,KAAK,SAAS,GAAG,GAAG;AACpD,gBAAI,MAAM,YAAY,MAAM,SAAS,IAAI,CAAC;AAAA,UAC5C,OAAO;AACL,gBAAI,MAAM,IAAI,IAAI,SAAS,IAAI;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,MAAQ,WAAW,eAAe,OAAO;AAAA,IAAC,WAAW,MAAQ,WAAW,eAAe,OAAO;AACvG,UAAI,UAAU;AACZ,iBAAS,GAAG;AAAA,MACd;AAAA,IACF,WAAW,MAAQ,iBAAiB,MAAQ,WAAW,CAAC,SAAS,CAAC,IAAI,iBAAiB,UAAU,MAAM,WAAW,CAAC,MAAM,OAAO,WAAW,CAAC,MAAM,KAAK;AACrJ,UAAI,WAAW,CAAC,MAAM,KAAK;AACzB,qBAAa,WAAW,MAAM,CAAC;AAAA,MACjC,WAAW,kBAAkB,KAAK,EAAE,GAAG;AACrC,qBAAa,GAAG,MAAM,CAAC;AAAA,MACzB,OAAO;AACL,qBAAa,GAAG,CAAC,IAAI,WAAW,MAAM,CAAC;AAAA,MACzC;AACA,UAAI,YAAY,UAAU;AACxB,cAAM,UAAU,WAAW,SAAS,oBAAoB;AACxD,qBAAa,WAAW,QAAQ,qBAAqB,EAAE;AACvD,YAAI,UAAU;AACZ,cAAI,IAAI,KAAK,YAAY,UAAU,OAAO;AAAA,QAC5C;AACA,YAAI,UAAU;AACZ,cAAI,IAAI,KAAK,YAAY,UAAU,OAAO;AAAA,QAC5C;AAAA,MACF;AAAA,IACF,WAAW,MAAQ,gBAAgB;AACjC,YAAM,YAAY,cAAc,QAAQ;AACxC,WAAK,UAAU,aAAa,aAAa,SAAS,CAAC,OAAO;AACxD,YAAI;AACF,cAAI,CAAC,IAAI,QAAQ,SAAS,GAAG,GAAG;AAC9B,kBAAM,IAAI,YAAY,OAAO,KAAK;AAClC,gBAAI,eAAe,QAAQ;AACzB,uBAAS;AAAA,YACX,WAAW,YAAY,QAAQ,IAAI,UAAU,KAAK,GAAG;AACnD,kBAAI,UAAU,IAAI;AAAA,YACpB;AAAA,UACF,OAAO;AACL,gBAAI,UAAU,IAAI;AAAA,UACpB;AAAA,QACF,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AACA,UAAI,QAAQ;AACZ,UAAI,MAAQ,WAAW;AACrB,YAAI,QAAQ,KAAK,GAAG,QAAQ,aAAa,EAAE,IAAI;AAC7C,uBAAa;AACb,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,UAAI,YAAY,QAAQ,aAAa,OAAO;AAC1C,YAAI,aAAa,SAAS,IAAI,aAAa,UAAU,MAAM,IAAI;AAC7D,cAAI,MAAQ,aAAa,OAAO;AAC9B,gBAAI,kBAAkB,UAAU,UAAU;AAAA,UAC5C,OAAO;AACL,gBAAI,gBAAgB,UAAU;AAAA,UAChC;AAAA,QACF;AAAA,MACF,YAAY,CAAC,UAAU,QAAQ,KAAkB,UAAU,CAAC,WAAW;AACrE,mBAAW,aAAa,OAAO,KAAK;AACpC,YAAI,MAAQ,aAAa,OAAO;AAC9B,cAAI,eAAe,UAAU,YAAY,QAAQ;AAAA,QACnD,OAAO;AACL,cAAI,aAAa,YAAY,QAAQ;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,sBAAsB;AAC1B,IAAI,iBAAiB,WAAS,CAAC,QAAQ,CAAC,IAAI,MAAM,MAAM,mBAAmB;AAC3E,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB,IAAI,OAAO,uBAAuB,GAAG;AAG/D,IAAI,gBAAgB,CAAC,UAAU,UAAU,eAAe;AACtD,QAAM,MAAM,SAAS,MAAM,aAAa,MAA6B,SAAS,MAAM,OAAO,SAAS,MAAM,OAAO,SAAS;AAC1H,QAAM,gBAAgB,YAAY,SAAS,WAAW;AACtD,QAAM,gBAAgB,SAAS,WAAW;AAC1C,MAAI,MAAQ,WAAW;AACrB,eAAW,cAAc,gBAAgB,OAAO,KAAK,aAAa,CAAC,GAAG;AACpE,UAAI,EAAE,cAAc,gBAAgB;AAClC,oBAAY,KAAK,YAAY,cAAc,UAAU,GAAG,QAAQ,YAAY,SAAS,OAAO;AAAA,MAC9F;AAAA,IACF;AAAA,EACF;AACA,aAAW,cAAc,gBAAgB,OAAO,KAAK,aAAa,CAAC,GAAG;AACpE,gBAAY,KAAK,YAAY,cAAc,UAAU,GAAG,cAAc,UAAU,GAAG,YAAY,SAAS,OAAO;AAAA,EACjH;AACF;AACA,SAAS,gBAAgB,WAAW;AAClC,SAAO,UAAU,SAAS,KAAK;AAAA;AAAA,IAE/B,CAAC,GAAG,UAAU,OAAO,UAAQ,SAAS,KAAK,GAAG,KAAK;AAAA;AAAA;AAAA,IAEnD;AAAA;AACF;AAGA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,qBAAqB;AACzB,IAAI,8BAA8B;AAClC,IAAI,oBAAoB;AACxB,IAAI,YAAY;AAChB,IAAI,YAAY,CAAC,gBAAgB,gBAAgB,YAAY,cAAc;AACzE,MAAI;AACJ,QAAM,YAAY,eAAe,WAAW,UAAU;AACtD,MAAI,KAAK;AACT,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,MAAQ,kBAAkB,CAAC,oBAAoB;AACjD,wBAAoB;AACpB,QAAI,UAAU,UAAU,QAAQ;AAC9B,UAAI,SAAS;AACX,kBAAU,UAAU,IAAI,UAAU,IAAI;AAAA,MACxC;AACA,gBAAU,WAAW,UAAU;AAAA;AAAA;AAAA,QAG/B;AAAA;AAAA;AAAA;AAAA;AAAA,QAIA;AAAA;AAAA,IACF;AAAA,EACF;AACA,MAAI,MAAQ,SAAS,UAAU,OAAO;AACpC,oBAAgB,WAAW,UAAU,WAAW,OAAO,IAAI,UAAU,MAAM,WAAW,IAAI,UAAU,KAAK,WAAW,mTAAmT;AAAA,EACza;AACA,MAAI,MAAQ,YAAY,UAAU,WAAW,MAAM;AACjD,UAAM,UAAU,QAAQ,IAAI,eAAe,UAAU,MAAM;AAAA,EAC7D,WAAW,MAAQ,kBAAkB,UAAU,UAAU,GAAyB;AAChF,UAAM,UAAU,QAAQ,MAAQ,WAAW,MAAQ,oBAAoB,uBAAuB,SAAS,IAAI,IAAI,eAAe,EAAE;AAAA,EAClI,OAAO;AACL,QAAI,MAAQ,OAAO,CAAC,WAAW;AAC7B,kBAAY,UAAU,UAAU;AAAA,IAClC;AACA,UAAM,UAAU,QAAQ,MAAQ,MAAM,IAAI,gBAAgB,YAAY,SAAS,SAAS,CAAC,sBAAsB,MAAQ,kBAAkB,UAAU,UAAU,IAAyB,YAAY,UAAU,KAAK,IAAI,IAAI,cAAc,CAAC,sBAAsB,MAAQ,kBAAkB,UAAU,UAAU,IAAyB,YAAY,UAAU,KAAK;AAChW,QAAI,MAAQ,OAAO,aAAa,UAAU,UAAU,iBAAiB;AACnE,kBAAY;AAAA,IACd;AACA,QAAI,MAAQ,eAAe;AACzB,oBAAc,MAAM,WAAW,SAAS;AAAA,IAC1C;AACA,UAAM,WAAW,IAAI,YAAY;AACjC,UAAM,4BAA4B,CAAC,SAAS,cAAc,MAAM;AAChE,QAAI,CAAC,6BAA6B,MAAQ,UAAU,MAAM,OAAO,KAAK,IAAI,MAAM,MAAM,SAAS;AAC7F,UAAI,UAAU,IAAI,IAAI,MAAM,IAAI,OAAO;AAAA,IACzC;AACA,QAAI,MAAQ,QAAQ;AAClB,4BAAsB,KAAK,SAAS;AAAA,IACtC;AACA,QAAI,UAAU,YAAY;AACxB,WAAK,KAAK,GAAG,KAAK,UAAU,WAAW,QAAQ,EAAE,IAAI;AACnD,oBAAY,UAAU,gBAAgB,WAAW,IAAI,GAAG;AACxD,YAAI,WAAW;AACb,cAAI,YAAY,SAAS;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAQ,KAAK;AACf,UAAI,UAAU,UAAU,OAAO;AAC7B,oBAAY;AAAA,MACd,WAAW,IAAI,YAAY,iBAAiB;AAC1C,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,MAAI,MAAM,IAAI;AACd,MAAI,MAAQ,gBAAgB;AAC1B,QAAI,UAAU,WAAW,IAAyB,IAA0B;AAC1E,UAAI,MAAM,IAAI;AACd,UAAI,MAAM,IAAI;AACd,UAAI,MAAM,IAAI,UAAU,UAAU;AAClC,UAAI,MAAM,KAAK,KAAK,UAAU,YAAY,OAAO,SAAS,GAAG;AAC7D,iBAAW,kBAAkB,eAAe,cAAc,eAAe,WAAW,UAAU;AAC9F,UAAI,YAAY,SAAS,UAAU,UAAU,SAAS,eAAe,OAAO;AAC1E,YAAI,MAAQ,uBAAuB;AACjC,6BAAmB,eAAe,KAAK;AAAA,QACzC,OAAO;AACL,oCAA0B,eAAe,OAAO,KAAK;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,qBAAqB,eAAa;AACpC,MAAI,WAAW;AACf,QAAM,OAAO,UAAU,QAAQ,YAAY,YAAY,CAAC;AACxD,MAAI,QAAQ,MAAM;AAChB,UAAM,iBAAiB,MAAM,KAAK,KAAK,UAAU,EAAE,KAAK,SAAO,IAAI,MAAM,CAAC;AAC1E,UAAM,iBAAiB,MAAM,KAAK,UAAU,UAAU;AACtD,eAAW,aAAa,iBAAiB,eAAe,QAAQ,IAAI,gBAAgB;AAClF,UAAI,UAAU,MAAM,KAAK,MAAM;AAC7B,qBAAa,MAAM,WAAW,kBAAkB,OAAO,iBAAiB,IAAI;AAC5E,kBAAU,MAAM,IAAI;AACpB,4BAAoB;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACA,MAAI,WAAW,CAAC;AAClB;AACA,IAAI,4BAA4B,CAAC,WAAW,cAAc;AACxD,MAAI,WAAW;AACf,QAAM,oBAAoB,MAAM,KAAK,UAAU,UAAU;AACzD,MAAI,UAAU,MAAM,KAAK,MAAQ,uBAAuB;AACtD,QAAI,OAAO;AACX,WAAO,OAAO,KAAK,aAAa;AAC9B,UAAI,QAAQ,KAAK,MAAM,MAAM,UAAU,MAAM,KAAK,KAAK,MAAM,MAAM,aAAa;AAC9E,0BAAkB,KAAK,IAAI;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACA,WAAS,KAAK,kBAAkB,SAAS,GAAG,MAAM,GAAG,MAAM;AACzD,UAAM,YAAY,kBAAkB,EAAE;AACtC,QAAI,UAAU,MAAM,MAAM,eAAe,UAAU,MAAM,GAAG;AAC1D,mBAAa,oBAAoB,SAAS,GAAG,WAAW,cAAc,SAAS,CAAC;AAChF,gBAAU,MAAM,EAAE,OAAO;AACzB,gBAAU,MAAM,IAAI;AACpB,gBAAU,MAAM,IAAI;AACpB,0BAAoB;AAAA,IACtB;AACA,QAAI,WAAW;AACb,gCAA0B,WAAW,SAAS;AAAA,IAChD;AAAA,EACF;AACA,MAAI,WAAW,CAAC;AAClB;AACA,IAAI,YAAY,CAAC,WAAW,QAAQ,aAAa,QAAQ,UAAU,WAAW;AAC5E,MAAI,eAAe,MAAQ,kBAAkB,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,cAAc;AAClG,MAAI;AACJ,MAAI,MAAQ,aAAa,aAAa,cAAc,aAAa,YAAY,aAAa;AACxF,mBAAe,aAAa;AAAA,EAC9B;AACA,SAAO,YAAY,QAAQ,EAAE,UAAU;AACrC,QAAI,OAAO,QAAQ,GAAG;AACpB,kBAAY,UAAU,MAAM,aAAa,UAAU,SAAS;AAC5D,UAAI,WAAW;AACb,eAAO,QAAQ,EAAE,QAAQ;AACzB,qBAAa,cAAc,WAAW,MAAQ,iBAAiB,cAAc,MAAM,IAAI,MAAM;AAAA,MAC/F;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,CAAC,QAAQ,UAAU,WAAW;AAC/C,WAAS,QAAQ,UAAU,SAAS,QAAQ,EAAE,OAAO;AACnD,UAAM,QAAQ,OAAO,KAAK;AAC1B,QAAI,OAAO;AACT,YAAM,MAAM,MAAM;AAClB,uBAAiB,KAAK;AACtB,UAAI,KAAK;AACP,YAAI,MAAQ,gBAAgB;AAC1B,wCAA8B;AAC9B,cAAI,IAAI,MAAM,GAAG;AACf,gBAAI,MAAM,EAAE,OAAO;AAAA,UACrB,OAAO;AACL,sCAA0B,KAAK,IAAI;AAAA,UACrC;AAAA,QACF;AACA,YAAI,OAAO;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,iBAAiB,CAAC,WAAW,OAAO,WAAW,OAAO,kBAAkB,UAAU;AACpF,MAAI,cAAc;AAClB,MAAI,cAAc;AAClB,MAAI,WAAW;AACf,MAAI,KAAK;AACT,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,gBAAgB,MAAM,CAAC;AAC3B,MAAI,cAAc,MAAM,SAAS;AACjC,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,gBAAgB,MAAM,CAAC;AAC3B,MAAI,cAAc,MAAM,SAAS;AACjC,MAAI;AACJ,MAAI;AACJ,SAAO,eAAe,aAAa,eAAe,WAAW;AAC3D,QAAI,iBAAiB,MAAM;AACzB,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,WAAW,eAAe,MAAM;AAC9B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,iBAAiB,MAAM;AAChC,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,WAAW,eAAe,MAAM;AAC9B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,YAAY,eAAe,eAAe,eAAe,GAAG;AACrE,YAAM,eAAe,eAAe,eAAe;AACnD,sBAAgB,MAAM,EAAE,WAAW;AACnC,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,WAAW,YAAY,aAAa,aAAa,eAAe,GAAG;AACjE,YAAM,aAAa,aAAa,eAAe;AAC/C,oBAAc,MAAM,EAAE,SAAS;AAC/B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,YAAY,eAAe,aAAa,eAAe,GAAG;AACnE,UAAI,MAAQ,mBAAmB,cAAc,UAAU,UAAU,YAAY,UAAU,SAAS;AAC9F,kCAA0B,cAAc,MAAM,YAAY,KAAK;AAAA,MACjE;AACA,YAAM,eAAe,aAAa,eAAe;AACjD,mBAAa,WAAW,cAAc,OAAO,YAAY,MAAM,WAAW;AAC1E,sBAAgB,MAAM,EAAE,WAAW;AACnC,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,YAAY,aAAa,eAAe,eAAe,GAAG;AACnE,UAAI,MAAQ,mBAAmB,cAAc,UAAU,UAAU,YAAY,UAAU,SAAS;AAC9F,kCAA0B,YAAY,MAAM,YAAY,KAAK;AAAA,MAC/D;AACA,YAAM,aAAa,eAAe,eAAe;AACjD,mBAAa,WAAW,YAAY,OAAO,cAAc,KAAK;AAC9D,oBAAc,MAAM,EAAE,SAAS;AAC/B,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,OAAO;AACL,iBAAW;AACX,UAAI,MAAQ,SAAS;AACnB,aAAK,KAAK,aAAa,MAAM,WAAW,EAAE,IAAI;AAC5C,cAAI,MAAM,EAAE,KAAK,MAAM,EAAE,EAAE,UAAU,QAAQ,MAAM,EAAE,EAAE,UAAU,cAAc,OAAO;AACpF,uBAAW;AACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAQ,WAAW,YAAY,GAAG;AACpC,oBAAY,MAAM,QAAQ;AAC1B,YAAI,UAAU,UAAU,cAAc,OAAO;AAC3C,iBAAO,UAAU,SAAS,MAAM,WAAW,GAAG,WAAW,UAAU,SAAS;AAAA,QAC9E,OAAO;AACL,gBAAM,WAAW,eAAe,eAAe;AAC/C,gBAAM,QAAQ,IAAI;AAClB,iBAAO,UAAU;AAAA,QACnB;AACA,wBAAgB,MAAM,EAAE,WAAW;AAAA,MACrC,OAAO;AACL,eAAO,UAAU,SAAS,MAAM,WAAW,GAAG,WAAW,aAAa,SAAS;AAC/E,wBAAgB,MAAM,EAAE,WAAW;AAAA,MACrC;AACA,UAAI,MAAM;AACR,YAAI,MAAQ,gBAAgB;AAC1B,uBAAa,oBAAoB,cAAc,KAAK,GAAG,MAAM,cAAc,cAAc,KAAK,CAAC;AAAA,QACjG,OAAO;AACL,uBAAa,cAAc,MAAM,YAAY,MAAM,cAAc,KAAK;AAAA,QACxE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,cAAc,WAAW;AAC3B,cAAU,WAAW,MAAM,YAAY,CAAC,KAAK,OAAO,OAAO,MAAM,YAAY,CAAC,EAAE,OAAO,WAAW,OAAO,aAAa,SAAS;AAAA,EACjI,WAAW,MAAQ,aAAa,cAAc,WAAW;AACvD,iBAAa,OAAO,aAAa,SAAS;AAAA,EAC5C;AACF;AACA,IAAI,cAAc,CAAC,WAAW,YAAY,kBAAkB,UAAU;AACpE,MAAI,UAAU,UAAU,WAAW,OAAO;AACxC,QAAI,MAAQ,kBAAkB,UAAU,UAAU,QAAQ;AACxD;AAAA;AAAA;AAAA,QAGA,cAAc,aAAa;AAAA;AAAA,QAG3B,UAAU,MAAM,aAAa;AAAA,QAAG;AAC9B,eAAO;AAAA,MACT;AACA,aAAO,UAAU,WAAW,WAAW;AAAA,IACzC;AACA,QAAI,MAAQ,WAAW,CAAC,iBAAiB;AACvC,aAAO,UAAU,UAAU,WAAW;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI,gBAAgB,UAAQ;AAC1B,SAAO,QAAQ,KAAK,MAAM,KAAK;AACjC;AACA,IAAI,sBAAsB,WAAS,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,MAAM;AACvE,IAAI,QAAQ,CAAC,UAAU,WAAW,kBAAkB,UAAU;AAC5D,QAAM,MAAM,UAAU,QAAQ,SAAS;AACvC,QAAM,cAAc,SAAS;AAC7B,QAAM,cAAc,UAAU;AAC9B,QAAM,MAAM,UAAU;AACtB,QAAM,OAAO,UAAU;AACvB,MAAI;AACJ,MAAI,CAAC,MAAQ,YAAY,SAAS,MAAM;AACtC,QAAI,MAAQ,KAAK;AACf,kBAAY,QAAQ,QAAQ,OAAO,QAAQ,kBAAkB,QAAQ;AAAA,IACvE;AACA,QAAI,MAAQ,iBAAiB,MAAQ,SAAS;AAC5C,UAAI,MAAQ,QAAQ,QAAQ,UAAU,CAAC,oBAAoB;AACzD,YAAI,MAAQ,yBAAyB,SAAS,WAAW,UAAU,QAAQ;AACzE,oBAAU,MAAM,MAAM,IAAI,UAAU,UAAU;AAC9C,6BAAmB,UAAU,MAAM,aAAa;AAAA,QAClD;AAAA,MACF,OAAO;AACL,sBAAc,UAAU,WAAW,SAAS;AAAA,MAC9C;AAAA,IACF;AACA,QAAI,MAAQ,aAAa,gBAAgB,QAAQ,gBAAgB,MAAM;AACrE,qBAAe,KAAK,aAAa,WAAW,aAAa,eAAe;AAAA,IAC1E,WAAW,gBAAgB,MAAM;AAC/B,UAAI,MAAQ,aAAa,MAAQ,YAAY,SAAS,WAAW,MAAM;AACrE,YAAI,cAAc;AAAA,MACpB;AACA,gBAAU,KAAK,MAAM,WAAW,aAAa,GAAG,YAAY,SAAS,CAAC;AAAA,IACxE;AAAA;AAAA,MAEA,CAAC,mBAAmB,MAAQ,aAAa,gBAAgB;AAAA,MAAM;AAC7D,mBAAa,aAAa,GAAG,YAAY,SAAS,CAAC;AAAA,IACrD;AACA,QAAI,MAAQ,OAAO,aAAa,QAAQ,OAAO;AAC7C,kBAAY;AAAA,IACd;AAAA,EACF,WAAW,MAAQ,YAAY,MAAQ,mBAAmB,gBAAgB,IAAI,MAAM,IAAI;AACtF,kBAAc,WAAW,cAAc;AAAA,EACzC,WAAW,MAAQ,YAAY,SAAS,WAAW,MAAM;AACvD,QAAI,OAAO;AAAA,EACb;AACF;AACA,IAAI,+BAA+B,SAAO;AACxC,QAAM,aAAa,IAAI;AACvB,aAAW,aAAa,YAAY;AAClC,QAAI,UAAU,aAAa,GAAqB;AAC9C,UAAI,UAAU,MAAM,GAAG;AACrB,cAAM,WAAW,UAAU,MAAM;AACjC,kBAAU,SAAS;AACnB,mBAAW,eAAe,YAAY;AACpC,cAAI,gBAAgB,WAAW;AAC7B,gBAAI,YAAY,MAAM,MAAM,UAAU,MAAM,KAAK,aAAa,IAAI;AAChE,kBAAI,YAAY,aAAa,MAAwB,aAAa,YAAY,aAAa,MAAM,KAAK,aAAa,YAAY,MAAM,MAAM,YAAY,aAAa,KAAoB,aAAa,YAAY,MAAM,GAAG;AACxN,0BAAU,SAAS;AACnB;AAAA,cACF;AAAA,YACF,OAAO;AACL,kBAAI,YAAY,aAAa,KAAuB,YAAY,aAAa,KAAoB,YAAY,YAAY,KAAK,MAAM,IAAI;AACtI,0BAAU,SAAS;AACnB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,mCAA6B,SAAS;AAAA,IACxC;AAAA,EACF;AACF;AACA,IAAI,gBAAgB,CAAC;AACrB,IAAI,+BAA+B,SAAO;AACxC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,aAAW,aAAa,IAAI,YAAY;AACtC,QAAI,UAAU,MAAM,MAAM,OAAO,UAAU,MAAM,MAAM,KAAK,YAAY;AACtE,yBAAmB,KAAK,WAAW;AACnC,YAAM,WAAW,UAAU,MAAM;AACjC,WAAK,IAAI,iBAAiB,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,eAAO,iBAAiB,CAAC;AACzB,YAAI,CAAC,KAAK,MAAM,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,UAAU,MAAM,MAAM,CAAC,MAAQ,yBAAyB,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,UAAU,MAAM,IAAI;AACnK,cAAI,oBAAoB,MAAM,QAAQ,GAAG;AACvC,gBAAI,mBAAmB,cAAc,KAAK,OAAK,EAAE,qBAAqB,IAAI;AAC1E,0CAA8B;AAC9B,iBAAK,MAAM,IAAI,KAAK,MAAM,KAAK;AAC/B,gBAAI,kBAAkB;AACpB,+BAAiB,iBAAiB,MAAM,IAAI,UAAU,MAAM;AAC5D,+BAAiB,gBAAgB;AAAA,YACnC,OAAO;AACL,mBAAK,MAAM,IAAI,UAAU,MAAM;AAC/B,4BAAc,KAAK;AAAA,gBACjB,eAAe;AAAA,gBACf,kBAAkB;AAAA,cACpB,CAAC;AAAA,YACH;AACA,gBAAI,KAAK,MAAM,GAAG;AAChB,4BAAc,IAAI,kBAAgB;AAChC,oBAAI,oBAAoB,aAAa,kBAAkB,KAAK,MAAM,CAAC,GAAG;AACpE,qCAAmB,cAAc,KAAK,OAAK,EAAE,qBAAqB,IAAI;AACtE,sBAAI,oBAAoB,CAAC,aAAa,eAAe;AACnD,iCAAa,gBAAgB,iBAAiB;AAAA,kBAChD;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,WAAW,CAAC,cAAc,KAAK,OAAK,EAAE,qBAAqB,IAAI,GAAG;AAChE,0BAAc,KAAK;AAAA,cACjB,kBAAkB;AAAA,YACpB,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,UAAU,aAAa,GAAqB;AAC9C,mCAA6B,SAAS;AAAA,IACxC;AAAA,EACF;AACF;AACA,IAAI,sBAAsB,CAAC,gBAAgB,aAAa;AACtD,MAAI,eAAe,aAAa,GAAqB;AACnD,QAAI,eAAe,aAAa,MAAM,MAAM,QAAQ,aAAa,IAAI;AACnE,aAAO;AAAA,IACT;AACA,QAAI,eAAe,aAAa,MAAM,MAAM,UAAU;AACpD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,MAAM,MAAM,UAAU;AACvC,WAAO;AAAA,EACT;AACA,SAAO,aAAa;AACtB;AACA,IAAI,mBAAmB,WAAS;AAC9B,MAAI,MAAQ,SAAS;AACnB,UAAM,WAAW,MAAM,QAAQ,OAAO,MAAM,QAAQ,IAAI,IAAI;AAC5D,UAAM,cAAc,MAAM,WAAW,IAAI,gBAAgB;AAAA,EAC3D;AACF;AACA,IAAI,eAAe,CAAC,QAAQ,SAAS,cAAc;AACjD,QAAM,WAAW,UAAU,OAAO,SAAS,OAAO,aAAa,SAAS,SAAS;AACjF,MAAI,MAAQ,QAAQ;AAClB,0BAAsB,SAAS,MAAM;AAAA,EACvC;AACA,SAAO;AACT;AACA,IAAI,eAAe,aAAW;AAC5B,QAAM,WAAW,CAAC;AAClB,MAAI,SAAS;AACX,aAAS,KAAK,GAAI,QAAQ,OAAO,KAAK,CAAC,GAAI,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAG,GAAG,aAAa,QAAQ,aAAa,CAAC;AAAA,EACrH;AACA,SAAO;AACT;AACA,IAAI,wBAAwB,CAAC,SAAS,QAAQ,oBAAoB,UAAU;AAC1E,MAAI;AACJ,MAAI,WAAW,UAAU,QAAQ,aAAa,GAAqB;AACjE,UAAM,WAAW,IAAI,IAAI,aAAa,MAAM,EAAE,OAAO,OAAO,CAAC;AAC7D,QAAI,SAAS,MAAM;AACjB,OAAC,KAAK,QAAQ,cAAc,OAAO,SAAS,GAAG,IAAI,GAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,QAAQ,CAAE;AACxF,UAAI,QAAQ,MAAM,KAAK,mBAAmB;AACxC,mBAAW,aAAa,MAAM,KAAK,QAAQ,UAAU,GAAG;AACtD,gCAAsB,WAAW,SAAS,IAAI;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,aAAa,CAAC,SAAS,iBAAiB,gBAAgB,UAAU;AACpE,MAAI,IAAI,IAAI,IAAI,IAAI;AACpB,QAAM,UAAU,QAAQ;AACxB,QAAM,UAAU,QAAQ;AACxB,QAAM,WAAW,QAAQ,WAAW,SAAS,MAAM,IAAI;AACvD,QAAM,YAAY,OAAO,eAAe,IAAI,kBAAkB,EAAE,MAAM,MAAM,eAAe;AAC3F,gBAAc,QAAQ;AACtB,MAAI,MAAQ,SAAS,MAAM,QAAQ,eAAe,KAAK,gBAAgB,KAAK,MAAM,GAAG;AACnF,UAAM,IAAI,MAAM;AAAA,uCACmB,YAAY,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAU7D;AAAA,EACD;AACA,MAAI,MAAQ,WAAW,QAAQ,kBAAkB;AAC/C,cAAU,UAAU,UAAU,WAAW,CAAC;AAC1C,YAAQ,iBAAiB,IAAI,CAAC,CAAC,UAAU,SAAS,MAAM,UAAU,QAAQ,SAAS,IAAI,QAAQ,QAAQ,CAAC;AAAA,EAC1G;AACA,MAAI,iBAAiB,UAAU,SAAS;AACtC,eAAW,OAAO,OAAO,KAAK,UAAU,OAAO,GAAG;AAChD,UAAI,QAAQ,aAAa,GAAG,KAAK,CAAC,CAAC,OAAO,OAAO,SAAS,OAAO,EAAE,SAAS,GAAG,GAAG;AAChF,kBAAU,QAAQ,GAAG,IAAI,QAAQ,GAAG;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACA,YAAU,QAAQ;AAClB,YAAU,WAAW;AACrB,UAAQ,UAAU;AAClB,YAAU,QAAQ,SAAS,QAAQ,MAAQ,YAAY,QAAQ,cAAc,UAAU;AACvF,MAAI,MAAQ,UAAU,MAAQ,WAAW;AACvC,cAAU,QAAQ,MAAM;AAAA,EAC1B;AACA,uBAAqB,mBAAmB,QAAQ,UAAU,OAAoC;AAC9F,MAAI,MAAQ,gBAAgB;AAC1B,iBAAa,QAAQ,MAAM;AAC3B,kCAA8B;AAAA,EAChC;AACA,QAAM,UAAU,WAAW,aAAa;AACxC,MAAI,MAAQ,gBAAgB;AAC1B,QAAI,WAAW;AACf,QAAI,mBAAmB;AACrB,mCAA6B,UAAU,KAAK;AAC5C,iBAAW,gBAAgB,eAAe;AACxC,cAAM,iBAAiB,aAAa;AACpC,YAAI,CAAC,eAAe,MAAM,GAAG;AAC3B,gBAAM,kBAAkB,MAAQ,WAAW,MAAQ,oBAAoB,0BAA0B,cAAc,IAAI,IAAI,eAAe,EAAE;AACxI,0BAAgB,MAAM,IAAI;AAC1B,uBAAa,eAAe,YAAY,eAAe,MAAM,IAAI,iBAAiB,cAAc;AAAA,QAClG;AAAA,MACF;AACA,iBAAW,gBAAgB,eAAe;AACxC,cAAM,iBAAiB,aAAa;AACpC,cAAM,cAAc,aAAa;AACjC,YAAI,aAAa;AACf,gBAAM,gBAAgB,YAAY;AAClC,cAAI,mBAAmB,YAAY;AACnC,cAAI,CAAC,MAAQ,yBAAyB,oBAAoB,iBAAiB,aAAa,GAAqB;AAC3G,gBAAI,mBAAmB,KAAK,eAAe,MAAM,MAAM,OAAO,SAAS,GAAG;AAC1E,mBAAO,iBAAiB;AACtB,kBAAI,WAAW,KAAK,gBAAgB,MAAM,MAAM,OAAO,KAAK;AAC5D,kBAAI,WAAW,QAAQ,MAAM,MAAM,eAAe,MAAM,KAAK,kBAAkB,QAAQ,YAAY;AACjG,0BAAU,QAAQ;AAClB,uBAAO,YAAY,mBAAmB,WAAW,OAAO,SAAS,QAAQ,MAAM,IAAI;AACjF,4BAAU,WAAW,OAAO,SAAS,QAAQ;AAAA,gBAC/C;AACA,oBAAI,CAAC,WAAW,CAAC,QAAQ,MAAM,GAAG;AAChC,qCAAmB;AACnB;AAAA,gBACF;AAAA,cACF;AACA,gCAAkB,gBAAgB;AAAA,YACpC;AAAA,UACF;AACA,cAAI,CAAC,oBAAoB,kBAAkB,eAAe,cAAc,eAAe,gBAAgB,kBAAkB;AACvH,gBAAI,mBAAmB,kBAAkB;AACvC,kBAAI,CAAC,MAAQ,yBAAyB,CAAC,eAAe,MAAM,KAAK,eAAe,MAAM,GAAG;AACvF,+BAAe,MAAM,IAAI,eAAe,MAAM,EAAE,WAAW;AAAA,cAC7D;AACA,2BAAa,eAAe,gBAAgB,gBAAgB;AAC5D,kBAAI,eAAe,aAAa,GAAqB;AACnD,+BAAe,UAAU,KAAK,eAAe,MAAM,MAAM,OAAO,KAAK;AAAA,cACvE;AAAA,YACF;AAAA,UACF;AACA,4BAAkB,OAAO,YAAY,MAAM,MAAM,cAAc,YAAY,MAAM,EAAE,cAAc;AAAA,QACnG,OAAO;AACL,cAAI,eAAe,aAAa,GAAqB;AACnD,gBAAI,eAAe;AACjB,6BAAe,MAAM,KAAK,KAAK,eAAe,WAAW,OAAO,KAAK;AAAA,YACvE;AACA,2BAAe,SAAS;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,6BAA6B;AAC/B,mCAA6B,UAAU,KAAK;AAAA,IAC9C;AACA,QAAI,WAAW,CAAC;AAChB,kBAAc,SAAS;AAAA,EACzB;AACA,MAAI,MAAQ,iCAAiC,QAAQ,UAAU,GAAgC;AAC7F,eAAW,aAAa,UAAU,MAAM,YAAY;AAClD,UAAI,UAAU,MAAM,MAAM,eAAe,CAAC,UAAU,MAAM,GAAG;AAC3D,YAAI,iBAAiB,UAAU,MAAM,KAAK,MAAM;AAC9C,oBAAU,MAAM,KAAK,KAAK,UAAU,WAAW,OAAO,KAAK;AAAA,QAC7D;AACA,kBAAU,SAAS;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,eAAa;AACf;AACA,IAAI,yBAAyB,eAAa,IAAI,cAAc,QAAQ,UAAU,SAAS,YAAY,UAAU,SAAS,MAAM,EAAE,WAAW,YAAY,YAAY,CAAC,GAAG;AACrK,IAAI,4BAA4B,oBAAkB,IAAI,cAAc,uBAAuB,eAAe,YAAY,IAAI,eAAe,SAAS,WAAW,eAAe,MAAM,CAAC,MAAM,IAAI,eAAe,WAAW,IAAI;AAG3N,IAAI,mBAAmB,CAAC,SAAS,sBAAsB;AACrD,MAAI,MAAQ,gBAAgB,qBAAqB,CAAC,QAAQ,qBAAqB,kBAAkB,KAAK,GAAG;AACvG,sBAAkB,KAAK,EAAE,KAAK,IAAI,QAAQ,OAAK,QAAQ,oBAAoB,CAAC,CAAC;AAAA,EAC/E;AACF;AACA,IAAI,iBAAiB,CAAC,SAAS,kBAAkB;AAC/C,MAAI,MAAQ,aAAa,MAAQ,WAAW;AAC1C,YAAQ,WAAW;AAAA,EACrB;AACA,MAAI,MAAQ,gBAAgB,QAAQ,UAAU,GAA8B;AAC1E,YAAQ,WAAW;AACnB;AAAA,EACF;AACA,mBAAiB,SAAS,QAAQ,mBAAmB;AACrD,QAAM,WAAW,MAAM,cAAc,SAAS,aAAa;AAC3D,SAAO,MAAQ,YAAY,UAAU,QAAQ,IAAI,SAAS;AAC5D;AACA,IAAI,gBAAgB,CAAC,SAAS,kBAAkB;AAC9C,QAAM,MAAM,QAAQ;AACpB,QAAM,cAAc,WAAW,kBAAkB,QAAQ,UAAU,SAAS;AAC5E,QAAM,WAAW,MAAQ,WAAW,QAAQ,iBAAiB;AAC7D,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,2BAA2B,IAAI,QAAQ,YAAY,CAAC,yNAAyN;AAAA,EAC/R;AACA,MAAI;AACJ,MAAI,eAAe;AACjB,QAAI,MAAQ,YAAY,MAAQ,cAAc;AAC5C,cAAQ,WAAW;AACnB,UAAI,QAAQ,mBAAmB;AAC7B,gBAAQ,kBAAkB,IAAI,CAAC,CAAC,YAAY,KAAK,MAAM,SAAS,UAAU,YAAY,KAAK,CAAC;AAC5F,gBAAQ,oBAAoB;AAAA,MAC9B;AAAA,IACF;AACA,uBAAmB,KAAK,mBAAmB;AAC3C,QAAI,MAAQ,aAAa;AACvB,qBAAe,SAAS,UAAU,mBAAmB;AAAA,IACvD;AAAA,EACF,OAAO;AACL,uBAAmB,KAAK,qBAAqB;AAC7C,QAAI,MAAQ,eAAe;AACzB,qBAAe,SAAS,UAAU,qBAAqB;AAAA,IACzD;AAAA,EACF;AACA,qBAAmB,KAAK,qBAAqB;AAC7C,MAAI,MAAQ,eAAe;AACzB,mBAAe,QAAQ,cAAc,MAAM,SAAS,UAAU,qBAAqB,CAAC;AAAA,EACtF;AACA,cAAY;AACZ,SAAO,QAAQ,cAAc,MAAM,gBAAgB,SAAS,UAAU,aAAa,CAAC;AACtF;AACA,IAAI,UAAU,CAAC,cAAc,OAAO,WAAW,YAAY,IAAI,aAAa,KAAK,EAAE,EAAE,MAAM,UAAQ;AACjG,UAAQ,MAAM,IAAI;AAClB,KAAG;AACL,CAAC,IAAI,GAAG;AACR,IAAI,aAAa,kBAAgB,wBAAwB,WAAW,gBAAgB,aAAa,QAAQ,OAAO,aAAa,SAAS;AACtI,IAAI,kBAAkB,CAAO,SAAS,UAAU,kBAAkB;AAChE,MAAI;AACJ,QAAM,MAAM,QAAQ;AACpB,QAAM,YAAY,WAAW,UAAU,QAAQ,UAAU,SAAS;AAClE,QAAM,KAAK,IAAI,MAAM;AACrB,MAAI,MAAQ,SAAS,eAAe;AAClC,iBAAa,OAAO;AAAA,EACtB;AACA,QAAM,YAAY,WAAW,UAAU,QAAQ,UAAU,SAAS;AAClE,MAAI,MAAQ,OAAO;AACjB,YAAQ,WAAW;AAAA,EACrB;AACA,MAAI,MAAQ,mBAAmB;AAC7B,UAAM,WAAW,SAAS,UAAU,KAAK,aAAa;AAAA,EACxD,OAAO;AACL,eAAW,SAAS,UAAU,KAAK,aAAa;AAAA,EAClD;AACA,MAAI,MAAQ,OAAO;AACjB,YAAQ,gBAAgB,QAAQ,kBAAkB,SAAS,IAAI,QAAQ,gBAAgB;AACvF,YAAQ,WAAW,CAAC;AAAA,EACtB;AACA,MAAI,MAAQ,mBAAmB;AAC7B,QAAI;AACF,0BAAoB,GAAG;AACvB,UAAI,eAAe;AACjB,YAAI,QAAQ,UAAU,UAAU,GAAgC;AAC9D,cAAI,MAAM,IAAI;AAAA,QAChB,WAAW,QAAQ,UAAU,UAAU,GAAgC;AACrE,cAAI,MAAM,IAAI;AAAA,QAChB;AAAA,MACF;AAAA,IACF,SAAS,GAAG;AACV,mBAAa,GAAG,GAAG;AAAA,IACrB;AAAA,EACF;AACA,MAAI,MAAQ,gBAAgB,IAAI;AAC9B,OAAG,IAAI,QAAM,GAAG,CAAC;AACjB,QAAI,MAAM,IAAI;AAAA,EAChB;AACA,YAAU;AACV,YAAU;AACV,MAAI,MAAQ,cAAc;AACxB,UAAM,oBAAoB,KAAK,IAAI,KAAK,MAAM,OAAO,KAAK,CAAC;AAC3D,UAAM,aAAa,MAAM,oBAAoB,OAAO;AACpD,QAAI,iBAAiB,WAAW,GAAG;AACjC,iBAAW;AAAA,IACb,OAAO;AACL,cAAQ,IAAI,gBAAgB,EAAE,KAAK,UAAU;AAC7C,cAAQ,WAAW;AACnB,uBAAiB,SAAS;AAAA,IAC5B;AAAA,EACF,OAAO;AACL,wBAAoB,OAAO;AAAA,EAC7B;AACF;AACA,IAAI,eAAe;AACnB,IAAI,aAAa,CAAC,SAAS,UAAU,KAAK,kBAAkB;AAC1D,QAAM,cAAc,MAAQ,cAAc,OAAO;AACjD,QAAM,WAAW,MAAQ,WAAW,OAAO;AAC3C,QAAM,YAAY,MAAQ,YAAY,OAAO;AAC7C,QAAM,YAAY,MAAQ,YAAY,OAAO;AAC7C,MAAI;AACF,mBAAe;AACf,eAAW,cAAc,SAAS,OAAO,IAAI,SAAS,UAAU,SAAS,OAAO;AAChF,QAAI,aAAa,WAAW;AAC1B,cAAQ,WAAW,CAAC;AAAA,IACtB;AACA,QAAI,aAAa,UAAU;AACzB,cAAQ,WAAW;AAAA,IACrB;AACA,QAAI,MAAQ,eAAe,MAAQ,SAAS;AAC1C,UAAI,MAAQ,cAAc,MAAQ,SAAS;AACzC,YAAI,MAAQ,mBAAmB;AAC7B,iBAAO,QAAQ,QAAQ,QAAQ,EAAE,KAAK,WAAS,WAAW,SAAS,OAAO,aAAa,CAAC;AAAA,QAC1F,OAAO;AACL,qBAAW,SAAS,UAAU,aAAa;AAAA,QAC7C;AAAA,MACF,OAAO;AACL,cAAM,aAAa,IAAI;AACvB,YAAI,QAAQ,UAAU,UAAU,GAAgC;AAC9D,qBAAW,cAAc;AAAA,QAC3B,OAAO;AACL,cAAI,cAAc;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AACV,iBAAa,GAAG,QAAQ,aAAa;AAAA,EACvC;AACA,iBAAe;AACf,SAAO;AACT;AAEA,IAAI,sBAAsB,aAAW;AACnC,QAAM,UAAU,QAAQ,UAAU;AAClC,QAAM,MAAM,QAAQ;AACpB,QAAM,gBAAgB,WAAW,cAAc,OAAO;AACtD,QAAM,WAAW,MAAQ,WAAW,QAAQ,iBAAiB;AAC7D,QAAM,oBAAoB,QAAQ;AAClC,MAAI,MAAQ,cAAc;AACxB,QAAI,MAAQ,OAAO;AACjB,cAAQ,WAAW;AAAA,IACrB;AACA,aAAS,UAAU,oBAAoB;AACvC,QAAI,MAAQ,OAAO;AACjB,cAAQ,WAAW,CAAC;AAAA,IACtB;AAAA,EACF;AACA,qBAAmB,KAAK,oBAAoB;AAC5C,MAAI,EAAE,QAAQ,UAAU,KAA8B;AACpD,YAAQ,WAAW;AACnB,QAAI,MAAQ,gBAAgB,MAAQ,gBAAgB;AAClD,sBAAgB,GAAG;AAAA,IACrB;AACA,QAAI,MAAQ,YAAY;AACtB,UAAI,MAAQ,OAAO;AACjB,gBAAQ,WAAW;AAAA,MACrB;AACA,eAAS,UAAU,kBAAkB;AACrC,UAAI,MAAQ,OAAO;AACjB,gBAAQ,WAAW,CAAC;AAAA,MACtB;AAAA,IACF;AACA,uBAAmB,KAAK,kBAAkB;AAC1C,kBAAc;AACd,QAAI,MAAQ,cAAc;AACxB,cAAQ,iBAAiB,GAAG;AAC5B,UAAI,CAAC,mBAAmB;AACtB,mBAAW,OAAO;AAAA,MACpB;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,MAAQ,cAAc;AACxB,UAAI,MAAQ,OAAO;AACjB,gBAAQ,WAAW;AAAA,MACrB;AACA,eAAS,UAAU,oBAAoB;AACvC,UAAI,MAAQ,OAAO;AACjB,gBAAQ,WAAW,CAAC;AAAA,MACtB;AAAA,IACF;AACA,uBAAmB,KAAK,oBAAoB;AAC5C,kBAAc;AAAA,EAChB;AACA,MAAI,MAAQ,UAAU,MAAQ,UAAU;AACtC,YAAQ,oBAAoB,GAAG;AAAA,EACjC;AACA,MAAI,MAAQ,cAAc;AACxB,QAAI,QAAQ,mBAAmB;AAC7B,cAAQ,kBAAkB;AAC1B,cAAQ,oBAAoB;AAAA,IAC9B;AACA,QAAI,QAAQ,UAAU,KAAyB;AAC7C,eAAS,MAAM,eAAe,SAAS,KAAK,CAAC;AAAA,IAC/C;AACA,YAAQ,WAAW,EAAE,IAA+B;AAAA,EACtD;AACF;AACA,IAAI,cAAc,SAAO;AACvB,MAAI,MAAQ,cAAc,MAAM,aAAa,MAAM,YAAY;AAC7D,UAAM,UAAU,WAAW,GAAG;AAC9B,UAAM,cAAc,QAAQ,cAAc;AAC1C,QAAI,gBAAgB,QAAQ,WAAW,IAAsB,SAAiC,GAAqB;AACjH,qBAAe,SAAS,KAAK;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI,aAAa,SAAO;AACtB,MAAI,MAAQ,gBAAgB;AAC1B,oBAAgB,IAAI,eAAe;AAAA,EACrC;AACA,MAAI,MAAQ,YAAY;AACtB,QAAI,WAAW;AAAA,EACjB;AACA,WAAS,MAAM,UAAU,KAAK,WAAW;AAAA,IACvC,QAAQ;AAAA,MACN,WAAW;AAAA,IACb;AAAA,EACF,CAAC,CAAC;AACF,MAAI,MAAQ,WAAW,YAAY,SAAS;AAC1C,gBAAY,QAAQ,aAAa,SAAS,qBAAqB,GAAG,KAAK,cAAc;AAAA,EACvF;AACF;AACA,IAAI,WAAW,CAAC,UAAU,QAAQ,QAAQ;AACxC,MAAI,YAAY,SAAS,MAAM,GAAG;AAChC,QAAI;AACF,aAAO,SAAS,MAAM,EAAE,GAAG;AAAA,IAC7B,SAAS,GAAG;AACV,mBAAa,CAAC;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,qBAAqB,CAAC,KAAK,kBAAkB;AAC/C,MAAI,MAAQ,oBAAoB;AAC9B,cAAU,KAAK,aAAa,eAAe;AAAA,MACzC,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,QACN,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAI,kBAAkB,SAAO;AAC3B,MAAI,IAAI;AACR,SAAO,MAAQ,gBAAgB,IAAI,UAAU,KAAK,KAAK,MAAQ,yBAAyB,OAAO,KAAK,UAAU,IAAI,MAAQ,oBAAoB,IAAI,cAAc,KAAK,MAAQ,yBAAyB,OAAO,KAAK,YAAY,EAAE,IAAI;AACtO;AACA,IAAI,sBAAsB,SAAO;AAC/B,QAAM,WAAW,IAAI;AACrB,MAAI,YAAY,MAAM;AACpB,aAAS,KAAK,GAAG,KAAK,SAAS,QAAQ,KAAK,IAAI,MAAM;AACpD,YAAM,WAAW,SAAS,EAAE;AAC5B,UAAI,OAAO,SAAS,sBAAsB,YAAY;AACpD,iBAAS,kBAAkB;AAAA,MAC7B;AACA,0BAAoB,QAAQ;AAAA,IAC9B;AAAA,EACF;AACF;AAGA,IAAI,WAAW,CAAC,KAAK,aAAa,WAAW,GAAG,EAAE,iBAAiB,IAAI,QAAQ;AAC/E,IAAI,WAAW,CAAC,KAAK,UAAU,QAAQ,YAAY;AACjD,QAAM,UAAU,WAAW,GAAG;AAC9B,MAAI,MAAQ,YAAY,CAAC,SAAS;AAChC,UAAM,IAAI,MAAM,mCAAmC,QAAQ,SAAS,+YAA+Y;AAAA,EACrd;AACA,QAAM,MAAM,MAAQ,WAAW,QAAQ,gBAAgB;AACvD,QAAM,SAAS,QAAQ,iBAAiB,IAAI,QAAQ;AACpD,QAAM,QAAQ,QAAQ;AACtB,QAAM,WAAW,MAAQ,WAAW,QAAQ,iBAAiB;AAC7D,WAAS,mBAAmB,QAAQ,QAAQ,UAAU,QAAQ,EAAE,CAAC,CAAC;AAClE,QAAM,aAAa,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,MAAM;AAC9D,QAAM,iBAAiB,WAAW,UAAU,CAAC;AAC7C,OAAK,CAAC,MAAQ,YAAY,EAAE,QAAQ,MAAmC,WAAW,WAAW,gBAAgB;AAC3G,YAAQ,iBAAiB,IAAI,UAAU,MAAM;AAC7C,QAAI,MAAQ,OAAO;AACjB,UAAI,QAAQ,UAAU,MAAwB;AAC5C,uBAAe,mBAAmB,QAAQ,2FAA2F,aAAa,KAAK,eAAe,QAAQ,eAAe,MAAM;AAAA,MACrM,WAAW,QAAQ,UAAU,MAAyB;AACpD,uBAAe,mBAAmB,QAAQ,gHAAgH,aAAa,KAAK,eAAe,QAAQ,eAAe,MAAM;AAAA,MAC1N;AAAA,IACF;AACA,QAAI,CAAC,MAAQ,YAAY,UAAU;AACjC,UAAI,MAAQ,iBAAiB,QAAQ,cAAc,QAAQ,KAAwB;AACjF,cAAM,eAAe,QAAQ,WAAW,QAAQ;AAChD,YAAI,cAAc;AAChB,uBAAa,IAAI,qBAAmB;AAClC,gBAAI;AACF,uBAAS,eAAe,EAAE,QAAQ,QAAQ,QAAQ;AAAA,YACpD,SAAS,GAAG;AACV,2BAAa,GAAG,GAAG;AAAA,YACrB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,UAAI,MAAQ,cAAc,SAAS,IAAsB,SAAiC,GAAqB;AAC7G,YAAI,MAAQ,mBAAmB,SAAS,uBAAuB;AAC7D,cAAI,SAAS,sBAAsB,QAAQ,QAAQ,QAAQ,MAAM,OAAO;AACtE;AAAA,UACF;AAAA,QACF;AACA,uBAAe,SAAS,KAAK;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,iBAAiB,CAAC,MAAM,SAAS,UAAU;AAC7C,MAAI,IAAI;AACR,QAAM,YAAY,KAAK;AACvB,MAAI,MAAQ,kBAAkB,QAAQ,UAAU,MAA2B,QAAQ,GAA8B;AAC/G,6CAAyC,QAAQ,YAAU,OAAO,eAAe,WAAW,QAAQ;AAAA,MAClG,SAAS,MAAM;AACb,cAAM,UAAU,WAAW,IAAI;AAC/B,cAAM,MAAM,MAAQ,WAAW,QAAQ,gBAAgB;AACvD,cAAM,WAAW,MAAQ,WAAW,QAAQ,iBAAiB;AAC7D,YAAI,CAAC,UAAU;AACb,kBAAQ,iBAAiB,KAAK,eAAa;AACzC,kBAAM,KAAK,UAAU,MAAM;AAC3B,mBAAO,OAAO,cAAc,GAAG,KAAK,WAAW,GAAG,IAAI;AAAA,UACxD,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,KAAK,SAAS,MAAM;AAC1B,iBAAO,OAAO,cAAc,GAAG,KAAK,UAAU,GAAG,IAAI;AAAA,QACvD;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACA,MAAI,MAAQ,UAAU,QAAQ,aAAa,MAAQ,kBAAkB,QAAQ,cAAc,KAAK,WAAW;AACzG,QAAI,MAAQ,iBAAiB,KAAK,YAAY,CAAC,QAAQ,YAAY;AACjE,cAAQ,aAAa,KAAK;AAAA,IAC5B;AACA,UAAM,UAAU,OAAO,SAAS,KAAK,QAAQ,cAAc,OAAO,KAAK,CAAC,CAAC;AACzE,YAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM;AAC3C,WAAK,MAAQ,QAAQ,MAAQ,WAAW,cAAc,OAAkB,CAAC,MAAQ,YAAY,QAAQ,MAAuB,cAAc,KAAiB;AACzJ,eAAO,eAAe,WAAW,YAAY;AAAA,UAC3C,MAAM;AACJ,mBAAO,SAAS,MAAM,UAAU;AAAA,UAClC;AAAA,UACA,IAAI,UAAU;AACZ,gBAAI,MAAQ,OAAO;AACjB,oBAAM,MAAM,WAAW,IAAI;AAC3B;AAAA;AAAA,iBAEC,QAAQ,OAAkC;AAAA,iBAE1C,OAAO,IAAI,UAAU,OAAoC;AAAA,iBAEzD,cAAc,QAAmB;AAAA,iBAEjC,cAAc,UAAwB;AAAA,gBAAG;AACxC,+BAAe,YAAY,UAAU,SAAS,QAAQ,SAAS;AAAA,wEACP;AAAA,cAC1D;AAAA,YACF;AACA,qBAAS,MAAM,YAAY,UAAU,OAAO;AAAA,UAC9C;AAAA,UACA,cAAc;AAAA,UACd,YAAY;AAAA,QACd,CAAC;AAAA,MACH,WAAW,MAAQ,YAAY,MAAQ,UAAU,QAAQ,KAAgC,cAAc,IAAiB;AACtH,eAAO,eAAe,WAAW,YAAY;AAAA,UAC3C,SAAS,MAAM;AACb,gBAAI;AACJ,kBAAM,MAAM,WAAW,IAAI;AAC3B,oBAAQ,MAAM,OAAO,OAAO,SAAS,IAAI,wBAAwB,OAAO,SAAS,IAAI,KAAK,MAAM;AAC9F,kBAAI;AACJ,sBAAQ,MAAM,IAAI,mBAAmB,OAAO,SAAS,IAAI,UAAU,EAAE,GAAG,IAAI;AAAA,YAC9E,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,QAAI,MAAQ,qBAAqB,CAAC,MAAQ,YAAY,QAAQ,IAA+B;AAC3F,YAAM,qBAAoC,oBAAI,IAAI;AAClD,gBAAU,2BAA2B,SAAU,UAAU,UAAU,UAAU;AAC3E,YAAI,IAAI,MAAM;AACZ,cAAI;AACJ,gBAAM,WAAW,mBAAmB,IAAI,QAAQ;AAChD,cAAI,KAAK,eAAe,QAAQ,GAAG;AACjC,uBAAW,KAAK,QAAQ;AACxB,mBAAO,KAAK,QAAQ;AAAA,UACtB,WAAW,UAAU,eAAe,QAAQ,KAAK,OAAO,KAAK,QAAQ,MAAM;AAAA,UAE3E,KAAK,QAAQ,KAAK,UAAU;AAC1B;AAAA,UACF,WAAW,YAAY,MAAM;AAC3B,kBAAM,UAAU,WAAW,IAAI;AAC/B,kBAAM,SAAS,WAAW,OAAO,SAAS,QAAQ;AAClD,gBAAI,UAAU,EAAE,SAAS,MAAmC,SAAS,OAA0B,aAAa,UAAU;AACpH,oBAAM,MAAM,MAAQ,WAAW,QAAQ,gBAAgB;AACvD,oBAAM,WAAW,MAAQ,WAAW,QAAQ,iBAAiB;AAC7D,oBAAM,SAAS,MAAM,QAAQ,eAAe,OAAO,SAAS,IAAI,QAAQ;AACxE,uBAAS,OAAO,SAAS,MAAM,QAAQ,kBAAgB;AACrD,oBAAI,SAAS,YAAY,KAAK,MAAM;AAClC,2BAAS,YAAY,EAAE,KAAK,UAAU,UAAU,UAAU,QAAQ;AAAA,gBACpE;AAAA,cACF,CAAC;AAAA,YACH;AACA;AAAA,UACF;AACA,eAAK,QAAQ,IAAI,aAAa,QAAQ,OAAO,KAAK,QAAQ,MAAM,YAAY,QAAQ;AAAA,QACtF,CAAC;AAAA,MACH;AACA,WAAK,qBAAqB,MAAM,KAAoB,oBAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,QAAQ,eAAe,OAAO,KAAK,CAAC,CAAC,GAAG,GAAG,QAAQ;AAAA,QAAO,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI;AAAA;AAAA,MAAqB,EAAE,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM;AAC3M,YAAI;AACJ,cAAM,WAAW,EAAE,CAAC,KAAK;AACzB,2BAAmB,IAAI,UAAU,QAAQ;AACzC,YAAI,MAAQ,WAAW,EAAE,CAAC,IAAI,KAAuB;AACnD,WAAC,MAAM,QAAQ,qBAAqB,OAAO,SAAS,IAAI,KAAK,CAAC,UAAU,QAAQ,CAAC;AAAA,QACnF;AACA,eAAO;AAAA,MACT,CAAC,CAAC,CAAC,CAAC;AAAA,IACN;AAAA,EACF;AACA,SAAO;AACT;AAGA,IAAI,sBAAsB,CAAO,KAAK,SAAS,SAAS,iBAAiB;AACvE,MAAI;AACJ,OAAK,QAAQ,UAAU,QAAsC,GAAG;AAC9D,YAAQ,WAAW;AACnB,UAAM,WAAW,QAAQ;AACzB,SAAK,MAAQ,YAAY,MAAQ,sBAAsB,UAAU;AAC/D,YAAM,aAAa,WAAW,SAAS,SAAS,YAAY;AAC5D,UAAI,cAAc,UAAU,YAAY;AACtC,cAAM,UAAU,WAAW,WAAW,QAAQ,SAAS,IAAI,QAAQ,UAAU,IAAI,8BAA8B,QAAQ,SAAS,GAAG;AACnI,eAAO,MAAM;AACb,gBAAQ;AAAA,MACV,OAAO;AACL,eAAO;AAAA,MACT;AACA,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,oBAAoB,QAAQ,SAAS,IAAI,QAAQ,UAAU,iBAAiB;AAAA,MAC9F;AACA,UAAI,MAAQ,UAAU,CAAC,KAAK,WAAW;AACrC,YAAI,MAAQ,eAAe;AACzB,kBAAQ,aAAa,KAAK;AAAA,QAC5B;AACA;AAAA,UAAe;AAAA,UAAM;AAAA,UAAS;AAAA;AAAA,QAAkB;AAChD,aAAK,YAAY;AAAA,MACnB;AACA,YAAM,iBAAiB,WAAW,kBAAkB,QAAQ,SAAS;AACrE,UAAI,MAAQ,QAAQ;AAClB,gBAAQ,WAAW;AAAA,MACrB;AACA,UAAI;AACF,YAAI,KAAK,OAAO;AAAA,MAClB,SAAS,GAAG;AACV,qBAAa,CAAC;AAAA,MAChB;AACA,UAAI,MAAQ,QAAQ;AAClB,gBAAQ,WAAW,CAAC;AAAA,MACtB;AACA,UAAI,MAAQ,eAAe;AACzB,gBAAQ,WAAW;AAAA,MACrB;AACA,qBAAe;AACf,4BAAsB,QAAQ,cAAc;AAAA,IAC9C,OAAO;AACL,aAAO,IAAI;AACX,YAAM,SAAS,IAAI;AACnB,qBAAe,YAAY,MAAM,EAAE;AAAA,QAAK,MAAM,QAAQ,WAAW;AAAA;AAAA,MAAsB;AAAA,IACzF;AACA,QAAI,MAAQ,SAAS,QAAQ,KAAK,OAAO;AACvC,UAAI;AACJ,UAAI,OAAO,KAAK,UAAU,UAAU;AAClC,gBAAQ,KAAK;AAAA,MACf,WAAW,MAAQ,QAAQ,OAAO,KAAK,UAAU,UAAU;AACzD,gBAAQ,aAAa,YAAY,GAAG;AACpC,YAAI,QAAQ,YAAY;AACtB,kBAAQ,KAAK,MAAM,QAAQ,UAAU;AAAA,QACvC;AACA,YAAI,MAAQ,qBAAqB,QAAQ,YAAY;AACnD,cAAI,aAAa,UAAU,QAAQ,UAAU;AAAA,QAC/C;AAAA,MACF;AACA,YAAM,WAAW,WAAW,SAAS,QAAQ,UAAU;AACvD,UAAI,CAAC,OAAO,IAAI,QAAQ,GAAG;AACzB,cAAM,oBAAoB,WAAW,kBAAkB,QAAQ,SAAS;AACxE,YAAI,CAAC,MAAQ,qBAAqB,MAAQ;AAAA,QAE1C,MAAQ,iBAAiB,QAAQ,UAAU,GAA4B;AACrE,kBAAQ,MAAM,OAAO,0BAAiB,EAAE,KAAK,OAAK,EAAE,SAAS,OAAO,QAAQ,CAAC;AAAA,QAC/E;AACA,sBAAc,UAAU,OAAO,CAAC,EAAE,QAAQ,UAAU,EAA+B;AACnF,0BAAkB;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACA,QAAM,oBAAoB,QAAQ;AAClC,QAAM,WAAW,MAAM,eAAe,SAAS,IAAI;AACnD,MAAI,MAAQ,gBAAgB,qBAAqB,kBAAkB,MAAM,GAAG;AAC1E,sBAAkB,MAAM,EAAE,KAAK,QAAQ;AAAA,EACzC,OAAO;AACL,aAAS;AAAA,EACX;AACF;AACA,IAAI,wBAAwB,cAAY;AACtC,MAAI,MAAQ,YAAY,MAAQ,mBAAmB;AACjD,aAAS,UAAU,mBAAmB;AAAA,EACxC;AACF;AAGA,IAAI,oBAAoB,SAAO;AAC7B,OAAK,IAAI,UAAU,OAA+B,GAAG;AACnD,UAAM,UAAU,WAAW,GAAG;AAC9B,UAAM,UAAU,QAAQ;AACxB,UAAM,eAAe,WAAW,qBAAqB,QAAQ,SAAS;AACtE,QAAI,MAAQ,0BAA0B;AACpC,4BAAsB,KAAK,SAAS,QAAQ,aAAa,IAAI;AAAA,IAC/D;AACA,QAAI,EAAE,QAAQ,UAAU,IAAuB;AAC7C,cAAQ,WAAW;AACnB,UAAI;AACJ,UAAI,MAAQ,mBAAmB;AAC7B,iBAAS,IAAI,aAAa,UAAU;AACpC,YAAI,QAAQ;AACV,cAAI,MAAQ,aAAa,kBAAkB,QAAQ,UAAU,GAAgC;AAC3F,kBAAM,WAAW,MAAQ,OAAO,SAAS,IAAI,YAAY,SAAS,IAAI,aAAa,QAAQ,CAAC,IAAI,SAAS,IAAI,YAAY,OAAO;AAChI,gBAAI,UAAU,OAAO,WAAW,MAAM,WAAW,IAAI;AAAA,UACvD;AACA,kCAAwB,KAAK,QAAQ,WAAW,QAAQ,OAAO;AAAA,QACjE;AAAA,MACF;AACA,UAAI,MAAQ,kBAAkB,CAAC,QAAQ;AACrC,YAAI,MAAQ,sBAAsB,MAAQ,QAAQ,MAAQ;AAAA,QAE1D,QAAQ,WAAW,IAA4B,IAA6B;AAC1E,8BAAoB,GAAG;AAAA,QACzB;AAAA,MACF;AACA,UAAI,MAAQ,cAAc;AACxB,YAAI,oBAAoB;AACxB,eAAO,oBAAoB,kBAAkB,cAAc,kBAAkB,MAAM;AACjF,cAAI,MAAQ,qBAAqB,kBAAkB,aAAa,KAAuB,kBAAkB,aAAa,MAAM,KAAK,kBAAkB,KAAK,KAAK,kBAAkB,KAAK,GAAG;AACrL,6BAAiB,SAAS,QAAQ,sBAAsB,iBAAiB;AACzE;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAQ,QAAQ,CAAC,MAAQ,qBAAqB,QAAQ,WAAW;AACnE,eAAO,QAAQ,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM;AACrE,cAAI,cAAc,MAAiB,IAAI,eAAe,UAAU,GAAG;AACjE,kBAAM,QAAQ,IAAI,UAAU;AAC5B,mBAAO,IAAI,UAAU;AACrB,gBAAI,UAAU,IAAI;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,MAAQ,oBAAoB;AAC9B,iBAAS,MAAM,oBAAoB,KAAK,SAAS,OAAO,CAAC;AAAA,MAC3D,OAAO;AACL,4BAAoB,KAAK,SAAS,OAAO;AAAA,MAC3C;AAAA,IACF,OAAO;AACL,4BAAsB,KAAK,SAAS,QAAQ,aAAa,KAAK;AAC9D,UAAI,WAAW,OAAO,SAAS,QAAQ,gBAAgB;AACrD,8BAAsB,QAAQ,cAAc;AAAA,MAC9C,WAAW,WAAW,OAAO,SAAS,QAAQ,kBAAkB;AAC9D,gBAAQ,iBAAiB,KAAK,MAAM,sBAAsB,QAAQ,cAAc,CAAC;AAAA,MACnF;AAAA,IACF;AACA,iBAAa;AAAA,EACf;AACF;AACA,IAAI,sBAAsB,SAAO;AAC/B,QAAM,gBAAgB,IAAI,MAAM,IAAI,IAAI,cAAc,MAAQ,UAAU,qBAAqB,IAAI,SAAS,MAAM,EAAE;AAClH,gBAAc,MAAM,IAAI;AACxB,eAAa,KAAK,eAAe,IAAI,UAAU;AACjD;AAIA,IAAI,qBAAqB,cAAY;AACnC,MAAI,MAAQ,YAAY,MAAQ,sBAAsB;AACpD,aAAS,UAAU,sBAAsB;AAAA,EAC3C;AACA,MAAI,MAAQ,cAAc;AACxB,aAAS,UAAU,oBAAoB;AAAA,EACzC;AACF;AACA,IAAI,uBAAuB,CAAM,QAAO;AACtC,OAAK,IAAI,UAAU,OAA+B,GAAG;AACnD,UAAM,UAAU,WAAW,GAAG;AAC9B,QAAI,MAAQ,cAAc;AACxB,UAAI,QAAQ,eAAe;AACzB,gBAAQ,cAAc,IAAI,gBAAc,WAAW,CAAC;AACpD,gBAAQ,gBAAgB;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,CAAC,MAAQ,UAAU;AACrB,yBAAmB,GAAG;AAAA,IACxB,WAAW,WAAW,OAAO,SAAS,QAAQ,gBAAgB;AAC5D,yBAAmB,QAAQ,cAAc;AAAA,IAC3C,WAAW,WAAW,OAAO,SAAS,QAAQ,kBAAkB;AAC9D,cAAQ,iBAAiB,KAAK,MAAM,mBAAmB,QAAQ,cAAc,CAAC;AAAA,IAChF;AAAA,EACF;AACF;AAIA,IAAI,uBAAuB,CAAC,sBAAsB,wBAAwB;AACxE,iBAAe,oBAAoB;AACnC,uBAAqB,oBAAoB;AACzC,kBAAgB,oBAAoB;AACpC,mBAAiB,oBAAoB;AACrC,iCAA+B,oBAAoB;AACnD,8BAA4B,oBAAoB;AAChD,8BAA4B,oBAAoB;AAChD,mBAAiB,oBAAoB;AACrC,sBAAoB,sBAAsB,mBAAmB;AAC7D,uBAAqB,oBAAoB;AAC3C;AACA,IAAI,iBAAiB,0BAAwB;AAC3C,QAAM,eAAe,qBAAqB;AAC1C,uBAAqB,YAAY,SAAU,MAAM;AAC/C,UAAM,UAAU;AAChB,UAAM,cAAc,MAAQ,YAAY,QAAQ,cAAc,iBAAiB;AAC/E,UAAM,aAAa,aAAa,KAAK,SAAS,cAAc,OAAO,KAAK;AACxE,QAAI,MAAQ,QAAQ,CAAC,eAAe,MAAM;AACxC,UAAI,KAAK;AACT,UAAI,SAAS;AACb,YAAM,kBAAkB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO;AAC/I,aAAO,KAAK,QAAQ,WAAW,QAAQ,MAAM;AAC3C,kBAAU,QAAQ,WAAW,EAAE,EAAE,MAAM;AACvC,yBAAiB,gBAAgB,MAAM,kBAAgB,CAAC,QAAQ,WAAW,EAAE,EAAE,YAAY,CAAC;AAC5F,YAAI,SAAS;AACX,cAAI,MAAQ,sBAAsB,WAAW,eAAe;AAC1D,uBAAW,cAAc,QAAQ,UAAU,IAAI,CAAC;AAAA,UAClD,OAAO;AACL,uBAAW,YAAY,QAAQ,UAAU,IAAI,CAAC;AAAA,UAChD;AAAA,QACF;AACA,YAAI,gBAAgB;AAClB,qBAAW,YAAY,QAAQ,WAAW,EAAE,EAAE,UAAU,IAAI,CAAC;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAI,uBAAuB,0BAAwB;AACjD,uBAAqB,gBAAgB,qBAAqB;AAC1D,uBAAqB,cAAc,SAAU,UAAU;AACrD,UAAM,WAAW,SAAS,MAAM,IAAI,YAAY,QAAQ;AACxD,UAAM,WAAW,gBAAgB,KAAK,YAAY,UAAU,KAAK,OAAO;AACxE,QAAI,UAAU;AACZ,YAAM,iBAAiB,sBAAsB,UAAU,QAAQ;AAC/D,YAAM,cAAc,eAAe,eAAe,SAAS,CAAC;AAC5D,YAAM,eAAe,aAAa,YAAY,YAAY,UAAU,YAAY,WAAW;AAC3F,mCAA6B,IAAI;AACjC,aAAO;AAAA,IACT;AACA,WAAO,KAAK,cAAc,QAAQ;AAAA,EACpC;AACF;AACA,IAAI,uBAAuB,sBAAoB;AAC7C,mBAAiB,gBAAgB,iBAAiB;AAClD,mBAAiB,cAAc,SAAU,UAAU;AACjD,QAAI,YAAY,OAAO,SAAS,MAAM,MAAM,aAAa;AACvD,YAAM,WAAW,gBAAgB,KAAK,YAAY,SAAS,MAAM,GAAG,KAAK,OAAO;AAChF,UAAI,UAAU;AACZ,cAAM,iBAAiB,sBAAsB,UAAU,SAAS,MAAM,CAAC;AACvE,cAAM,eAAe,eAAe,KAAK,OAAK,MAAM,QAAQ;AAC5D,YAAI,cAAc;AAChB,uBAAa,OAAO;AACpB,uCAA6B,IAAI;AACjC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK,cAAc,QAAQ;AAAA,EACpC;AACF;AACA,IAAI,mBAAmB,0BAAwB;AAC7C,QAAM,kBAAkB,qBAAqB;AAC7C,uBAAqB,UAAU,YAAa,aAAa;AACvD,gBAAY,QAAQ,cAAY;AAC9B,UAAI,OAAO,aAAa,UAAU;AAChC,mBAAW,KAAK,cAAc,eAAe,QAAQ;AAAA,MACvD;AACA,YAAM,WAAW,SAAS,MAAM,IAAI,YAAY,QAAQ;AACxD,YAAM,WAAW,gBAAgB,KAAK,YAAY,UAAU,KAAK,OAAO;AACxE,UAAI,UAAU;AACZ,cAAM,kBAAkB,SAAS,eAAe,EAAE;AAClD,wBAAgB,MAAM,IAAI;AAC1B,iBAAS,MAAM,EAAE,WAAW,cAAc,eAAe;AACzD,iBAAS,MAAM,IAAI;AACnB,cAAM,iBAAiB,sBAAsB,UAAU,QAAQ;AAC/D,cAAM,cAAc,eAAe,CAAC;AACpC,eAAO,aAAa,YAAY,YAAY,UAAU,YAAY,WAAW;AAAA,MAC/E;AACA,UAAI,SAAS,aAAa,KAAK,CAAC,CAAC,SAAS,aAAa,MAAM,GAAG;AAC9D,iBAAS,SAAS;AAAA,MACpB;AACA,aAAO,gBAAgB,KAAK,MAAM,QAAQ;AAAA,IAC5C,CAAC;AAAA,EACH;AACF;AACA,IAAI,kBAAkB,0BAAwB;AAC5C,uBAAqB,SAAS,YAAa,aAAa;AACtD,gBAAY,QAAQ,cAAY;AAC9B,UAAI,OAAO,aAAa,UAAU;AAChC,mBAAW,KAAK,cAAc,eAAe,QAAQ;AAAA,MACvD;AACA,WAAK,YAAY,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AACA,IAAI,8BAA8B,0BAAwB;AACxD,QAAM,6BAA6B,qBAAqB;AACxD,uBAAqB,qBAAqB,SAAU,UAAU,MAAM;AAClE,QAAI,aAAa,gBAAgB,aAAa,aAAa;AACzD,aAAO,2BAA2B,KAAK,MAAM,UAAU,IAAI;AAAA,IAC7D;AACA,UAAM,YAAY,KAAK,cAAc,cAAc,GAAG;AACtD,QAAI;AACJ,cAAU,YAAY;AACtB,QAAI,aAAa,cAAc;AAC7B,aAAO,OAAO,UAAU,YAAY;AAClC,aAAK,QAAQ,IAAI;AAAA,MACnB;AAAA,IACF,WAAW,aAAa,aAAa;AACnC,aAAO,OAAO,UAAU,YAAY;AAClC,aAAK,OAAO,IAAI;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,8BAA8B,0BAAwB;AACxD,uBAAqB,qBAAqB,SAAU,UAAU,MAAM;AAClE,SAAK,mBAAmB,UAAU,IAAI;AAAA,EACxC;AACF;AACA,IAAI,iCAAiC,0BAAwB;AAC3D,QAAM,gCAAgC,qBAAqB;AAC3D,uBAAqB,wBAAwB,SAAU,UAAU,SAAS;AACxE,QAAI,aAAa,gBAAgB,aAAa,aAAa;AACzD,aAAO,8BAA8B,KAAK,MAAM,UAAU,OAAO;AAAA,IACnE;AACA,QAAI,aAAa,cAAc;AAC7B,WAAK,QAAQ,OAAO;AACpB,aAAO;AAAA,IACT,WAAW,aAAa,aAAa;AACnC,WAAK,OAAO,OAAO;AACnB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAI,mBAAmB,0BAAwB;AAC7C,QAAM,aAAa,OAAO,yBAAyB,KAAK,WAAW,aAAa;AAChF,SAAO,eAAe,sBAAsB,iBAAiB,UAAU;AACvE,MAAI,MAAQ,+BAA+B;AACzC,WAAO,eAAe,sBAAsB,eAAe;AAAA;AAAA;AAAA,MAGzD,MAAM;AACJ,cAAM,eAAe,qBAAqB,KAAK,UAAU;AACzD,cAAM,cAAc,aAAa,IAAI,UAAQ;AAC3C,cAAI,IAAI;AACR,gBAAM,OAAO,CAAC;AACd,cAAI,cAAc,KAAK;AACvB,iBAAO,eAAe,YAAY,MAAM,MAAM,KAAK,MAAM,GAAG;AAC1D,gBAAI,YAAY,aAAa,KAAqB,YAAY,aAAa,GAAsB;AAC/F,mBAAK,MAAM,MAAM,KAAK,YAAY,gBAAgB,OAAO,SAAS,GAAG,KAAK,MAAM,OAAO,KAAK,EAAE;AAAA,YAChG;AACA,0BAAc,YAAY;AAAA,UAC5B;AACA,iBAAO,KAAK,OAAO,SAAO,QAAQ,EAAE,EAAE,KAAK,GAAG;AAAA,QAChD,CAAC,EAAE,OAAO,UAAQ,SAAS,EAAE,EAAE,KAAK,GAAG;AACvC,eAAO,MAAM,cAAc;AAAA,MAC7B;AAAA;AAAA;AAAA;AAAA,MAIA,IAAI,OAAO;AACT,cAAM,eAAe,qBAAqB,KAAK,UAAU;AACzD,qBAAa,QAAQ,UAAQ;AAC3B,cAAI,cAAc,KAAK;AACvB,iBAAO,eAAe,YAAY,MAAM,MAAM,KAAK,MAAM,GAAG;AAC1D,kBAAM,MAAM;AACZ,0BAAc,YAAY;AAC1B,gBAAI,OAAO;AAAA,UACb;AACA,cAAI,KAAK,MAAM,MAAM,IAAI;AACvB,kBAAM,WAAW,KAAK,cAAc,eAAe,KAAK;AACxD,qBAAS,MAAM,IAAI;AACnB,yBAAa,KAAK,eAAe,UAAU,KAAK,WAAW;AAAA,UAC7D,OAAO;AACL,iBAAK,OAAO;AAAA,UACd;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,WAAO,eAAe,sBAAsB,eAAe;AAAA,MACzD,MAAM;AACJ,YAAI;AACJ,cAAM,WAAW,gBAAgB,KAAK,YAAY,IAAI,KAAK,OAAO;AAClE,cAAM,KAAK,YAAY,OAAO,SAAS,SAAS,gBAAgB,OAAO,SAAS,GAAG,cAAc,GAAmB;AAClH,iBAAO,SAAS,YAAY;AAAA,QAC9B,WAAW,UAAU;AACnB,iBAAO,SAAS;AAAA,QAClB,OAAO;AACL,iBAAO,KAAK;AAAA,QACd;AAAA,MACF;AAAA,MACA,IAAI,OAAO;AACT,YAAI;AACJ,cAAM,WAAW,gBAAgB,KAAK,YAAY,IAAI,KAAK,OAAO;AAClE,cAAM,KAAK,YAAY,OAAO,SAAS,SAAS,gBAAgB,OAAO,SAAS,GAAG,cAAc,GAAmB;AAClH,mBAAS,YAAY,cAAc;AAAA,QACrC,WAAW,UAAU;AACnB,mBAAS,cAAc;AAAA,QACzB,OAAO;AACL,eAAK,gBAAgB;AACrB,gBAAM,gBAAgB,KAAK,MAAM;AACjC,cAAI,eAAe;AACjB,yBAAa,MAAM,eAAe,KAAK,UAAU;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAI,sBAAsB,CAAC,KAAK,YAAY;AAAA,EAC1C,MAAM,qBAAqB,MAAM;AAAA,IAC/B,KAAK,GAAG;AACN,aAAO,KAAK,CAAC;AAAA,IACf;AAAA,EACF;AACA,MAAI,QAAQ,UAAU,GAA4B;AAChD,UAAM,eAAe,IAAI,iBAAiB,YAAY;AACtD,WAAO,eAAe,KAAK,YAAY;AAAA,MACrC,MAAM;AACJ,eAAO,KAAK,WAAW,IAAI,OAAK,EAAE,aAAa,CAAC;AAAA,MAClD;AAAA,IACF,CAAC;AACD,WAAO,eAAe,KAAK,qBAAqB;AAAA,MAC9C,MAAM;AACJ,eAAO,IAAI,SAAS;AAAA,MACtB;AAAA,IACF,CAAC;AACD,WAAO,eAAe,KAAK,cAAc;AAAA,MACvC,MAAM;AACJ,cAAM,aAAa,aAAa,KAAK,IAAI;AACzC,aAAK,IAAI,UAAU,OAA+B,KAAK,WAAW,IAAI,EAAE,UAAU,GAAqB;AACrG,gBAAM,SAAS,IAAI,aAAa;AAChC,mBAAS,KAAK,GAAG,KAAK,WAAW,QAAQ,MAAM;AAC7C,kBAAM,OAAO,WAAW,EAAE,EAAE,MAAM;AAClC,gBAAI,MAAM;AACR,qBAAO,KAAK,IAAI;AAAA,YAClB;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,aAAa,KAAK,UAAU;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAI,uBAAuB,gBAAc;AACvC,QAAM,eAAe,CAAC;AACtB,aAAW,aAAa,MAAM,KAAK,UAAU,GAAG;AAC9C,QAAI,UAAU,MAAM,GAAG;AACrB,mBAAa,KAAK,SAAS;AAAA,IAC7B;AACA,iBAAa,KAAK,GAAG,qBAAqB,UAAU,UAAU,CAAC;AAAA,EACjE;AACA,SAAO;AACT;AACA,IAAI,cAAc,UAAQ,KAAK,MAAM,KAAK,KAAK,aAAa,KAAK,KAAK,aAAa,MAAM,KAAK;AAC9F,IAAI,kBAAkB,CAAC,YAAY,UAAU,aAAa;AACxD,MAAI,KAAK;AACT,MAAI;AACJ,SAAO,KAAK,WAAW,QAAQ,MAAM;AACnC,gBAAY,WAAW,EAAE;AACzB,QAAI,UAAU,MAAM,KAAK,UAAU,MAAM,MAAM,YAAY,UAAU,MAAM,MAAM,UAAU;AACzF,aAAO;AAAA,IACT;AACA,gBAAY,gBAAgB,UAAU,YAAY,UAAU,QAAQ;AACpE,QAAI,WAAW;AACb,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,wBAAwB,CAAC,GAAG,aAAa;AAC3C,QAAM,aAAa,CAAC,CAAC;AACrB,UAAQ,IAAI,EAAE,gBAAgB,EAAE,MAAM,MAAM,UAAU;AACpD,eAAW,KAAK,CAAC;AAAA,EACnB;AACA,SAAO;AACT;AAMA,IAAI,qBAAqB,CAAC,MAAM,gBAAgB;AAC9C,QAAM,UAAU;AAAA,IACd,SAAS,YAAY,CAAC;AAAA,IACtB,WAAW,YAAY,CAAC;AAAA,EAC1B;AACA,MAAI,MAAQ,QAAQ;AAClB,YAAQ,YAAY,YAAY,CAAC;AAAA,EACnC;AACA,MAAI,MAAQ,cAAc;AACxB,YAAQ,cAAc,YAAY,CAAC;AAAA,EACrC;AACA,MAAI,MAAQ,eAAe;AACzB,YAAQ,aAAa,KAAK;AAAA,EAC5B;AACA,MAAI,MAAQ,SAAS;AACnB,YAAQ,mBAAmB,CAAC;AAAA,EAC9B;AACA,MAAI,MAAQ,aAAa,CAAC,kBAAkB,QAAQ,UAAU,GAAgC;AAC5F,YAAQ,WAAW;AAAA,EACrB;AACA,MAAI,MAAQ,uBAAuB;AACjC,QAAI,MAAQ,UAAU,QAAQ,UAAU,GAAgC;AACtE,2BAAqB,KAAK,WAAW,OAAO;AAAA,IAC9C;AAAA,EACF,OAAO;AACL,QAAI,MAAQ,mBAAmB;AAC7B,0BAAoB,KAAK,WAAW,OAAO;AAAA,IAC7C;AACA,QAAI,MAAQ,cAAc;AACxB,qBAAe,KAAK,SAAS;AAAA,IAC/B;AACA,QAAI,MAAQ,oBAAoB;AAC9B,2BAAqB,KAAK,SAAS;AAAA,IACrC;AACA,QAAI,MAAQ,4BAA4B,QAAQ,UAAU,GAAgC;AACxF,uBAAiB,KAAK,SAAS;AAAA,IACjC;AAAA,EACF;AACA,QAAM,4BAA4B,KAAK,UAAU;AACjD,QAAM,+BAA+B,KAAK,UAAU;AACpD,SAAO,OAAO,KAAK,WAAW;AAAA,IAC5B,iBAAiB;AACf,mBAAa,MAAM,OAAO;AAAA,IAC5B;AAAA,IACA,oBAAoB;AAClB,YAAM,UAAU,WAAW,IAAI;AAC/B,4BAAsB,MAAM,SAAS,QAAQ,aAAa,KAAK;AAC/D,wBAAkB,IAAI;AACtB,UAAI,MAAQ,qBAAqB,2BAA2B;AAC1D,kCAA0B,KAAK,IAAI;AAAA,MACrC;AAAA,IACF;AAAA,IACA,uBAAuB;AACrB,2BAAqB,IAAI;AACzB,UAAI,MAAQ,wBAAwB,8BAA8B;AAChE,qCAA6B,KAAK,IAAI;AAAA,MACxC;AAAA,IACF;AAAA,IACA,iBAAiB;AACf,UAAI,gBAAgB;AAClB,YAAI,CAAC,KAAK,YAAY;AACpB,cAAI,MAAQ,sBAAsB;AAChC,iBAAK,aAAa;AAAA,cAChB,MAAM;AAAA,cACN,gBAAgB,CAAC,EAAE,QAAQ,UAAU;AAAA,YACvC,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,aAAa;AAAA,cAChB,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,cAAI,KAAK,WAAW,SAAS,QAAQ;AACnC,kBAAM,IAAI,MAAM,6CAA6C,QAAQ,SAAS,oBAAoB,KAAK,WAAW,IAAI,+CAA+C;AAAA,UACvK;AAAA,QACF;AAAA,MACF,OAAO;AACL,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACD,OAAK,KAAK,QAAQ;AAClB,SAAO;AAAA,IAAe;AAAA,IAAM;AAAA,IAAS,IAA+B;AAAA;AAAA,EAAkB;AACxF;AAkNA,IAAI,wBAAwB,CAAC,KAAK,SAAS,WAAW,0BAA0B;AAC9E,MAAI,MAAQ,gBAAgB,WAAW;AACrC,QAAI,MAAQ,0BAA0B;AACpC,UAAI,uBAAuB;AACzB,oBAAY,UAAU;AAAA,UAAO,CAAC,CAAC,KAAK,MAAM,QAAQ;AAAA;AAAA,QAAqB;AAAA,MACzE,OAAO;AACL,oBAAY,UAAU,OAAO,CAAC,CAAC,KAAK,MAAM,EAAE,QAAQ,GAAsB;AAAA,MAC5E;AAAA,IACF;AACA,cAAU,IAAI,CAAC,CAAC,OAAO,MAAM,MAAM,MAAM;AACvC,YAAM,SAAS,MAAQ,qBAAqB,sBAAsB,KAAK,KAAK,IAAI;AAChF,YAAM,UAAU,kBAAkB,SAAS,MAAM;AACjD,YAAM,OAAO,iBAAiB,KAAK;AACnC,UAAI,IAAI,QAAQ,MAAM,SAAS,IAAI;AACnC,OAAC,QAAQ,gBAAgB,QAAQ,iBAAiB,CAAC,GAAG,KAAK,MAAM,IAAI,IAAI,QAAQ,MAAM,SAAS,IAAI,CAAC;AAAA,IACvG,CAAC;AAAA,EACH;AACF;AACA,IAAI,oBAAoB,CAAC,SAAS,eAAe,QAAM;AACrD,MAAI;AACJ,MAAI;AACF,QAAI,MAAQ,UAAU;AACpB,UAAI,QAAQ,UAAU,KAAyB;AAC7C,SAAC,KAAK,QAAQ,mBAAmB,OAAO,SAAS,GAAG,UAAU,EAAE,EAAE;AAAA,MACpE,OAAO;AACL,SAAC,QAAQ,oBAAoB,QAAQ,qBAAqB,CAAC,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;AAAA,MACrF;AAAA,IACF,OAAO;AACL,cAAQ,cAAc,UAAU,EAAE,EAAE;AAAA,IACtC;AAAA,EACF,SAAS,GAAG;AACV,iBAAa,CAAC;AAAA,EAChB;AACF;AACA,IAAI,wBAAwB,CAAC,KAAK,UAAU;AAC1C,MAAI,MAAQ,8BAA8B,QAAQ,EAAwB,QAAO;AACjF,MAAI,MAAQ,4BAA4B,QAAQ,EAAsB,QAAO;AAC7E,MAAI,MAAQ,0BAA0B,QAAQ,GAAqB,QAAO,IAAI;AAC9E,MAAI,MAAQ,4BAA4B,QAAQ,MAAyB,IAAI,cAAe,QAAO,IAAI;AACvG,SAAO;AACT;AACA,IAAI,mBAAmB,WAAS,0BAA0B;AAAA,EACxD,UAAU,QAAQ,OAAqB;AAAA,EACvC,UAAU,QAAQ,OAAqB;AACzC,KAAK,QAAQ,OAAqB;", "names": []}