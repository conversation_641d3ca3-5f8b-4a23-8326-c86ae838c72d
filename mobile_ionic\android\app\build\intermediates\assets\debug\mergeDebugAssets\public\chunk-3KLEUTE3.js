import{a as w}from"./chunk-FULEFYAM.js";import{a as L}from"./chunk-NETZAO6G.js";import{$ as E,Cb as G,Da as R,a as A,b as N,d as x,e as F,ea as I,i as M,m as k,o as p,s as S,zb as D}from"./chunk-YFIZFQXH.js";import{a as y,h as u}from"./chunk-LNJ3S2LQ.js";function q(){if(typeof process>"u"){var o=typeof window<"u"?window:{},r=5e3,a=Date.now(),e=!1;o.document.addEventListener("deviceready",function(){console.log("Ionic Native: deviceready event fired after "+(Date.now()-a)+" ms"),e=!0}),setTimeout(function(){!e&&o.cordova&&console.warn("Ionic Native: deviceready did not fire within "+r+"ms. This can happen when plugins are in an inconsistent state. Try removing plugins from plugins/ and reinstalling them.")},r)}}var W={error:"cordova_not_available"},_={error:"plugin_not_installed"};function C(o){var r=function(){if(Promise)return new Promise(function(n,i){o(n,i)});console.error("No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.")};if(typeof window<"u"&&window.angular){var a=window.document,e=window.angular.element(a.querySelector("[ng-app]")||a.body).injector();if(e){var t=e.get("$q");return t(function(n,i){o(n,i)})}console.warn("Angular 1 was detected but $q couldn't be retrieved. This is usually when the app is not bootstrapped on the html or body tag. Falling back to native promises which won't trigger an automatic digest when promises resolve.")}return r()}function H(o,r,a,e){e===void 0&&(e={});var t,n,i=C(function(s,c){e.destruct?t=h(o,r,a,e,function(){for(var l=[],f=0;f<arguments.length;f++)l[f]=arguments[f];return s(l)},function(){for(var l=[],f=0;f<arguments.length;f++)l[f]=arguments[f];return c(l)}):t=h(o,r,a,e,s,c),n=c});return t&&t.error&&(i.catch(function(){}),typeof n=="function"&&n(t.error)),i}function J(o,r,a,e){return e===void 0&&(e={}),C(function(t,n){var i=h(o,r,a,e);i?i.error?n(i.error):i.then&&i.then(t).catch(n):n({error:"unexpected_error"})})}function O(o,r,a,e){return e===void 0&&(e={}),new A(function(t){var n;return e.destruct?n=h(o,r,a,e,function(){for(var i=[],s=0;s<arguments.length;s++)i[s]=arguments[s];return t.next(i)},function(){for(var i=[],s=0;s<arguments.length;s++)i[s]=arguments[s];return t.error(i)}):n=h(o,r,a,e,t.next.bind(t),t.error.bind(t)),n&&n.error&&(t.error(n.error),t.complete()),function(){try{if(e.clearFunction)return e.clearWithArgs?h(o,e.clearFunction,a,e,t.next.bind(t),t.error.bind(t)):h(o,e.clearFunction,[])}catch(i){console.warn("Unable to clear the previous observable watch for",o.constructor.getPluginName(),r),console.warn(i)}}})}function Y(o,r){return r=typeof window<"u"&&r?B(window,r):r||(typeof window<"u"?window:{}),M(r,o)}function v(o,r,a){var e,t;typeof o=="string"?e=o:(e=o.constructor.getPluginRef(),a=o.constructor.getPluginName(),t=o.constructor.getPluginInstallName());var n=b(e);return!n||r&&typeof n[r]>"u"?typeof window>"u"||!window.cordova?(K(a,r),W):(Z(a,t,r),_):!0}function z(o,r,a,e){if(r===void 0&&(r={}),r.sync)return o;if(r.callbackOrder==="reverse")o.unshift(e),o.unshift(a);else if(r.callbackStyle==="node")o.push(function(s,c){s?e(s):a(c)});else if(r.callbackStyle==="object"&&r.successName&&r.errorName){var t={};t[r.successName]=a,t[r.errorName]=e,o.push(t)}else if(typeof r.successIndex<"u"||typeof r.errorIndex<"u"){var n=function(){r.successIndex>o.length?o[r.successIndex]=a:o.splice(r.successIndex,0,a)},i=function(){r.errorIndex>o.length?o[r.errorIndex]=e:o.splice(r.errorIndex,0,e)};r.successIndex>r.errorIndex?(i(),n()):(n(),i())}else o.push(a),o.push(e);return o}function h(o,r,a,e,t,n){e===void 0&&(e={}),a=z(a,e,t,n);var i=v(o,r);if(i===!0){var s=b(o.constructor.getPluginRef());return s[r].apply(s,a)}else return i}function b(o){return typeof window<"u"?B(window,o):null}function B(o,r){for(var a=r.split("."),e=o,t=0;t<a.length;t++){if(!e)return null;e=e[a[t]]}return e}function Z(o,r,a){console.warn(a?"Native: tried calling "+o+"."+a+", but the "+o+" plugin is not installed.":"Native: tried accessing the "+o+" plugin but it's not installed."),r&&console.warn("Install the "+o+" plugin: 'ionic cordova plugin add "+r+"'")}function K(o,r){typeof process>"u"&&console.warn(r?"Native: tried calling "+o+"."+r+", but Cordova is not available. Make sure to include cordova.js or run in a device/simulator":"Native: tried accessing the "+o+" plugin but Cordova is not available. Make sure to include cordova.js or run in a device/simulator")}var T=function(o,r,a){return a===void 0&&(a={}),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return a.sync?h(o,r,e,a):a.observable?O(o,r,e,a):a.eventObservable&&a.event?Y(a.event,a.element):a.otherPromise?J(o,r,e,a):H(o,r,e,a)}};function $(o,r){for(var a=r.split("."),e=o,t=0;t<a.length;t++){if(!e)return null;e=e[a[t]]}return e}var P=function(){function o(){}return o.installed=function(){var r=v(this.pluginRef)===!0;return r},o.getPlugin=function(){return typeof window<"u"?$(window,this.pluginRef):null},o.getPluginName=function(){var r=this.pluginName;return r},o.getPluginRef=function(){var r=this.pluginRef;return r},o.getPluginInstallName=function(){var r=this.plugin;return r},o.getSupportedPlatforms=function(){var r=this.platforms;return r},o.pluginName="",o.pluginRef="",o.plugin="",o.repo="",o.platforms=[],o.install="",o}();function d(o,r,a,e){return T(o,r,a).apply(this,e)}q();var U=function(o){x(r,o);function r(){return o!==null&&o.apply(this,arguments)||this}return r.prototype.getAPNSToken=function(){return d(this,"getAPNSToken",{},arguments)},r.prototype.getToken=function(){return d(this,"getToken",{},arguments)},r.prototype.onTokenRefresh=function(){return d(this,"onTokenRefresh",{observable:!0},arguments)},r.prototype.subscribeToTopic=function(a){return d(this,"subscribeToTopic",{},arguments)},r.prototype.unsubscribeFromTopic=function(a){return d(this,"unsubscribeFromTopic",{},arguments)},r.prototype.hasPermission=function(){return d(this,"hasPermission",{},arguments)},r.prototype.onNotification=function(){return d(this,"onNotification",{observable:!0,successIndex:0,errorIndex:2},arguments)},r.prototype.clearAllNotifications=function(){return d(this,"clearAllNotifications",{},arguments)},r.prototype.requestPushPermissionIOS=function(a){return d(this,"requestPushPermissionIOS",{},arguments)},r.prototype.createNotificationChannelAndroid=function(a){return d(this,"createNotificationChannelAndroid",{},arguments)},r.\u0275fac=(()=>{let a;return function(t){return(a||(a=S(r)))(t||r)}})(),r.\u0275prov=k({token:r,factory:r.\u0275fac}),r.pluginName="FCM",r.plugin="cordova-plugin-fcm-with-dependecy-updated",r.pluginRef="FCMPlugin",r.repo="https://github.com/andrehtissot/cordova-plugin-fcm-with-dependecy-updated",r.platforms=["Android","iOS"],r=F([],r),r}(P);var g=L("FirebaseMessaging",{web:()=>import("./chunk-HCFMJL6U.js").then(o=>new o.FirebaseMessagingWeb)});var Be=(()=>{let r=class r{constructor(e,t,n,i,s,c){this.fcm=e,this.http=t,this.platform=n,this.toastCtrl=i,this.alertCtrl=s,this.router=c,this.notificationSubject=new N,this.notifications$=this.notificationSubject.asObservable()}initPush(){return u(this,null,function*(){try{if(this.platform.is("cordova")||this.platform.is("capacitor")){if(console.log("Initializing FCM..."),this.platform.is("android")&&(yield this.createAndroidNotificationChannels()),(yield this.checkGooglePlayServices())?localStorage.removeItem("google_play_services_missing"):(console.warn("Google Play Services not available. FCM may not work properly."),this.alertCtrl.create({header:"Google Play Services Required",message:"This app requires Google Play Services for push notifications. Please install or update Google Play Services and restart the app.",buttons:["OK"]}).then(t=>t.present()),localStorage.setItem("google_play_services_missing","true")),this.platform.is("capacitor"))try{let t=yield g.requestPermissions();console.log("FCM permission result:",t),t.receive==="granted"?(console.log("FCM permission granted"),console.log("Device registered with FCM")):(console.warn("FCM permission not granted:",t.receive),this.alertCtrl.create({header:"Notification Permission Required",message:"This app requires notification permissions to alert you about emergencies. Please enable notifications for this app in your device settings.",buttons:["OK"]}).then(n=>n.present()))}catch(t){console.error("Error initializing Capacitor Firebase Messaging:",t)}try{let t=yield this.getToken();console.log("FCM Token registered:",t.substring(0,20)+"..."),this.registerTokenWithBackend(t)}catch(t){console.error("Error getting FCM token:",t)}try{this.platform.is("cordova")&&this.fcm.onTokenRefresh().subscribe({next:t=>{console.log("FCM Token refreshed (Cordova):",t),this.registerTokenWithBackend(t)},error:t=>{console.error("Error in token refresh (Cordova):",t)}}),this.platform.is("capacitor")&&g.addListener("tokenReceived",t=>{console.log("FCM Token refreshed (Capacitor):",t.token),this.registerTokenWithBackend(t.token)})}catch(t){console.error("Failed to set up token refresh:",t)}this.setupNotificationListeners()}else console.log("FCM not initialized: not running on a device")}catch(e){console.error("Error in initPush:",e)}})}getToken(){return u(this,null,function*(){if(this.platform.is("capacitor"))try{let e=yield g.getToken();return console.log("Got FCM token from Capacitor Firebase Messaging:",e.token),e.token}catch(e){console.error("Error getting token from Capacitor Firebase Messaging:",e);try{let t=yield this.fcm.getToken();return console.log("Got FCM token from Cordova FCM plugin:",t),t}catch(t){throw console.error("Error getting token from Cordova FCM plugin:",t),t}}else{if(this.platform.is("cordova"))return this.fcm.getToken();{let e="browser-mock-token-"+Math.random().toString(36).substring(2,15);return console.log("Using mock FCM token for browser:",e),Promise.resolve(e)}}})}registerTokenWithBackend(e,t){if(localStorage.getItem("fcm_token")===e){console.log("Token already registered, skipping registration");return}let i="web";this.platform.is("ios")?i="ios":this.platform.is("android")&&(i="android"),console.log(`Registering ${i} token with backend...`);let s={token:e,device_type:i,project_id:w.firebase.projectId||"last-5acaf"};if(t)s.user_id=t,console.log(`Associating token with user ID: ${t}`);else{let c=localStorage.getItem("token");if(c)try{let l=this.parseJwt(c);l&&l.sub&&(s.user_id=l.sub,console.log(`Associating token with user ID from JWT: ${l.sub}`))}catch(l){console.error("Error parsing JWT token:",l)}}e&&(w.firebase.projectId||s.project_id)?(localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registering","true"),this.http.post(`${w.apiUrl}/device-token`,s).subscribe({next:c=>{console.log("Token registered with backend:",c),localStorage.removeItem("fcm_token_registering")},error:c=>{console.error("Error registering token:",c),localStorage.removeItem("fcm_token_registering")}})):(console.log("Skipping token registration: Missing project ID or token"),e&&localStorage.setItem("fcm_token",e))}parseJwt(e){try{let n=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),i=decodeURIComponent(atob(n).split("").map(function(s){return"%"+("00"+s.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(i)}catch(t){return console.error("Error parsing JWT token:",t),null}}setupNotificationListeners(){this.setupCapacitorNotificationListeners(),this.setupCordovaNotificationListeners()}setupCapacitorNotificationListeners(){try{this.platform.is("capacitor")&&(console.log("Setting up Capacitor Firebase Messaging notification listeners"),g.addListener("notificationReceived",e=>{console.log("Capacitor: Notification received in foreground:",e);let t=e.notification.data||{};this.processNotification(y({title:e.notification.title||"",body:e.notification.body||"",category:t.category||"",severity:t.severity||"low",wasTapped:!1,notification_id:t.notification_id||null,time:new Date().toISOString()},Object.keys(t).filter(n=>!["category","severity","notification_id"].includes(n)).reduce((n,i)=>(n[i]=t[i],n),{})))}),g.addListener("notificationActionPerformed",e=>{console.log("Capacitor: Notification tapped:",e);let t=e.notification.data||{};this.processNotification(y({title:e.notification.title||"",body:e.notification.body||"",category:t.category||"",severity:t.severity||"low",wasTapped:!0,notification_id:t.notification_id||null,time:new Date().toISOString()},Object.keys(t).filter(n=>!["category","severity","notification_id"].includes(n)).reduce((n,i)=>(n[i]=t[i],n),{})))}))}catch(e){console.error("Failed to set up Capacitor notification listeners:",e)}}setupCordovaNotificationListeners(){try{this.platform.is("cordova")&&(console.log("Setting up Cordova FCM notification listeners"),this.fcm.onNotification().subscribe({next:e=>{console.log("Cordova FCM notification received:",e);let t=y({},e);this.processNotification(y({title:e.title||e.aps&&e.aps.alert&&e.aps.alert.title||"",body:e.body||e.aps&&e.aps.alert&&e.aps.alert.body||e.message||"",category:e.category||"",severity:e.severity||"low",wasTapped:e.wasTapped||!1,notification_id:e.notification_id||null,time:e.time||new Date().toISOString()},Object.keys(t).filter(n=>!["title","body","category","severity","wasTapped","notification_id","time"].includes(n)).reduce((n,i)=>(n[i]=t[i],n),{})))},error:e=>{console.error("Error in Cordova FCM notification subscription:",e)}}))}catch(e){console.error("Failed to set up Cordova FCM notification listeners:",e)}}processNotification(e){try{console.log("Processed notification:",{title:e.title,body:e.body,category:e.category,severity:e.severity,wasTapped:e.wasTapped,notification_id:e.notification_id,project_id:w.firebase.projectId||"new-firebase-project"}),this.notificationSubject.next(e),e.wasTapped?(console.log("Notification tapped in background"),this.handleBackgroundNotification(e)):(console.log("Notification received in foreground"),this.handleForegroundNotification(e))}catch(t){console.error("Error processing notification:",t,e)}}handleForegroundNotification(e){return u(this,null,function*(){try{console.log("\u{1F514} Handling foreground notification:",e),this.vibrateDevice(),this.playNotificationSound(),yield new Promise(t=>setTimeout(t,500)),yield this.showEmergencyNotificationModal(e)}catch(t){console.error("\u274C Error handling foreground notification:",t),yield this.showFallbackToast(e)}})}showEmergencyNotificationModal(e){return u(this,null,function*(){var t;try{console.log("\u{1F4F1} Creating emergency notification modal...",e),yield new Promise(s=>setTimeout(s,100));let n=yield this.alertCtrl.getTop();n&&(console.log("\u26A0\uFE0F Alert already present, dismissing first..."),yield n.dismiss(),yield new Promise(s=>setTimeout(s,500))),console.log("\u{1F35E} Showing immediate toast notification as backup..."),yield this.showFallbackToast(e);let i=yield this.alertCtrl.create({header:e.title||"EMERGENCY ALERT",subHeader:e.category?`${e.category.toUpperCase()} ALERT`:"",message:e.body||"",buttons:[{text:"Go to Safe Area",cssClass:"alert-button-primary",handler:()=>(console.log("\u{1F5FA}\uFE0F User tapped Go to Safe Area from modal"),this.navigateBasedOnNotification(e),!0)},{text:"Dismiss",role:"cancel",cssClass:"alert-button-secondary",handler:()=>(console.log("\u274C User dismissed notification modal"),!0)}],cssClass:`emergency-notification ${((t=e.category)==null?void 0:t.toLowerCase())||"general"}-alert`,backdropDismiss:!1,keyboardClose:!1});console.log("\u2705 Emergency modal created, presenting..."),yield i.present(),console.log("\u2705 Emergency modal presented successfully")}catch(n){console.error("\u274C Error creating emergency modal:",n),console.error("\u274C Modal error details:",n==null?void 0:n.message,n==null?void 0:n.stack),yield this.showFallbackToast(e)}})}showFallbackToast(e){return u(this,null,function*(){try{console.log("\u{1F35E} Showing emergency toast notification"),yield(yield this.toastCtrl.create({header:`\u{1F6A8} ${e.title||"EMERGENCY ALERT"}`,message:e.body||"Emergency notification received",duration:8e3,position:"top",color:this.getToastColor(e.category),cssClass:"emergency-toast",buttons:[{text:"\u{1F5FA}\uFE0F Go to Safe Area",handler:()=>(console.log("\u{1F5FA}\uFE0F User tapped Go to Safe Area from toast"),this.navigateBasedOnNotification(e),!0)},{text:"Dismiss",role:"cancel",handler:()=>(console.log("\u274C User dismissed toast notification"),!0)}]})).present(),console.log("\u2705 Emergency toast shown successfully"),this.vibrateDevice()}catch(t){console.error("\u274C Even fallback toast failed:",t);try{yield(yield this.alertCtrl.create({header:"\u{1F6A8} EMERGENCY ALERT",message:`${e.title}

${e.body}`,buttons:[{text:"Go to Safe Area",handler:()=>this.navigateBasedOnNotification(e)},"Dismiss"]})).present(),console.log("\u2705 Simple alert shown as last resort")}catch(n){console.error("\u274C All notification methods failed:",n),console.log("\u{1F4E2} EMERGENCY NOTIFICATION (all display methods failed):",e)}}})}getToastColor(e){if(!e)return"warning";switch(e.toLowerCase()){case"earthquake":return"warning";case"flood":return"primary";case"typhoon":return"success";case"fire":return"danger";default:return"warning"}}simulateForegroundNotification(e){return u(this,null,function*(){console.log("\u{1F9EA} Simulating foreground notification:",e),yield this.processNotification(e)})}simulateBackgroundNotification(e){return u(this,null,function*(){console.log("\u{1F9EA} Simulating background notification:",e),e.wasTapped=!0,yield this.processNotification(e)})}getDisasterStyle(e){if(!e)return{color:"#666666",icon:"notifications-outline"};let t=e.toLowerCase();return t.includes("earthquake")||t.includes("quake")?{color:"#ffa500",icon:"earth-outline"}:t.includes("flood")||t.includes("flash")?{color:"#0000ff",icon:"water-outline"}:t.includes("typhoon")||t.includes("storm")||t.includes("hurricane")?{color:"#008000",icon:"thunderstorm-outline"}:t.includes("fire")?{color:"#ff0000",icon:"flame-outline"}:{color:"#666666",icon:"alert-circle-outline"}}vibrateDevice(){"vibrate"in navigator?(navigator.vibrate([1e3,200,1e3,200,1e3]),console.log("Device vibration triggered with strong pattern")):console.log("Vibration API not supported on this device")}playNotificationSound(){try{let e=new Audio;e.src="data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU...",e.volume=1,e.play().catch(t=>{console.error("Error playing notification sound:",t)})}catch(e){console.error("Error creating audio element:",e)}}handleBackgroundNotification(e){return u(this,null,function*(){try{console.log("\u{1F514} Handling background notification tap:",e),this.vibrateDevice(),yield new Promise(t=>setTimeout(t,1e3)),yield this.showEmergencyNotificationModal(e)}catch(t){console.error("\u274C Error handling background notification:",t),this.navigateBasedOnNotification(e)}})}navigateBasedOnNotification(e){if(console.log("\u{1F5FA}\uFE0F Navigating based on notification:",e),e.category==="evacuation_center"||e.data&&e.data.type==="evacuation_center_added"){console.log("\u{1F3E2} New evacuation center notification detected"),this.handleEvacuationCenterNotification(e);return}if(e.category){let t=e.category.toLowerCase();console.log("\u{1F4CD} Notification category:",t);let n="";switch(t){case"flood":case"flashflood":n="Flood";break;case"earthquake":case"quake":n="Earthquake";break;case"typhoon":case"storm":case"hurricane":n="Typhoon";break;case"fire":n="Fire";break;default:console.warn("Unknown disaster category:",t),n="all";break}if(console.log(`\u{1F5FA}\uFE0F Mapped disaster type: ${t} -> ${n}`),n&&n!=="all"){console.log("\u{1F5FA}\uFE0F Navigating to disaster-specific map:",n);let i="";switch(n.toLowerCase()){case"earthquake":i="/earthquake-map";break;case"typhoon":i="/typhoon-map";break;case"flood":i="/flood-map";break;default:i="/tabs/map";break}console.log(`\u{1F5FA}\uFE0F Navigating to route: ${i}`),this.router.navigate([i])}else console.log("\u{1F3E0} Navigating to home (unknown disaster type)"),this.router.navigate(["/tabs/home"])}else console.log("\u{1F3E0} Navigating to home (no category)"),this.router.navigate(["/tabs/home"])}handleEvacuationCenterNotification(e){var t,n;console.log("\u{1F3E2} Handling evacuation center notification:",e);try{let i=null;if(e.data&&(i=e.data),i&&i.evacuation_center_id){let s=i.evacuation_center_id,c=i.disaster_type,l=(t=i.location)==null?void 0:t.latitude,f=(n=i.location)==null?void 0:n.longitude;console.log("\u{1F3E2} Evacuation center details:",{id:s,disasterType:c,lat:l,lng:f});let m="/all-maps";if(c)switch(c.toLowerCase()){case"earthquake":m="/earthquake-map";break;case"typhoon":m="/typhoon-map";break;case"flood":case"flash flood":m="/flood-map";break;default:m="/all-maps";break}console.log(`\u{1F5FA}\uFE0F Navigating to ${m} for new evacuation center`),this.router.navigate([m],{queryParams:{newCenterId:s,highlightCenter:"true",centerLat:l,centerLng:f}})}else console.log("\u{1F3E2} No evacuation center data found, navigating to all maps"),this.router.navigate(["/all-maps"])}catch(i){console.error("\u274C Error handling evacuation center notification:",i),this.router.navigate(["/all-maps"])}}checkGooglePlayServices(){return u(this,null,function*(){try{if(this.platform.is("capacitor")&&this.platform.is("android"))try{return yield g.getToken(),!0}catch(e){console.error("Error checking Google Play Services:",e);let t=e.message||"";return!(t.includes("Google Play Services")||t.includes("GoogleApiAvailability")||t.includes("API unavailable"))}return!0}catch(e){return console.error("Error in checkGooglePlayServices:",e),!0}})}createAndroidNotificationChannels(){return u(this,null,function*(){try{this.platform.is("android")&&(console.log("Creating Android notification channels"),yield this.sendTestChannelNotification("emergency-alerts","Emergency Alerts","High priority notifications for emergencies","high"),yield this.sendTestChannelNotification("general-notifications","General Notifications","Standard notifications","default"),console.log("Android notification channels created successfully"))}catch(e){console.error("Error creating Android notification channels:",e)}})}sendTestChannelNotification(e,t,n,i){return u(this,null,function*(){try{let s={notification:{title:"Channel Setup",body:"Setting up notification channels",android:{channelId:e,priority:i==="high"?"high":i==="default"?"default":"low",sound:i!=="low",vibrate:i!=="low",visibility:"public"}}};console.log(`Created notification channel: ${e} (${t})`)}catch(s){console.error(`Error creating notification channel ${e}:`,s)}})}refreshFCMToken(e){return u(this,null,function*(){try{if(console.log("Refreshing FCM token..."),this.platform.is("capacitor"))try{yield g.deleteToken(),console.log("Existing FCM token deleted");let t=yield g.getToken();return console.log("New FCM token obtained:",t.token),this.registerTokenWithBackend(t.token,e),!0}catch(t){return console.error("Error refreshing Capacitor FCM token:",t),!1}else if(this.platform.is("cordova"))try{let t=yield this.fcm.getToken();return console.log("New FCM token obtained from Cordova:",t),this.registerTokenWithBackend(t,e),!0}catch(t){return console.error("Error refreshing Cordova FCM token:",t),!1}else{let t="browser-mock-token-"+Math.random().toString(36).substring(2,15);return console.log("New mock FCM token generated:",t),this.registerTokenWithBackend(t,e),!0}}catch(t){return console.error("Error in refreshFCMToken:",t),!1}})}};r.\u0275fac=function(t){return new(t||r)(p(U),p(E),p(R),p(G),p(D),p(I))},r.\u0275prov=k({token:r,factory:r.\u0275fac,providedIn:"root"});let o=r;return o})();export{U as a,Be as b};
