import {
  Injectable
} from "./chunk-BBINBVM2.js";
import "./chunk-XYJ3Z5FP.js";
import {
  fromEvent
} from "./chunk-NZN5AKWE.js";
import {
  Observable
} from "./chunk-HEDKW4S6.js";
import {
  __extends
} from "./chunk-7IZRYL2Z.js";
import "./chunk-HLNXGIF7.js";

// node_modules/@ionic-native/core/bootstrap.js
function checkReady() {
  if (typeof process === "undefined") {
    var win_1 = typeof window !== "undefined" ? window : {};
    var DEVICE_READY_TIMEOUT_1 = 5e3;
    var before_1 = Date.now();
    var didFireReady_1 = false;
    win_1.document.addEventListener("deviceready", function() {
      console.log("Ionic Native: deviceready event fired after " + (Date.now() - before_1) + " ms");
      didFireReady_1 = true;
    });
    setTimeout(function() {
      if (!didFireReady_1 && win_1.cordova) {
        console.warn("Ionic Native: deviceready did not fire within " + DEVICE_READY_TIMEOUT_1 + "ms. This can happen when plugins are in an inconsistent state. Try removing plugins from plugins/ and reinstalling them.");
      }
    }, DEVICE_READY_TIMEOUT_1);
  }
}

// node_modules/@ionic-native/core/decorators/common.js
var ERR_CORDOVA_NOT_AVAILABLE = {
  error: "cordova_not_available"
};
var ERR_PLUGIN_NOT_INSTALLED = {
  error: "plugin_not_installed"
};
function getPromise(callback) {
  var tryNativePromise = function() {
    if (Promise) {
      return new Promise(function(resolve, reject) {
        callback(resolve, reject);
      });
    } else {
      console.error("No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.");
    }
  };
  if (typeof window !== "undefined" && window.angular) {
    var doc = window.document;
    var injector = window.angular.element(doc.querySelector("[ng-app]") || doc.body).injector();
    if (injector) {
      var $q = injector.get("$q");
      return $q(function(resolve, reject) {
        callback(resolve, reject);
      });
    }
    console.warn("Angular 1 was detected but $q couldn't be retrieved. This is usually when the app is not bootstrapped on the html or body tag. Falling back to native promises which won't trigger an automatic digest when promises resolve.");
  }
  return tryNativePromise();
}
function wrapPromise(pluginObj, methodName, args, opts) {
  if (opts === void 0) {
    opts = {};
  }
  var pluginResult, rej;
  var p = getPromise(function(resolve, reject) {
    if (opts.destruct) {
      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, function() {
        var args2 = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args2[_i] = arguments[_i];
        }
        return resolve(args2);
      }, function() {
        var args2 = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args2[_i] = arguments[_i];
        }
        return reject(args2);
      });
    } else {
      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, resolve, reject);
    }
    rej = reject;
  });
  if (pluginResult && pluginResult.error) {
    p.catch(function() {
    });
    typeof rej === "function" && rej(pluginResult.error);
  }
  return p;
}
function wrapOtherPromise(pluginObj, methodName, args, opts) {
  if (opts === void 0) {
    opts = {};
  }
  return getPromise(function(resolve, reject) {
    var pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts);
    if (pluginResult) {
      if (pluginResult.error) {
        reject(pluginResult.error);
      } else if (pluginResult.then) {
        pluginResult.then(resolve).catch(reject);
      }
    } else {
      reject({
        error: "unexpected_error"
      });
    }
  });
}
function wrapObservable(pluginObj, methodName, args, opts) {
  if (opts === void 0) {
    opts = {};
  }
  return new Observable(function(observer) {
    var pluginResult;
    if (opts.destruct) {
      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, function() {
        var args2 = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args2[_i] = arguments[_i];
        }
        return observer.next(args2);
      }, function() {
        var args2 = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args2[_i] = arguments[_i];
        }
        return observer.error(args2);
      });
    } else {
      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, observer.next.bind(observer), observer.error.bind(observer));
    }
    if (pluginResult && pluginResult.error) {
      observer.error(pluginResult.error);
      observer.complete();
    }
    return function() {
      try {
        if (opts.clearFunction) {
          if (opts.clearWithArgs) {
            return callCordovaPlugin(pluginObj, opts.clearFunction, args, opts, observer.next.bind(observer), observer.error.bind(observer));
          }
          return callCordovaPlugin(pluginObj, opts.clearFunction, []);
        }
      } catch (e) {
        console.warn("Unable to clear the previous observable watch for", pluginObj.constructor.getPluginName(), methodName);
        console.warn(e);
      }
    };
  });
}
function wrapEventObservable(event, element) {
  element = typeof window !== "undefined" && element ? get(window, element) : element || (typeof window !== "undefined" ? window : {});
  return fromEvent(element, event);
}
function checkAvailability(plugin, methodName, pluginName) {
  var pluginRef, pluginInstance, pluginPackage;
  if (typeof plugin === "string") {
    pluginRef = plugin;
  } else {
    pluginRef = plugin.constructor.getPluginRef();
    pluginName = plugin.constructor.getPluginName();
    pluginPackage = plugin.constructor.getPluginInstallName();
  }
  pluginInstance = getPlugin(pluginRef);
  if (!pluginInstance || !!methodName && typeof pluginInstance[methodName] === "undefined") {
    if (typeof window === "undefined" || !window.cordova) {
      cordovaWarn(pluginName, methodName);
      return ERR_CORDOVA_NOT_AVAILABLE;
    }
    pluginWarn(pluginName, pluginPackage, methodName);
    return ERR_PLUGIN_NOT_INSTALLED;
  }
  return true;
}
function setIndex(args, opts, resolve, reject) {
  if (opts === void 0) {
    opts = {};
  }
  if (opts.sync) {
    return args;
  }
  if (opts.callbackOrder === "reverse") {
    args.unshift(reject);
    args.unshift(resolve);
  } else if (opts.callbackStyle === "node") {
    args.push(function(err, result) {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
  } else if (opts.callbackStyle === "object" && opts.successName && opts.errorName) {
    var obj = {};
    obj[opts.successName] = resolve;
    obj[opts.errorName] = reject;
    args.push(obj);
  } else if (typeof opts.successIndex !== "undefined" || typeof opts.errorIndex !== "undefined") {
    var setSuccessIndex = function() {
      if (opts.successIndex > args.length) {
        args[opts.successIndex] = resolve;
      } else {
        args.splice(opts.successIndex, 0, resolve);
      }
    };
    var setErrorIndex = function() {
      if (opts.errorIndex > args.length) {
        args[opts.errorIndex] = reject;
      } else {
        args.splice(opts.errorIndex, 0, reject);
      }
    };
    if (opts.successIndex > opts.errorIndex) {
      setErrorIndex();
      setSuccessIndex();
    } else {
      setSuccessIndex();
      setErrorIndex();
    }
  } else {
    args.push(resolve);
    args.push(reject);
  }
  return args;
}
function callCordovaPlugin(pluginObj, methodName, args, opts, resolve, reject) {
  if (opts === void 0) {
    opts = {};
  }
  args = setIndex(args, opts, resolve, reject);
  var availabilityCheck = checkAvailability(pluginObj, methodName);
  if (availabilityCheck === true) {
    var pluginInstance = getPlugin(pluginObj.constructor.getPluginRef());
    return pluginInstance[methodName].apply(pluginInstance, args);
  } else {
    return availabilityCheck;
  }
}
function getPlugin(pluginRef) {
  if (typeof window !== "undefined") {
    return get(window, pluginRef);
  }
  return null;
}
function get(element, path) {
  var paths = path.split(".");
  var obj = element;
  for (var i = 0; i < paths.length; i++) {
    if (!obj) {
      return null;
    }
    obj = obj[paths[i]];
  }
  return obj;
}
function pluginWarn(pluginName, plugin, method) {
  if (method) {
    console.warn("Native: tried calling " + pluginName + "." + method + ", but the " + pluginName + " plugin is not installed.");
  } else {
    console.warn("Native: tried accessing the " + pluginName + " plugin but it's not installed.");
  }
  if (plugin) {
    console.warn("Install the " + pluginName + " plugin: 'ionic cordova plugin add " + plugin + "'");
  }
}
function cordovaWarn(pluginName, method) {
  if (typeof process === "undefined") {
    if (method) {
      console.warn("Native: tried calling " + pluginName + "." + method + ", but Cordova is not available. Make sure to include cordova.js or run in a device/simulator");
    } else {
      console.warn("Native: tried accessing the " + pluginName + " plugin but Cordova is not available. Make sure to include cordova.js or run in a device/simulator");
    }
  }
}
var wrap = function(pluginObj, methodName, opts) {
  if (opts === void 0) {
    opts = {};
  }
  return function() {
    var args = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }
    if (opts.sync) {
      return callCordovaPlugin(pluginObj, methodName, args, opts);
    } else if (opts.observable) {
      return wrapObservable(pluginObj, methodName, args, opts);
    } else if (opts.eventObservable && opts.event) {
      return wrapEventObservable(opts.event, opts.element);
    } else if (opts.otherPromise) {
      return wrapOtherPromise(pluginObj, methodName, args, opts);
    } else {
      return wrapPromise(pluginObj, methodName, args, opts);
    }
  };
};

// node_modules/@ionic-native/core/util.js
function get2(element, path) {
  var paths = path.split(".");
  var obj = element;
  for (var i = 0; i < paths.length; i++) {
    if (!obj) {
      return null;
    }
    obj = obj[paths[i]];
  }
  return obj;
}

// node_modules/@ionic-native/core/ionic-native-plugin.js
var IonicNativePlugin = (
  /** @class */
  function() {
    function IonicNativePlugin2() {
    }
    IonicNativePlugin2.installed = function() {
      var isAvailable = checkAvailability(this.pluginRef) === true;
      return isAvailable;
    };
    IonicNativePlugin2.getPlugin = function() {
      if (typeof window !== "undefined") {
        return get2(window, this.pluginRef);
      }
      return null;
    };
    IonicNativePlugin2.getPluginName = function() {
      var pluginName = this.pluginName;
      return pluginName;
    };
    IonicNativePlugin2.getPluginRef = function() {
      var pluginRef = this.pluginRef;
      return pluginRef;
    };
    IonicNativePlugin2.getPluginInstallName = function() {
      var plugin = this.plugin;
      return plugin;
    };
    IonicNativePlugin2.getSupportedPlatforms = function() {
      var platform = this.platforms;
      return platform;
    };
    IonicNativePlugin2.pluginName = "";
    IonicNativePlugin2.pluginRef = "";
    IonicNativePlugin2.plugin = "";
    IonicNativePlugin2.repo = "";
    IonicNativePlugin2.platforms = [];
    IonicNativePlugin2.install = "";
    return IonicNativePlugin2;
  }()
);

// node_modules/@ionic-native/core/decorators/cordova.js
function cordova(pluginObj, methodName, config, args) {
  return wrap(pluginObj, methodName, config).apply(this, args);
}

// node_modules/@ionic-native/core/index.js
checkReady();

// node_modules/@ionic-native/background-mode/ngx/index.js
var BackgroundMode = (
  /** @class */
  function(_super) {
    __extends(BackgroundMode2, _super);
    function BackgroundMode2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    BackgroundMode2.prototype.enable = function() {
      return cordova(this, "enable", {
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.disable = function() {
      return cordova(this, "disable", {
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.setEnabled = function(enable) {
      return cordova(this, "setEnabled", {
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.fireEvent = function(event) {
      var args = [];
      for (var _i = 1; _i < arguments.length; _i++) {
        args[_i - 1] = arguments[_i];
      }
      return cordova(this, "fireEvent", {
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.isEnabled = function() {
      return cordova(this, "isEnabled", {
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.isActive = function() {
      return cordova(this, "isActive", {
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.setDefaults = function(overrides) {
      return cordova(this, "setDefaults", {
        "platforms": ["Android"]
      }, arguments);
    };
    BackgroundMode2.prototype.configure = function(options) {
      return cordova(this, "configure", {
        "platforms": ["Android"],
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.on = function(event) {
      return cordova(this, "on", {
        "observable": true,
        "clearFunction": "un",
        "clearWithArgs": true
      }, arguments);
    };
    BackgroundMode2.prototype.un = function(event, callback) {
      return cordova(this, "un", {}, arguments);
    };
    BackgroundMode2.prototype.moveToBackground = function() {
      return cordova(this, "moveToBackground", {
        "platforms": ["Android"],
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.disableWebViewOptimizations = function() {
      return cordova(this, "disableWebViewOptimizations", {
        "platforms": ["Android"],
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.moveToForeground = function() {
      return cordova(this, "moveToForeground", {
        "platforms": ["Android"],
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.overrideBackButton = function() {
      return cordova(this, "overrideBackButton", {
        "platforms": ["Android"],
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.excludeFromTaskList = function() {
      return cordova(this, "excludeFromTaskList", {
        "platforms": ["Android"],
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.isScreenOff = function(fn) {
      return cordova(this, "isScreenOff", {
        "platforms": ["Android"]
      }, arguments);
    };
    BackgroundMode2.prototype.wakeUp = function() {
      return cordova(this, "wakeUp", {
        "platforms": ["Android"],
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.unlock = function() {
      return cordova(this, "unlock", {
        "platforms": ["Android"],
        "sync": true
      }, arguments);
    };
    BackgroundMode2.prototype.disableBatteryOptimizations = function() {
      return cordova(this, "disableBatteryOptimizations", {
        "platforms": ["Android"],
        "sync": true
      }, arguments);
    };
    BackgroundMode2.pluginName = "BackgroundMode";
    BackgroundMode2.plugin = "cordova-plugin-background-mode";
    BackgroundMode2.pluginRef = "cordova.plugins.backgroundMode";
    BackgroundMode2.repo = "https://github.com/katzer/cordova-plugin-background-mode";
    BackgroundMode2.platforms = ["AmazonFire OS", "Android", "Browser", "iOS", "Windows"];
    BackgroundMode2.decorators = [{
      type: Injectable
    }];
    return BackgroundMode2;
  }(IonicNativePlugin)
);
export {
  BackgroundMode
};
//# sourceMappingURL=@ionic-native_background-mode_ngx.js.map
