import{a as ae}from"./chunk-5QR2BYRY.js";import{a as re}from"./chunk-FULEFYAM.js";import{$ as N,$a as U,A as P,Bb as E,C,Cb as T,D as u,Db as k,F as n,G as r,H as m,I as O,J as h,Ja as $,K as _,L as b,M as l,Ma as G,N as f,Na as I,O as z,Q as V,R as F,S as W,V as D,W as R,Wa as H,X as S,ab as w,cb as J,ea as y,fb as K,gb as X,ha as A,jb as Y,ka as q,kb as Z,lb as ee,oa as B,q as v,qb as te,r as M,tb as ne,ub as ie,vb as oe,y as c,z as p}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as L}from"./chunk-LNJ3S2LQ.js";var ce=(()=>{let a=class a{constructor(i,e,t){this.modalCtrl=i,this.router=e,this.toastCtrl=t}dismiss(){this.modalCtrl.dismiss()}viewOnMap(){this.modalCtrl.dismiss(),this.router.navigate(["/tabs/map"],{queryParams:{lat:this.center.latitude,lng:this.center.longitude,name:this.center.name,viewOnly:"true"}}),this.toastCtrl.create({message:`Showing ${this.center.name} on map`,duration:2e3,color:"success"}).then(i=>i.present())}getDirections(){this.modalCtrl.dismiss(),this.router.navigate(["/tabs/map"],{queryParams:{lat:this.center.latitude,lng:this.center.longitude,name:this.center.name,directions:"true"}}),this.toastCtrl.create({message:`Getting directions to ${this.center.name}`,duration:2e3,color:"success"}).then(i=>i.present())}getDisasterTypeIcon(i){if(!i)return"alert-circle-outline";let e=i.toLowerCase();return e.includes("earthquake")||e.includes("quake")?"earth-outline":e.includes("flood")||e.includes("flash")?"water-outline":e.includes("typhoon")||e.includes("storm")?"thunderstorm-outline":e.includes("fire")?"flame-outline":"alert-circle-outline"}getStatusColor(i){if(!i)return"medium";let e=i.toLowerCase();return e.includes("active")||e.includes("open")?"success":e.includes("inactive")||e.includes("closed")?"warning":e.includes("full")?"danger":"medium"}};a.\u0275fac=function(e){return new(e||a)(p(E),p(y),p(T))},a.\u0275cmp=P({type:a,selectors:[["app-evacuation-center-modal"]],inputs:{center:"center"},decls:25,vars:4,consts:[[1,"modal-container"],[1,"close-button",3,"click"],["name","close-circle","color","danger"],[1,"center-name"],[1,"center-image"],["src","assets/evacuation-center.jpg","onerror","this.src='assets/evacuation-placeholder.jpg'",3,"alt"],[1,"info-section"],[1,"info-label"],[1,"info-value","contact"],["name","call-outline"],[1,"info-value","address"],["name","location-outline"],[1,"directions-button"],["expand","block","color","primary",3,"click"],["name","navigate","slot","start"]],template:function(e,t){e&1&&(n(0,"div",0)(1,"div",1),h("click",function(){return t.dismiss()}),m(2,"ion-icon",2),r(),n(3,"h2",3),l(4),r(),n(5,"div",4),m(6,"img",5),r(),n(7,"div",6)(8,"div",7),l(9,"Contact Number"),r(),n(10,"div",8),m(11,"ion-icon",9),n(12,"span"),l(13),r()()(),n(14,"div",6)(15,"div",7),l(16,"Address"),r(),n(17,"div",10),m(18,"ion-icon",11),n(19,"span"),l(20),r()()(),n(21,"div",12)(22,"ion-button",13),h("click",function(){return t.getDirections()}),m(23,"ion-icon",14),l(24," Get Directions "),r()()()),e&2&&(c(4),f(t.center.name),c(2),b("alt",t.center.name),c(7),f(t.center.contact||"No contact available"),c(7),f(t.center.address))},dependencies:[k,I,w,S],styles:[".modal-container[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;overflow:hidden;width:100%;max-width:350px;margin:0 auto;position:relative;box-shadow:0 4px 12px #0000001a}.close-button[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px;z-index:10}.close-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;background:#fff;border-radius:50%}.center-name[_ngcontent-%COMP%]{font-size:18px;font-weight:600;text-align:center;margin:15px 15px 10px;color:#000}.center-image[_ngcontent-%COMP%]{width:100%;height:160px;overflow:hidden}.center-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.info-section[_ngcontent-%COMP%]{padding:10px 15px;border-bottom:1px solid #f0f0f0}.info-section[_ngcontent-%COMP%]:last-of-type{border-bottom:none}.info-label[_ngcontent-%COMP%]{font-size:14px;color:#09f;margin-bottom:5px}.info-value[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:15px;color:#333}.info-value[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px;font-size:18px;min-width:18px}.info-value.contact[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#333}.info-value.address[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ff4961}.directions-button[_ngcontent-%COMP%]{padding:10px 15px 15px}.directions-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 8px;--background: #0099ff;font-weight:500;margin:0}.directions-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:5px}"]});let o=a;return o})();function ge(o,a){o&1&&(n(0,"ion-text",11)(1,"p"),l(2,"Search by center name, address, or disaster type"),r()())}function pe(o,a){o&1&&(n(0,"div",12),m(1,"ion-spinner",13),n(2,"ion-text",14)(3,"p"),l(4,"Loading evacuation centers..."),r()()())}function ue(o,a){if(o&1){let s=O();n(0,"div",15),m(1,"ion-icon",16),n(2,"ion-text",17)(3,"p"),l(4),r()(),n(5,"ion-button",18),h("click",function(){v(s);let e=_();return M(e.loadEvacuationCenters())}),m(6,"ion-icon",19),l(7," Try Again "),r()()}if(o&2){let s=_();c(4),f(s.errorMessage)}}function he(o,a){if(o&1&&(n(0,"ion-badge",25),l(1),r()),o&2){let s=_(2).$implicit;b("color",s.status==="Active"?"success":"warning"),c(),z(" ",s.status," ")}}function _e(o,a){if(o&1&&(n(0,"p")(1,"ion-badge",23),l(2),r(),C(3,he,2,2,"ion-badge",24),r()),o&2){let s=_().$implicit;c(2),f(s.disaster_type),c(),u("ngIf",s.status)}}function fe(o,a){if(o&1){let s=O();n(0,"ion-item",21),h("click",function(){let e=v(s).$implicit,t=_(2);return M(t.viewOnMap(e))}),m(1,"ion-icon",22),n(2,"ion-label")(3,"h2"),l(4),r(),n(5,"p"),l(6),r(),C(7,_e,4,2,"p",8),r()()}if(o&2){let s=a.$implicit;c(4),f(s.name),c(2),f(s.address),c(),u("ngIf",s.disaster_type)}}function Ce(o,a){if(o&1&&(n(0,"ion-list"),C(1,fe,8,3,"ion-item",20),r()),o&2){let s=_();c(),u("ngForOf",s.locations)}}function xe(o,a){if(o&1&&(n(0,"div",26),m(1,"ion-icon",27),n(2,"ion-text",14)(3,"p"),l(4),r()()()),o&2){let s=_();c(4),z('No evacuation centers found matching "',s.searchQuery,'"')}}function ve(o,a){if(o&1){let s=O();n(0,"div",28),m(1,"ion-icon",29),n(2,"ion-text",14)(3,"h3"),l(4,"Search for Evacuation Centers"),r(),n(5,"p"),l(6,"Enter a name, address, or disaster type to find evacuation centers"),r()(),n(7,"ion-button",30),h("click",function(){v(s);let e=_();return e.searchQuery="all",M(e.onSearch({target:{value:"all"}}))}),l(8," Show All Centers "),r()()}}var ze=(()=>{let a=class a{constructor(i,e,t,g,d){this.http=i,this.loadingService=e,this.toastCtrl=t,this.router=g,this.modalCtrl=d,this.searchQuery="",this.locations=[],this.allCenters=[],this.isLoading=!1,this.hasError=!1,this.errorMessage=""}ngOnInit(){this.loadEvacuationCenters()}loadEvacuationCenters(){return L(this,null,function*(){yield this.loadingService.showLoading("Loading evacuation centers..."),this.isLoading=!0;try{this.http.get(`${re.apiUrl}/evacuation-centers`).subscribe({next:i=>{console.log("Loaded evacuation centers:",i),this.allCenters=i||[],this.isLoading=!1,this.loadingService.dismissLoading()},error:i=>{console.error("Error loading evacuation centers:",i),this.hasError=!0,this.errorMessage="Failed to load evacuation centers. Please try again later.",this.isLoading=!1,this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load evacuation centers. Please try again later.",duration:3e3,color:"danger"}).then(e=>e.present())}})}catch(i){console.error("Exception loading evacuation centers:",i),this.hasError=!0,this.errorMessage="An unexpected error occurred. Please try again later.",this.isLoading=!1,this.loadingService.dismissLoading()}})}onSearch(i){let e=i.target.value.toLowerCase().trim();if(console.log("Searching for:",e),!e){this.locations=[];return}this.locations=this.allCenters.filter(t=>{var j,Q;let g=t.name.toLowerCase().startsWith(e),d=t.name.toLowerCase().includes(e),x=(j=t.address)==null?void 0:j.toLowerCase().includes(e),se=(Q=t.disaster_type)==null?void 0:Q.toLowerCase().includes(e);return g||d||x||se}),this.locations.sort((t,g)=>{let d=t.name.toLowerCase().startsWith(e),x=g.name.toLowerCase().startsWith(e);return d&&!x?-1:!d&&x?1:0})}clearSearch(){this.searchQuery="",this.locations=[]}refreshCenters(i){this.loadEvacuationCenters().then(()=>{i.target.complete()})}viewOnMap(i){return L(this,null,function*(){yield(yield this.modalCtrl.create({component:ce,componentProps:{center:i},cssClass:"evacuation-center-modal"})).present()})}};a.\u0275fac=function(e){return new(e||a)(p(N),p(ae),p(T),p(y),p(E))},a.\u0275cmp=P({type:a,selectors:[["app-search"]],decls:16,vars:7,consts:[["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","pullingText","Pull to refresh","refreshingSpinner","circles","refreshingText","Refreshing..."],[1,"search-container"],["placeholder","Search evacuation centers by name","animated","true","showCancelButton","focus","debounce","300",3,"ngModelChange","ionInput","ionClear","ngModel"],["color","medium","class","search-hint",4,"ngIf"],[1,"search-results"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],[4,"ngIf"],["class","no-results",4,"ngIf"],["class","empty-state",4,"ngIf"],["color","medium",1,"search-hint"],[1,"loading-container"],["name","circles"],["color","medium"],[1,"error-container"],["name","alert-circle-outline","color","danger","size","large"],["color","danger"],["fill","outline","size","small",3,"click"],["name","refresh-outline","slot","start"],["button","","detail","",3,"click",4,"ngFor","ngForOf"],["button","","detail","",3,"click"],["name","location-outline","slot","start","color","primary"],["color","secondary"],[3,"color",4,"ngIf"],[3,"color"],[1,"no-results"],["name","search-outline","color","medium","size","large"],[1,"empty-state"],["name","search","color","primary","size","large"],["fill","outline",3,"click"]],template:function(e,t){e&1&&(n(0,"ion-header")(1,"ion-toolbar")(2,"ion-title"),l(3,"Search Evacuation Centers"),r()()(),n(4,"ion-content")(5,"ion-refresher",0),h("ionRefresh",function(d){return t.refreshCenters(d)}),m(6,"ion-refresher-content",1),r(),n(7,"div",2)(8,"ion-searchbar",3),W("ngModelChange",function(d){return F(t.searchQuery,d)||(t.searchQuery=d),d}),h("ionInput",function(d){return t.onSearch(d)})("ionClear",function(){return t.clearSearch()}),r(),C(9,ge,3,0,"ion-text",4),r(),n(10,"div",5),C(11,pe,5,0,"div",6)(12,ue,8,1,"div",7)(13,Ce,2,1,"ion-list",8)(14,xe,5,1,"div",9)(15,ve,9,0,"div",10),r()()),e&2&&(c(8),V("ngModel",t.searchQuery),c(),u("ngIf",!t.searchQuery),c(2),u("ngIf",t.isLoading),c(),u("ngIf",t.hasError),c(),u("ngIf",t.locations.length>0),c(),u("ngIf",t.searchQuery&&t.locations.length===0&&!t.isLoading&&!t.hasError),c(),u("ngIf",!t.searchQuery&&!t.isLoading&&!t.hasError&&t.allCenters.length>0))},dependencies:[k,G,I,H,U,w,J,K,X,Y,Z,ee,te,ne,ie,oe,$,S,D,R,B,A,q],styles:["ion-content[_ngcontent-%COMP%]{--background: #f8f9fa}.search-container[_ngcontent-%COMP%]{padding:10px 16px 0}.search-container[_ngcontent-%COMP%]   ion-searchbar[_ngcontent-%COMP%]{--border-radius: 10px;--box-shadow: 0 2px 6px rgba(0, 0, 0, .1);--placeholder-color: var(--ion-color-medium);--icon-color: var(--ion-color-primary)}.search-container[_ngcontent-%COMP%]   .search-hint[_ngcontent-%COMP%]{font-size:12px;margin:0 0 10px 16px;display:block}.search-results[_ngcontent-%COMP%]{padding:0 16px 16px}.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;padding:32px 16px;min-height:200px}.loading-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:48px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;font-size:16px}.loading-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:16px}ion-list[_ngcontent-%COMP%]{background:transparent;padding:0}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px;--background: white;margin-bottom:10px;border-radius:10px;--border-radius: 10px;box-shadow:0 2px 4px #0000000d}ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px;font-weight:500;margin-bottom:4px;color:var(--ion-color-dark)}ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:var(--ion-color-medium);margin:2px 0}ion-item[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%]{margin-right:6px;padding:4px 8px;border-radius:4px}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:500;margin:8px 0;color:var(--ion-color-dark)}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:20px}"]});let o=a;return o})();export{ze as SearchPage};
