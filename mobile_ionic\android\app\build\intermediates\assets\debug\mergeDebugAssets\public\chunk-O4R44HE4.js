import{a as j,b as G,c as K}from"./chunk-AC42TJRX.js";import{a as V}from"./chunk-KGX7B5OW.js";import{a as y}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$ as k,$a as H,A as b,Ab as z,Cb as B,D as P,Db as U,F as g,G as m,H as C,J as x,M as w,Na as $,O as T,Oa as I,Pa as _,Qa as E,Wa as S,X as N,ab as R,ca as L,ea as A,g as v,p as l,ub as Y,vb as D,y as M,zb as F}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{f as J,h as c}from"./chunk-LNJ3S2LQ.js";var r=J(K());var lt=(()=>{let d=class d{constructor(){this.userMarker=null,this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.loadingCtrl=l(z),this.toastCtrl=l(B),this.alertCtrl=l(F),this.http=l(k),this.router=l(A),this.route=l(L),this.mapboxRouting=l(G),this.offlineStorage=l(V)}ngOnInit(){console.log("\u{1F7E2} TYPHOON MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F7E2} TYPHOON MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return c(this,null,function*(){console.log("\u{1F7E2} TYPHOON MAP: View initialized, loading map..."),setTimeout(()=>c(this,null,function*(){yield this.loadTyphoonMap()}),100)})}loadTyphoonMap(){return c(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading typhoon evacuation centers...",spinner:"crescent"});yield t.present();try{let n=yield j.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),e=n.coords.latitude,a=n.coords.longitude;console.log(`\u{1F7E2} TYPHOON MAP: User location [${e}, ${a}]`),this.userLocation={lat:e,lng:a},this.initializeMap(e,a),yield this.loadTyphoonCenters(e,a),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F7E2} Showing ${this.evacuationCenters.length} typhoon evacuation centers`,duration:3e3,color:"success",position:"top"})).present()}catch(n){yield t.dismiss(),console.error("\u{1F7E2} TYPHOON MAP: Error loading map",n),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadTyphoonMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,n){if(console.log(`\u{1F7E2} TYPHOON MAP: Initializing map at [${t}, ${n}]`),!document.getElementById("typhoon-map"))throw console.error("\u{1F7E2} TYPHOON MAP: Container #typhoon-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=r.map("typhoon-map").setView([t,n],13),r.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=r.marker([t,n],{icon:r.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadTyphoonCenters(t,n){return c(this,null,function*(){try{console.log("\u{1F7E2} TYPHOON MAP: Fetching typhoon centers...");let e=[];if(this.offlineStorage.isOfflineMode()||!navigator.onLine){if(console.log("\u{1F504} Loading typhoon centers from offline storage"),e=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} OFFLINE DATA:",e),e.length===0){console.warn("\u26A0\uFE0F No cached evacuation centers found"),yield(yield this.alertCtrl.create({header:"No Offline Data",message:"No offline evacuation data available. Please sync data when online.",buttons:["OK"]})).present();return}}else try{e=yield v(this.http.get(`${y.apiUrl}/evacuation-centers`)),console.log("\u{1F7E2} TYPHOON MAP: Total centers received from API:",(e==null?void 0:e.length)||0)}catch(o){if(console.error("\u274C API failed, falling back to offline data:",o),e=yield this.offlineStorage.getEvacuationCenters(),e.length===0){yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server and no offline data available. Please check your connection or sync data when online.",buttons:["OK"]})).present();return}}if(this.evacuationCenters=e.filter(o=>o.disaster_type==="Typhoon"),console.log(`\u{1F7E2} TYPHOON MAP: Filtered to ${this.evacuationCenters.length} typhoon centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Typhoon Centers",message:"No typhoon evacuation centers found in the database.",buttons:["OK"]})).present();return}let a=this.offlineStorage.isOfflineMode()||!navigator.onLine;if(this.evacuationCenters.forEach(o=>{let i=Number(o.latitude),s=Number(o.longitude);if(!isNaN(i)&&!isNaN(s)){let p=r.marker([i,s],{icon:r.icon({iconUrl:"assets/forTyphoon.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),u=this.calculateDistance(t,n,i,s);p.on("click",()=>{a?this.showOfflineMarkerInfo(o,u):this.showTransportationOptions(o)});let h=this.newCenterId&&o.id.toString()===this.newCenterId,f=a?"<p><em>\u{1F4F1} Offline Mode - Limited functionality</em></p>":"<p><em>Click marker for route options</em></p>";p.bindPopup(`
            <div class="evacuation-popup">
              <h3>\u{1F7E2} ${o.name} ${h?"\u2B50 NEW!":""}</h3>
              <p><strong>Type:</strong> Typhoon Center</p>
              <p><strong>Distance:</strong> ${(u/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${o.capacity||"N/A"}</p>
              ${f}
              ${h?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
            </div>
          `),h&&(p.openPopup(),this.map.setView([i,s],15),this.toastCtrl.create({message:`\u{1F195} New typhoon evacuation center: ${o.name}`,duration:5e3,color:"success",position:"top"}).then(q=>q.present())),p.addTo(this.map),console.log(`\u{1F7E2} Added typhoon marker: ${o.name}`)}}),a?console.log("\u{1F7E2} Offline mode: Showing markers only (no routing)"):(console.log("\u{1F7E2} Online mode: Auto-routing to 2 nearest typhoon centers..."),yield this.routeToTwoNearestCenters()),this.evacuationCenters.length>0){let o=r.latLngBounds([]);o.extend([t,n]),this.evacuationCenters.forEach(i=>{o.extend([Number(i.latitude),Number(i.longitude)])}),this.map.fitBounds(o,{padding:[50,50]})}}catch(e){console.error("\u{1F7E2} TYPHOON MAP: Error loading centers",e),yield(yield this.toastCtrl.create({message:"Error loading typhoon centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}routeToTwoNearestCenters(){return c(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F7E2} TYPHOON MAP: No user location or evacuation centers available");return}try{console.log("\u{1F7E2} TYPHOON MAP: Finding 2 nearest typhoon centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0)return;this.clearRoutes(),yield this.calculateRoutes(t)}catch(t){console.error("\u{1F7E2} TYPHOON MAP: Error calculating routes",t)}})}getTwoNearestCenters(t,n){return[...this.evacuationCenters].sort((a,o)=>{let i=this.calculateDistance(t,n,Number(a.latitude),Number(a.longitude)),s=this.calculateDistance(t,n,Number(o.latitude),Number(o.longitude));return i-s}).slice(0,2)}clearRoutes(){this.map.eachLayer(t=>{t instanceof r.GeoJSON&&this.map.removeLayer(t)})}calculateRoutes(t){return c(this,null,function*(){for(let n of t)yield this.calculateRoute(n,"walking")})}calculateRoute(t,n){return c(this,null,function*(){try{if(!this.userLocation){console.error("\u{1F7E2} TYPHOON MAP: No user location available for routing");return}let e=yield fetch(`https://api.mapbox.com/directions/v5/mapbox/${n}/${this.userLocation.lng},${this.userLocation.lat};${t.longitude},${t.latitude}?geometries=geojson&access_token=${y.mapboxAccessToken}`);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);let a=yield e.json();if(a.routes&&a.routes.length>0){let i={type:"Feature",geometry:a.routes[0].geometry,properties:{}};r.geoJSON(i,{style:{color:"#008000",weight:4,opacity:.8}}).addTo(this.map),console.log(`\u{1F7E2} TYPHOON MAP: Route added to ${t.name}`)}}catch(e){console.error("\u{1F7E2} TYPHOON MAP: Error calculating route:",e)}})}showOfflineMarkerInfo(t,n){return c(this,null,function*(){yield(yield this.alertCtrl.create({header:`\u{1F4F1} ${t.name}`,message:`
        <div style="text-align: left;">
          <p><strong>Type:</strong> Typhoon Center</p>
          <p><strong>Distance:</strong> ${(n/1e3).toFixed(2)} km</p>
          <p><strong>Address:</strong> ${t.address||"N/A"}</p>
          <p><strong>Capacity:</strong> ${t.capacity||"N/A"}</p>
          <p><strong>Status:</strong> ${t.status||"N/A"}</p>
          <br>
          <p><em>\u{1F4F1} Offline Mode: Routing not available. Use external navigation apps for directions.</em></p>
        </div>
      `,buttons:[{text:"Open in Maps",handler:()=>{this.openInExternalMaps(t)}},{text:"Close",role:"cancel"}]})).present()})}openInExternalMaps(t){return c(this,null,function*(){let n=Number(t.latitude),e=Number(t.longitude),a=`https://www.google.com/maps/dir/?api=1&destination=${n},${e}&travelmode=walking`;try{window.open(a,"_system")}catch(o){console.error("Error opening external maps:",o),yield(yield this.toastCtrl.create({message:"Could not open external maps app",duration:3e3,color:"danger"})).present()}})}showTransportationOptions(t){return c(this,null,function*(){var a,o;if(this.offlineStorage.isOfflineMode()||!navigator.onLine){let i=this.calculateDistance(((a=this.userLocation)==null?void 0:a.lat)||0,((o=this.userLocation)==null?void 0:o.lng)||0,Number(t.latitude),Number(t.longitude));yield this.showOfflineMarkerInfo(t,i);return}yield(yield this.alertCtrl.create({header:`Route to ${t.name}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToCenter(t,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToCenter(t,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToCenter(t,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToCenter(t,n){return c(this,null,function*(){if(!this.userLocation)return;if(this.offlineStorage.isOfflineMode()||!navigator.onLine){console.log("\u{1F7E2} Offline mode: Cannot calculate routes"),yield(yield this.toastCtrl.create({message:"\u{1F4F1} Offline mode: Routing not available. Use external navigation apps.",duration:4e3,color:"warning"})).present(),yield this.openInExternalMaps(t);return}try{this.clearRoutes();let a="walking";switch(n){case"walking":a="walking";break;case"cycling":a="cycling";break;case"driving":a="driving";break}let o=yield fetch(`https://api.mapbox.com/directions/v5/mapbox/${a}/${this.userLocation.lng},${this.userLocation.lat};${t.longitude},${t.latitude}?geometries=geojson&access_token=${y.mapboxAccessToken}`);if(o.ok){let i=yield o.json();if(i&&i.routes&&i.routes.length>0){let s=i.routes[0],u=r.polyline(s.geometry.coordinates.map(f=>[f[1],f[0]]),{color:"#008000",weight:5,opacity:.8});u.addTo(this.map),yield(yield this.toastCtrl.create({message:`\u{1F7E2} Route: ${(s.distance/1e3).toFixed(2)}km, ${(s.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"success"})).present(),this.map.fitBounds(u.getBounds(),{padding:[50,50]})}}}catch(a){console.error("\u{1F7E2} TYPHOON MAP: Error calculating individual route:",a),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,n,e,a){let i=t*Math.PI/180,s=e*Math.PI/180,p=(e-t)*Math.PI/180,u=(a-n)*Math.PI/180,h=Math.sin(p/2)*Math.sin(p/2)+Math.cos(i)*Math.cos(s)*Math.sin(u/2)*Math.sin(u/2);return 6371e3*(2*Math.atan2(Math.sqrt(h),Math.sqrt(1-h)))}goBack(){this.router.navigate(["/tabs/home"])}ionViewWillLeave(){this.map&&this.map.remove()}};d.\u0275fac=function(n){return new(n||d)},d.\u0275cmp=b({type:d,selectors:[["app-typhoon-map"]],decls:18,vars:3,consts:[[3,"translucent"],["color","success"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[3,"fullscreen"],["id","typhoon-map",2,"height","100%","width","100%"],[1,"floating-info"],[1,"info-row"],["name","thunderstorm","color","success"],[1,"info-text"]],template:function(n,e){n&1&&(g(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),x("click",function(){return e.goBack()}),C(4,"ion-icon",4),m()(),g(5,"ion-title"),w(6,"\u{1F7E2} Typhoon Evacuation Centers"),m()()(),g(7,"ion-content",5),C(8,"div",6),g(9,"div",7)(10,"ion-card")(11,"ion-card-content")(12,"div",8),C(13,"ion-icon",9),g(14,"span"),w(15),m()(),g(16,"div",10),w(17," Showing evacuation centers specifically for typhoon disasters "),m()()()()()),n&2&&(P("translucent",!0),M(7),P("fullscreen",!0),M(8),T("Typhoon Centers: ",e.evacuationCenters.length,""))},dependencies:[U,$,I,_,E,S,H,R,Y,D,N],styles:["#typhoon-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-success);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-success);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-success);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}"]});let O=d;return O})();export{lt as TyphoonMapPage};
